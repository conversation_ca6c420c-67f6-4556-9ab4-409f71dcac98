import { NextResponse } from 'next/server';
import { NotificationService } from '@/services/NotificationService';
import { ProcessarNotificacaoData } from '@/types/notifications';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { tipo, destinatario_id, contexto, canal, agendamento_id, empresa_id }: ProcessarNotificacaoData = body;

    // Validações básicas
    if (!tipo || !destinatario_id || !contexto) {
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios: tipo, destinatario_id, contexto'
      }, { status: 400 });
    }

    console.log(`📧 API: Processando notificação ${tipo} para usuário ${destinatario_id}`);

    const notificationService = new NotificationService();
    
    const resultado = await notificationService.processarNotificacao({
      tipo,
      destinatario_id,
      contexto,
      canal: canal || 'email',
      agendamento_id,
      empresa_id
    });

    if (resultado.success) {
      return NextResponse.json({
        success: true,
        message: 'Notificação processada com sucesso',
        notificacao_id: resultado.notificacao_id
      });
    } else {
      return NextResponse.json({
        success: false,
        error: resultado.error
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Erro na API de notificações:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    const notificationService = new NotificationService();

    if (action === 'pendentes') {
      const limite = parseInt(searchParams.get('limite') || '10');
      const notificacoesPendentes = await notificationService.buscarNotificacoesPendentes(limite);
      
      return NextResponse.json({
        success: true,
        data: notificacoesPendentes
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Ação não especificada ou inválida'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Erro ao buscar notificações:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}
