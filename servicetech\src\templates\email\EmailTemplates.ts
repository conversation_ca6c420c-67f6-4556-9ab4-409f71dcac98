import { EmailTemplate, ContextoAgendamento, ContextoProprietario } from '@/types/notifications';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

export class EmailTemplates {
  
  /**
   * Template base para emails
   */
  private baseTemplate(title: string, content: string): string {
    return `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
        .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; padding: 20px 0; border-bottom: 2px solid #3b82f6; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #3b82f6; }
        .content { padding: 20px 0; }
        .highlight { background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0; }
        .button { display: inline-block; padding: 12px 24px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .button:hover { background-color: #2563eb; }
        .footer { text-align: center; padding: 20px 0; border-top: 1px solid #e5e7eb; margin-top: 30px; color: #6b7280; font-size: 14px; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 15px 0; }
        .info-item { padding: 8px; background-color: #f9fafb; border-radius: 4px; }
        .info-label { font-weight: bold; color: #374151; }
        .info-value { color: #6b7280; }
        @media (max-width: 600px) { .info-grid { grid-template-columns: 1fr; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">ServiceTech</div>
            <p style="margin: 5px 0; color: #6b7280;">Plataforma de Agendamento de Serviços</p>
        </div>
        <div class="content">
            ${content}
        </div>
        <div class="footer">
            <p>Este é um email automático, não responda a esta mensagem.</p>
            <p>© 2025 ServiceTech. Todos os direitos reservados.</p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Formatar data para exibição
   */
  private formatarData(dataString: string): string {
    const data = new Date(dataString);
    return format(data, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  }

  /**
   * Formatar valor monetário
   */
  private formatarValor(valor: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  }

  /**
   * Template: Novo agendamento para cliente
   */
  novoAgendamentoCliente(contexto: ContextoAgendamento): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);
    const valorFormatado = this.formatarValor(contexto.valor_total);

    const content = `
      <h2 style="color: #3b82f6; margin-bottom: 20px;">Agendamento Solicitado com Sucesso! 🎉</h2>
      
      <p>Olá! Seu agendamento foi solicitado com sucesso e está aguardando confirmação do estabelecimento.</p>
      
      <div class="highlight">
        <h3 style="margin-top: 0; color: #1f2937;">Detalhes do Agendamento</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Estabelecimento:</div>
            <div class="info-value">${contexto.empresa_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Serviço:</div>
            <div class="info-value">${contexto.servico_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Profissional:</div>
            <div class="info-value">${contexto.colaborador_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Data e Horário:</div>
            <div class="info-value">${dataFormatada}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Valor:</div>
            <div class="info-value">${valorFormatado}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Forma de Pagamento:</div>
            <div class="info-value">${contexto.forma_pagamento}</div>
          </div>
        </div>
        
        <p style="margin: 15px 0 5px 0;"><strong>Código de Confirmação:</strong> <span style="font-size: 18px; color: #3b82f6; font-weight: bold;">${contexto.codigo_confirmacao}</span></p>
      </div>

      ${contexto.observacoes_cliente ? `
        <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;">
          <strong>Suas observações:</strong><br>
          ${contexto.observacoes_cliente}
        </div>
      ` : ''}

      <p><strong>⏰ Importante:</strong> O estabelecimento tem até <strong>24 horas</strong> para confirmar seu agendamento. Você receberá uma notificação assim que houver uma resposta.</p>
      
      <p>Endereço: ${contexto.empresa_endereco}</p>
    `;

    return {
      subject: `Agendamento Solicitado - ${contexto.servico_nome} em ${contexto.empresa_nome}`,
      html: this.baseTemplate('Agendamento Solicitado', content),
      text: `Agendamento solicitado com sucesso!\n\nServiço: ${contexto.servico_nome}\nEstabelecimento: ${contexto.empresa_nome}\nData: ${dataFormatada}\nValor: ${valorFormatado}\nCódigo: ${contexto.codigo_confirmacao}`
    };
  }

  /**
   * Template: Agendamento confirmado
   */
  agendamentoConfirmado(contexto: ContextoAgendamento): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);
    const valorFormatado = this.formatarValor(contexto.valor_total);

    const content = `
      <h2 style="color: #10b981; margin-bottom: 20px;">Agendamento Confirmado! ✅</h2>
      
      <p>Ótima notícia! Seu agendamento foi <strong>confirmado</strong> pelo estabelecimento.</p>
      
      <div class="highlight">
        <h3 style="margin-top: 0; color: #1f2937;">Detalhes Confirmados</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Estabelecimento:</div>
            <div class="info-value">${contexto.empresa_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Serviço:</div>
            <div class="info-value">${contexto.servico_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Profissional:</div>
            <div class="info-value">${contexto.colaborador_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Data e Horário:</div>
            <div class="info-value">${dataFormatada}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Valor:</div>
            <div class="info-value">${valorFormatado}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Código:</div>
            <div class="info-value">${contexto.codigo_confirmacao}</div>
          </div>
        </div>
      </div>

      <p><strong>📍 Endereço:</strong> ${contexto.empresa_endereco}</p>
      
      <p><strong>💡 Dica:</strong> Chegue alguns minutos antes do horário agendado. Em caso de imprevistos, entre em contato diretamente com o estabelecimento.</p>
      
      <p>Aguardamos você! 😊</p>
    `;

    return {
      subject: `✅ Agendamento Confirmado - ${contexto.servico_nome}`,
      html: this.baseTemplate('Agendamento Confirmado', content),
      text: `Agendamento confirmado!\n\nServiço: ${contexto.servico_nome}\nEstabelecimento: ${contexto.empresa_nome}\nData: ${dataFormatada}\nEndereço: ${contexto.empresa_endereco}`
    };
  }

  /**
   * Template: Agendamento recusado
   */
  agendamentoRecusado(contexto: ContextoAgendamento): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);

    const content = `
      <h2 style="color: #ef4444; margin-bottom: 20px;">Agendamento Não Confirmado 😔</h2>
      
      <p>Infelizmente, o estabelecimento não pôde confirmar seu agendamento para o horário solicitado.</p>
      
      <div style="background-color: #fef2f2; padding: 15px; border-radius: 6px; border-left: 4px solid #ef4444; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #1f2937;">Agendamento Recusado</h3>
        <p><strong>Serviço:</strong> ${contexto.servico_nome}</p>
        <p><strong>Data/Horário:</strong> ${dataFormatada}</p>
        <p><strong>Estabelecimento:</strong> ${contexto.empresa_nome}</p>
        <p><strong>Código:</strong> ${contexto.codigo_confirmacao}</p>
      </div>

      ${contexto.forma_pagamento === 'Online' ? `
        <div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;">
          <p><strong>💳 Reembolso:</strong> Como você pagou online, o reembolso será processado automaticamente em até 5 dias úteis.</p>
        </div>
      ` : ''}

      <p><strong>🔄 Não desista!</strong> Tente agendar para outro horário ou explore outros estabelecimentos disponíveis em nossa plataforma.</p>
      
      <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/buscar" class="button">Buscar Outros Horários</a>
    `;

    return {
      subject: `❌ Agendamento Não Confirmado - ${contexto.servico_nome}`,
      html: this.baseTemplate('Agendamento Não Confirmado', content),
      text: `Agendamento não confirmado.\n\nServiço: ${contexto.servico_nome}\nData: ${dataFormatada}\nEstabelecimento: ${contexto.empresa_nome}\n\nTente agendar para outro horário.`
    };
  }

  /**
   * Template: Agendamento cancelado
   */
  agendamentoCancelado(contexto: ContextoAgendamento): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);

    const content = `
      <h2 style="color: #f59e0b; margin-bottom: 20px;">Agendamento Cancelado ⚠️</h2>
      
      <p>Seu agendamento foi cancelado conforme solicitado.</p>
      
      <div style="background-color: #fffbeb; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #1f2937;">Detalhes do Cancelamento</h3>
        <p><strong>Serviço:</strong> ${contexto.servico_nome}</p>
        <p><strong>Data/Horário:</strong> ${dataFormatada}</p>
        <p><strong>Estabelecimento:</strong> ${contexto.empresa_nome}</p>
        <p><strong>Código:</strong> ${contexto.codigo_confirmacao}</p>
      </div>

      ${contexto.forma_pagamento === 'Online' ? `
        <div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;">
          <p><strong>💳 Reembolso:</strong> O reembolso será processado de acordo com a política de cancelamento do estabelecimento.</p>
        </div>
      ` : ''}

      <p>Esperamos vê-lo novamente em breve! 😊</p>
      
      <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/buscar" class="button">Fazer Novo Agendamento</a>
    `;

    return {
      subject: `🚫 Agendamento Cancelado - ${contexto.servico_nome}`,
      html: this.baseTemplate('Agendamento Cancelado', content),
      text: `Agendamento cancelado.\n\nServiço: ${contexto.servico_nome}\nData: ${dataFormatada}\nEstabelecimento: ${contexto.empresa_nome}`
    };
  }

  /**
   * Template: Lembrete de confirmação para proprietário/colaborador
   */
  lembreteConfirmacao(contexto: ContextoProprietario): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);
    const prazoFormatado = this.formatarData(contexto.prazo_confirmacao);
    const valorFormatado = this.formatarValor(contexto.valor_total);

    const content = `
      <h2 style="color: #f59e0b; margin-bottom: 20px;">⏰ Lembrete: Agendamento Pendente</h2>
      
      <p>Você tem um agendamento aguardando confirmação que expira em breve!</p>
      
      <div style="background-color: #fffbeb; padding: 15px; border-radius: 6px; border-left: 4px solid #f59e0b; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #1f2937;">Detalhes do Agendamento</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Cliente:</div>
            <div class="info-value">${contexto.cliente_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Serviço:</div>
            <div class="info-value">${contexto.servico_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Profissional:</div>
            <div class="info-value">${contexto.colaborador_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Data e Horário:</div>
            <div class="info-value">${dataFormatada}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Valor:</div>
            <div class="info-value">${valorFormatado}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Prazo Limite:</div>
            <div class="info-value">${prazoFormatado}</div>
          </div>
        </div>
        
        <p style="margin: 15px 0 5px 0;"><strong>Código:</strong> <span style="font-size: 18px; color: #f59e0b; font-weight: bold;">${contexto.codigo_confirmacao}</span></p>
      </div>

      ${contexto.observacoes_cliente ? `
        <div style="background-color: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <strong>Observações do cliente:</strong><br>
          ${contexto.observacoes_cliente}
        </div>
      ` : ''}

      <p><strong>⚠️ Ação Necessária:</strong> Confirme ou recuse este agendamento antes do prazo limite para evitar cancelamento automático.</p>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/proprietario/agendamentos" class="button" style="background-color: #10b981;">✅ Confirmar</a>
        <a href="${process.env.NEXT_PUBLIC_APP_URL || 'https://servicetech.com.br'}/proprietario/agendamentos" class="button" style="background-color: #ef4444;">❌ Recusar</a>
      </div>
    `;

    return {
      subject: `⏰ Lembrete: Agendamento de ${contexto.cliente_nome} expira em breve`,
      html: this.baseTemplate('Lembrete de Confirmação', content),
      text: `Lembrete: Agendamento pendente!\n\nCliente: ${contexto.cliente_nome}\nServiço: ${contexto.servico_nome}\nData: ${dataFormatada}\nPrazo: ${prazoFormatado}\n\nConfirme ou recuse antes do prazo limite.`
    };
  }

  /**
   * Template: Lembrete de agendamento para cliente
   */
  lembreteAgendamento(contexto: ContextoAgendamento): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);
    const valorFormatado = this.formatarValor(contexto.valor_total);

    const content = `
      <h2 style="color: #3b82f6; margin-bottom: 20px;">🔔 Lembrete: Seu Agendamento é Amanhã!</h2>
      
      <p>Este é um lembrete amigável sobre seu agendamento confirmado para amanhã.</p>
      
      <div class="highlight">
        <h3 style="margin-top: 0; color: #1f2937;">Detalhes do Agendamento</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Estabelecimento:</div>
            <div class="info-value">${contexto.empresa_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Serviço:</div>
            <div class="info-value">${contexto.servico_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Profissional:</div>
            <div class="info-value">${contexto.colaborador_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Data e Horário:</div>
            <div class="info-value">${dataFormatada}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Valor:</div>
            <div class="info-value">${valorFormatado}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Código:</div>
            <div class="info-value">${contexto.codigo_confirmacao}</div>
          </div>
        </div>
      </div>

      <p><strong>📍 Endereço:</strong> ${contexto.empresa_endereco}</p>
      
      <div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;">
        <p><strong>💡 Dicas importantes:</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>Chegue 10 minutos antes do horário</li>
          <li>Traga um documento de identificação</li>
          <li>Em caso de imprevistos, entre em contato com o estabelecimento</li>
        </ul>
      </div>

      <p>Estamos ansiosos para atendê-lo! 😊</p>
    `;

    return {
      subject: `🔔 Lembrete: ${contexto.servico_nome} amanhã às ${format(new Date(contexto.data_hora_inicio), 'HH:mm')}`,
      html: this.baseTemplate('Lembrete de Agendamento', content),
      text: `Lembrete: Seu agendamento é amanhã!\n\nServiço: ${contexto.servico_nome}\nEstabelecimento: ${contexto.empresa_nome}\nData: ${dataFormatada}\nEndereço: ${contexto.empresa_endereco}`
    };
  }

  /**
   * Template: Pagamento confirmado
   */
  pagamentoConfirmado(contexto: ContextoAgendamento): EmailTemplate {
    const dataFormatada = this.formatarData(contexto.data_hora_inicio);
    const valorFormatado = this.formatarValor(contexto.valor_total);

    const content = `
      <h2 style="color: #10b981; margin-bottom: 20px;">💳 Pagamento Confirmado! ✅</h2>

      <p>Excelente! Seu pagamento foi processado com sucesso e seu agendamento está confirmado.</p>

      <div style="background-color: #f0fdf4; padding: 15px; border-radius: 6px; border-left: 4px solid #10b981; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #1f2937;">💰 Comprovante de Pagamento</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Valor Pago:</div>
            <div class="info-value" style="color: #10b981; font-weight: bold;">${valorFormatado}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Forma de Pagamento:</div>
            <div class="info-value">Online</div>
          </div>
          <div class="info-item">
            <div class="info-label">Status:</div>
            <div class="info-value" style="color: #10b981; font-weight: bold;">✅ Pago</div>
          </div>
          <div class="info-item">
            <div class="info-label">Data do Pagamento:</div>
            <div class="info-value">${format(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}</div>
          </div>
        </div>
      </div>

      <div class="highlight">
        <h3 style="margin-top: 0; color: #1f2937;">📅 Detalhes do Agendamento</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">Estabelecimento:</div>
            <div class="info-value">${contexto.empresa_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Serviço:</div>
            <div class="info-value">${contexto.servico_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Profissional:</div>
            <div class="info-value">${contexto.colaborador_nome}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Data e Horário:</div>
            <div class="info-value">${dataFormatada}</div>
          </div>
          <div class="info-item">
            <div class="info-label">Código:</div>
            <div class="info-value">${contexto.codigo_confirmacao}</div>
          </div>
        </div>
      </div>

      <p><strong>📍 Endereço:</strong> ${contexto.empresa_endereco}</p>

      <div style="background-color: #f0f9ff; padding: 15px; border-radius: 6px; border-left: 4px solid #3b82f6; margin: 20px 0;">
        <p><strong>🎯 Próximos passos:</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px;">
          <li>Seu agendamento está confirmado e pago</li>
          <li>Chegue 10 minutos antes do horário</li>
          <li>Apresente o código de confirmação no local</li>
          <li>Guarde este email como comprovante</li>
        </ul>
      </div>

      <p>Obrigado por escolher nossos serviços! 😊</p>
    `;

    return {
      subject: `💳 Pagamento Confirmado - ${contexto.servico_nome} em ${contexto.empresa_nome}`,
      html: this.baseTemplate('Pagamento Confirmado', content),
      text: `Pagamento confirmado!\n\nValor: ${valorFormatado}\nServiço: ${contexto.servico_nome}\nEstabelecimento: ${contexto.empresa_nome}\nData: ${dataFormatada}\nCódigo: ${contexto.codigo_confirmacao}`
    };
  }
}
