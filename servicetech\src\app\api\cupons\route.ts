import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { CriarCupomData, FiltrosCupons } from '@/types/marketing';

// GET - Listar cupons da empresa
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, plano_saas_id, planos_saas(nome_plano)')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se é plano Premium
    const planoSaas = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Módulo de marketing disponível apenas no plano Premium' 
      }, { status: 403 });
    }

    // Extrair filtros
    const filtros: FiltrosCupons = {
      busca: searchParams.get('busca') || undefined,
      status: (searchParams.get('status') as any) || 'todos',
      tipo_desconto: (searchParams.get('tipo_desconto') as any) || 'todos',
      data_inicio: searchParams.get('data_inicio') || undefined,
      data_fim: searchParams.get('data_fim') || undefined,
    };

    const pagina = parseInt(searchParams.get('pagina') || '1');
    const limite = parseInt(searchParams.get('limite') || '10');
    const offset = (pagina - 1) * limite;

    // Construir query
    let query = supabase
      .from('cupons')
      .select('*', { count: 'exact' })
      .eq('empresa_id', empresa.empresa_id)
      .order('created_at', { ascending: false });

    // Aplicar filtros
    if (filtros.busca) {
      query = query.or(`nome_cupom.ilike.%${filtros.busca}%,codigo_cupom.ilike.%${filtros.busca}%`);
    }

    if (filtros.status && filtros.status !== 'todos') {
      const agora = new Date().toISOString();
      switch (filtros.status) {
        case 'ativos':
          query = query.eq('ativo', true).gte('data_fim', agora);
          break;
        case 'inativos':
          query = query.eq('ativo', false);
          break;
        case 'expirados':
          query = query.lt('data_fim', agora);
          break;
      }
    }

    if (filtros.tipo_desconto && filtros.tipo_desconto !== 'todos') {
      query = query.eq('tipo_desconto', filtros.tipo_desconto);
    }

    if (filtros.data_inicio) {
      query = query.gte('data_inicio', filtros.data_inicio);
    }

    if (filtros.data_fim) {
      query = query.lte('data_fim', filtros.data_fim);
    }

    // Executar query com paginação
    const { data: cupons, error: cuponsError, count } = await query
      .range(offset, offset + limite - 1);

    if (cuponsError) {
      console.error('Erro ao buscar cupons:', cuponsError);
      return NextResponse.json({ success: false, error: 'Erro ao buscar cupons' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        cupons: cupons || [],
        total: count || 0,
        pagina,
        total_paginas: Math.ceil((count || 0) / limite)
      }
    });

  } catch (error) {
    console.error('Erro na API de cupons:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// POST - Criar novo cupom
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const body: CriarCupomData = await request.json();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ success: false, error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, plano_saas_id, planos_saas(nome_plano)')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json({ success: false, error: 'Empresa não encontrada' }, { status: 404 });
    }

    // Verificar se é plano Premium
    const planoSaas2 = Array.isArray(empresa.planos_saas)
      ? empresa.planos_saas[0]
      : empresa.planos_saas;
    const planoNome = planoSaas2?.nome_plano;
    
    if (planoNome?.toLowerCase() !== 'premium') {
      return NextResponse.json({ 
        success: false, 
        error: 'Módulo de marketing disponível apenas no plano Premium' 
      }, { status: 403 });
    }

    // Validações
    const erros = validarDadosCupom(body);
    if (Object.keys(erros).length > 0) {
      return NextResponse.json({ success: false, error: 'Dados inválidos', erros }, { status: 400 });
    }

    // Verificar se código do cupom já existe
    const { data: cupomExistente } = await supabase
      .from('cupons')
      .select('cupom_id')
      .eq('codigo_cupom', body.codigo_cupom)
      .single();

    if (cupomExistente) {
      return NextResponse.json({ 
        success: false, 
        error: 'Código do cupom já existe. Escolha outro código.' 
      }, { status: 400 });
    }

    // Criar cupom
    const { data: cupom, error: cupomError } = await supabase
      .from('cupons')
      .insert([{
        empresa_id: empresa.empresa_id,
        ...body,
        ativo: body.ativo ?? true
      }])
      .select()
      .single();

    if (cupomError) {
      console.error('Erro ao criar cupom:', cupomError);
      return NextResponse.json({ success: false, error: 'Erro ao criar cupom' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: cupom,
      message: 'Cupom criado com sucesso'
    });

  } catch (error) {
    console.error('Erro na criação de cupom:', error);
    return NextResponse.json({ success: false, error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// Função de validação
function validarDadosCupom(dados: CriarCupomData): Record<string, string> {
  const erros: Record<string, string> = {};

  if (!dados.codigo_cupom || dados.codigo_cupom.length < 3) {
    erros.codigo_cupom = 'Código deve ter pelo menos 3 caracteres';
  }

  if (!dados.nome_cupom || dados.nome_cupom.length < 3) {
    erros.nome_cupom = 'Nome deve ter pelo menos 3 caracteres';
  }

  if (!dados.descricao || dados.descricao.length < 10) {
    erros.descricao = 'Descrição deve ter pelo menos 10 caracteres';
  }

  if (!dados.tipo_desconto || !['valor_fixo', 'percentual'].includes(dados.tipo_desconto)) {
    erros.tipo_desconto = 'Tipo de desconto inválido';
  }

  if (!dados.valor_desconto || dados.valor_desconto <= 0) {
    erros.valor_desconto = 'Valor do desconto deve ser maior que zero';
  }

  if (dados.tipo_desconto === 'percentual' && dados.valor_desconto > 90) {
    erros.valor_desconto = 'Desconto percentual não pode ser maior que 90%';
  }

  if (dados.valor_minimo_pedido && dados.valor_minimo_pedido < 0) {
    erros.valor_minimo_pedido = 'Valor mínimo não pode ser negativo';
  }

  if (dados.tipo_desconto === 'valor_fixo' && dados.valor_minimo_pedido && dados.valor_desconto >= dados.valor_minimo_pedido) {
    erros.valor_minimo_pedido = 'Valor mínimo deve ser maior que o desconto';
  }

  if (!dados.data_inicio) {
    erros.data_inicio = 'Data de início é obrigatória';
  }

  if (!dados.data_fim) {
    erros.data_fim = 'Data de fim é obrigatória';
  }

  if (dados.data_inicio && dados.data_fim && new Date(dados.data_fim) <= new Date(dados.data_inicio)) {
    erros.data_fim = 'Data de fim deve ser posterior à data de início';
  }

  if (dados.limite_usos_total && dados.limite_usos_total <= 0) {
    erros.limite_usos_total = 'Limite de usos deve ser maior que zero';
  }

  if (dados.limite_usos_por_cliente && dados.limite_usos_por_cliente <= 0) {
    erros.limite_usos_por_cliente = 'Limite por cliente deve ser maior que zero';
  }

  return erros;
}
