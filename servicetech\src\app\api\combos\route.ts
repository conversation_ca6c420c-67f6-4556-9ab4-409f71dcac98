import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { CriarComboData, FiltrosCombos, ComboApiResponse } from '@/types/combos';
import { validarCombo } from '@/utils/combos';

// GET - Buscar combos da empresa do usuário
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Extrair filtros da query string
    const { searchParams } = new URL(request.url);
    const filtros: FiltrosCombos = {
      ativo: searchParams.get('ativo') ? searchParams.get('ativo') === 'true' : undefined,
      busca: searchParams.get('busca') || undefined,
      data_inicio: searchParams.get('data_inicio') || undefined,
      data_fim: searchParams.get('data_fim') || undefined,
    };

    // Construir query
    let query = supabase
      .from('combos_servicos')
      .select(`
        *,
        combo_itens (
          *,
          servicos (
            servico_id,
            nome_servico,
            descricao,
            preco,
            duracao_minutos,
            categoria
          )
        )
      `)
      .eq('empresa_id', empresa.empresa_id);

    // Aplicar filtros
    if (filtros.ativo !== undefined) {
      query = query.eq('ativo', filtros.ativo);
    }

    if (filtros.busca) {
      query = query.or(`nome_combo.ilike.%${filtros.busca}%,descricao.ilike.%${filtros.busca}%`);
    }

    if (filtros.data_inicio) {
      query = query.gte('data_inicio', filtros.data_inicio);
    }

    if (filtros.data_fim) {
      query = query.lte('data_fim', filtros.data_fim);
    }

    const { data: combos, error: combosError } = await query.order('created_at', { ascending: false });

    if (combosError) {
      console.error('Erro ao buscar combos:', combosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar combos' },
        { status: 500 }
      );
    }

    // Transformar dados para incluir cálculos
    const combosCompletos = combos?.map(combo => {
      const itens = combo.combo_itens?.map((item: any) => ({
        ...item,
        servico: item.servicos
      })) || [];

      const valor_original = itens.reduce((total: number, item: any) => {
        return total + (item.servico.preco * item.quantidade);
      }, 0);

      let valor_com_desconto = valor_original;
      if (combo.desconto_valor_fixo && combo.desconto_valor_fixo > 0) {
        valor_com_desconto = Math.max(0, valor_original - combo.desconto_valor_fixo);
      } else if (combo.desconto_percentual && combo.desconto_percentual > 0) {
        const desconto = (valor_original * combo.desconto_percentual) / 100;
        valor_com_desconto = Math.max(0, valor_original - desconto);
      }

      const economia = valor_original - valor_com_desconto;

      return {
        ...combo,
        itens,
        valor_original,
        valor_com_desconto,
        economia
      };
    }) || [];

    const response: ComboApiResponse = {
      success: true,
      data: combosCompletos
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Erro na API de combos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Criar novo combo
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Extrair dados do corpo da requisição
    const dadosCombo: CriarComboData = await request.json();

    // Validar dados
    const validacao = validarCombo(dadosCombo);
    if (!validacao.valido) {
      return NextResponse.json(
        { success: false, error: validacao.erros.join(', ') },
        { status: 400 }
      );
    }

    // Verificar se os serviços existem e pertencem à empresa
    const servicosIds = dadosCombo.itens.map(item => item.servico_id);
    const { data: servicos, error: servicosError } = await supabase
      .from('servicos')
      .select('servico_id')
      .eq('empresa_id', empresa.empresa_id)
      .in('servico_id', servicosIds)
      .eq('ativo', true);

    if (servicosError || !servicos || servicos.length !== servicosIds.length) {
      return NextResponse.json(
        { success: false, error: 'Um ou mais serviços não foram encontrados' },
        { status: 400 }
      );
    }

    // Criar combo
    const { data: novoCombo, error: comboError } = await supabase
      .from('combos_servicos')
      .insert({
        empresa_id: empresa.empresa_id,
        nome_combo: dadosCombo.nome_combo,
        descricao: dadosCombo.descricao,
        desconto_valor_fixo: dadosCombo.desconto_valor_fixo || null,
        desconto_percentual: dadosCombo.desconto_percentual || null,
        ativo: dadosCombo.ativo ?? true,
        data_inicio: dadosCombo.data_inicio || null,
        data_fim: dadosCombo.data_fim || null,
        limite_usos: dadosCombo.limite_usos || null,
        usos_realizados: 0
      })
      .select()
      .single();

    if (comboError || !novoCombo) {
      console.error('Erro ao criar combo:', comboError);
      return NextResponse.json(
        { success: false, error: 'Erro ao criar combo' },
        { status: 500 }
      );
    }

    // Criar itens do combo
    const itensCombo = dadosCombo.itens.map(item => ({
      combo_id: novoCombo.combo_id,
      servico_id: item.servico_id,
      quantidade: item.quantidade,
      ordem_execucao: item.ordem_execucao,
      obrigatorio: item.obrigatorio
    }));

    const { error: itensError } = await supabase
      .from('combo_itens')
      .insert(itensCombo);

    if (itensError) {
      console.error('Erro ao criar itens do combo:', itensError);
      // Tentar remover o combo criado
      await supabase
        .from('combos_servicos')
        .delete()
        .eq('combo_id', novoCombo.combo_id);
      
      return NextResponse.json(
        { success: false, error: 'Erro ao criar itens do combo' },
        { status: 500 }
      );
    }

    const response: ComboApiResponse = {
      success: true,
      data: novoCombo,
      message: 'Combo criado com sucesso'
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Erro na API de combos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
