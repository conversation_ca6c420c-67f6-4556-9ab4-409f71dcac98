import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import Stripe from 'stripe';
import { ProcessarReembolsoData } from '@/types/agendamentos';

// Inicializar o cliente Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const resolvedParams = await params;
    const agendamentoId = parseInt(resolvedParams.id);

    if (isNaN(agendamentoId)) {
      return NextResponse.json(
        { success: false, error: 'ID do agendamento inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usu<PERSON>rio não autenticado' },
        { status: 401 }
      );
    }

    const body: ProcessarReembolsoData = await request.json();
    const { motivo, valor_reembolso } = body;

    // Buscar agendamento com dados completos
    const { data: agendamento, error: agendamentoError } = await supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        cliente_user_id,
        empresa_id,
        colaborador_user_id,
        valor_total,
        status_agendamento,
        status_pagamento,
        forma_pagamento,
        stripe_payment_intent_id,
        data_hora_inicio,
        servicos!inner (
          nome_servico
        ),
        empresas!inner (
          nome_empresa,
          proprietario_user_id
        )
      `)
      .eq('agendamento_id', agendamentoId)
      .single();

    if (agendamentoError || !agendamento) {
      return NextResponse.json(
        { success: false, error: 'Agendamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar permissões - cliente, proprietário ou colaborador podem solicitar reembolso
    const isCliente = agendamento.cliente_user_id === user.id;
    const empresa = Array.isArray(agendamento.empresas) ? agendamento.empresas[0] : agendamento.empresas;
    const isProprietario = empresa?.proprietario_user_id === user.id;
    const isColaborador = agendamento.colaborador_user_id === user.id;

    if (!isCliente && !isProprietario && !isColaborador) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado a processar reembolso para este agendamento' },
        { status: 403 }
      );
    }

    // Verificar se o agendamento pode ser reembolsado
    if (agendamento.forma_pagamento !== 'Online') {
      return NextResponse.json(
        { success: false, error: 'Este agendamento não foi pago online' },
        { status: 400 }
      );
    }

    if (agendamento.status_pagamento !== 'Pago') {
      return NextResponse.json(
        { success: false, error: 'Este agendamento não foi pago ou já foi reembolsado' },
        { status: 400 }
      );
    }

    if (!agendamento.stripe_payment_intent_id) {
      return NextResponse.json(
        { success: false, error: 'Payment Intent não encontrado para este agendamento' },
        { status: 400 }
      );
    }

    // Calcular valor do reembolso
    const valorReembolso = valor_reembolso || agendamento.valor_total;
    
    if (valorReembolso <= 0 || valorReembolso > agendamento.valor_total) {
      return NextResponse.json(
        { success: false, error: 'Valor de reembolso inválido' },
        { status: 400 }
      );
    }

    // Buscar o Payment Intent no Stripe
    let paymentIntent;
    try {
      paymentIntent = await stripe.paymentIntents.retrieve(agendamento.stripe_payment_intent_id);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Payment Intent não encontrado no Stripe' },
        { status: 400 }
      );
    }

    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json(
        { success: false, error: 'Payment Intent não foi bem-sucedido, não é possível reembolsar' },
        { status: 400 }
      );
    }

    // Criar reembolso no Stripe
    const refund = await stripe.refunds.create({
      payment_intent: agendamento.stripe_payment_intent_id,
      amount: Math.round(valorReembolso * 100), // Converter para centavos
      metadata: {
        agendamento_id: agendamentoId.toString(),
        motivo: motivo || 'Cancelamento de agendamento',
        processado_por: user.id,
        tipo: 'agendamento'
      },
      reason: 'requested_by_customer'
    });

    // Atualizar status do agendamento
    const { error: updateError } = await supabase
      .from('agendamentos')
      .update({
        status_pagamento: valorReembolso >= agendamento.valor_total ? 'Reembolsado' : 'Pago',
        status_agendamento: 'Cancelado',
        updated_at: new Date().toISOString()
      })
      .eq('agendamento_id', agendamentoId);

    if (updateError) {
      console.error('Erro ao atualizar status do agendamento:', updateError);
    }

    // Registrar o reembolso na tabela de pagamentos
    const { error: pagamentoError } = await supabase
      .from('pagamentos')
      .insert([{
        agendamento_id: agendamentoId,
        stripe_payment_intent_id: agendamento.stripe_payment_intent_id,
        stripe_charge_id: refund.id,
        valor: -valorReembolso, // Valor negativo para indicar reembolso
        status: 'Pago',
        metodo: 'stripe_refund',
        tipo_pagamento: 'Reembolso',
        data_pagamento: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }]);

    if (pagamentoError) {
      console.error('Erro ao registrar reembolso na tabela de pagamentos:', pagamentoError);
    }

    console.log('✅ Reembolso processado com sucesso:', {
      agendamento_id: agendamentoId,
      refund_id: refund.id,
      valor_reembolsado: valorReembolso,
      motivo: motivo
    });

    // Enviar notificações (não bloquear a resposta)
    try {
      const { notificarCancelamentoAgendamento } = await import('@/utils/notificationHelpers');
      notificarCancelamentoAgendamento(agendamentoId).catch(error => {
        console.error('❌ Erro ao enviar notificações de cancelamento:', error);
      });
    } catch (error) {
      console.error('❌ Erro ao importar helper de notificações:', error);
    }

    return NextResponse.json({
      success: true,
      data: {
        refund_id: refund.id,
        valor_reembolsado: valorReembolso,
        message: 'Reembolso processado com sucesso'
      }
    });

  } catch (error: any) {
    console.error('Erro ao processar reembolso:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
