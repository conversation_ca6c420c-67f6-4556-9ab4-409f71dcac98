/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/servicos/route";
exports.ids = ["app/api/servicos/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fservicos%2Froute&page=%2Fapi%2Fservicos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservicos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fservicos%2Froute&page=%2Fapi%2Fservicos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservicos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projetos_1_geremias_servicetech_src_app_api_servicos_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/servicos/route.ts */ \"(rsc)/./src/app/api/servicos/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/servicos/route\",\n        pathname: \"/api/servicos\",\n        filename: \"route\",\n        bundlePath: \"app/api/servicos/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\api\\\\servicos\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projetos_1_geremias_servicetech_src_app_api_servicos_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fservicos%2Froute&page=%2Fapi%2Fservicos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservicos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/servicos/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/servicos/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n/* harmony import */ var _types_servicos__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/servicos */ \"(rsc)/./src/types/servicos.ts\");\n\n\n\n// GET - Listar serviços da empresa do usuário\nasync function GET(request) {\n    try {\n        const supabase = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Verificar autenticação\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Usuário não autenticado'\n            }, {\n                status: 401\n            });\n        }\n        // Verificar se é proprietário\n        const userRole = user.user_metadata?.role;\n        if (userRole !== 'Proprietario') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Acesso negado. Apenas proprietários podem gerenciar serviços.'\n            }, {\n                status: 403\n            });\n        }\n        // Buscar empresa do usuário\n        const { data: empresa, error: empresaError } = await supabase.from('empresas').select('empresa_id, plano_saas_id').eq('proprietario_user_id', user.id).single();\n        if (empresaError || !empresa) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Empresa não encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Buscar parâmetros de filtro\n        const { searchParams } = new URL(request.url);\n        const ativo = searchParams.get('ativo');\n        const categoria = searchParams.get('categoria');\n        const busca = searchParams.get('busca');\n        // Construir query\n        let query = supabase.from('servicos').select('*').eq('empresa_id', empresa.empresa_id).order('created_at', {\n            ascending: false\n        });\n        // Aplicar filtros\n        if (ativo !== null) {\n            query = query.eq('ativo', ativo === 'true');\n        }\n        if (categoria) {\n            query = query.eq('categoria', categoria);\n        }\n        if (busca) {\n            query = query.or(`nome_servico.ilike.%${busca}%,descricao.ilike.%${busca}%`);\n        }\n        const { data: servicos, error: servicosError } = await query;\n        if (servicosError) {\n            console.error('Erro ao buscar serviços:', servicosError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao buscar serviços'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: servicos,\n            empresa_id: empresa.empresa_id\n        });\n    } catch (error) {\n        console.error('Erro geral na API de serviços:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Criar novo serviço\nasync function POST(request) {\n    try {\n        const supabase = await (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        // Verificar autenticação\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Usuário não autenticado'\n            }, {\n                status: 401\n            });\n        }\n        // Verificar se é proprietário\n        const userRole = user.user_metadata?.role;\n        if (userRole !== 'Proprietario') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Acesso negado. Apenas proprietários podem criar serviços.'\n            }, {\n                status: 403\n            });\n        }\n        // Buscar empresa e plano do usuário\n        const { data: empresa, error: empresaError } = await supabase.from('empresas').select(`\n        empresa_id,\n        planos_saas!inner(nome_plano)\n      `).eq('proprietario_user_id', user.id).single();\n        if (empresaError || !empresa) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Empresa não encontrada'\n            }, {\n                status: 404\n            });\n        }\n        // Verificar limite de serviços\n        const { data: servicosExistentes, error: countError } = await supabase.from('servicos').select('servico_id').eq('empresa_id', empresa.empresa_id).eq('ativo', true);\n        if (countError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao verificar limite de serviços'\n            }, {\n                status: 500\n            });\n        }\n        const plano = Array.isArray(empresa.planos_saas) ? empresa.planos_saas[0] : empresa.planos_saas;\n        const planoNome = plano?.nome_plano?.toLowerCase() || 'essencial';\n        const limite = planoNome === 'premium' ? _types_servicos__WEBPACK_IMPORTED_MODULE_2__.LIMITES_SERVICOS.premium : _types_servicos__WEBPACK_IMPORTED_MODULE_2__.LIMITES_SERVICOS.essencial;\n        if (servicosExistentes.length >= limite) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `Limite de ${limite} serviços atingido para o plano ${planoNome}. Considere fazer upgrade para o plano Premium.`\n            }, {\n                status: 400\n            });\n        }\n        // Validar dados do corpo da requisição\n        const body = await request.json();\n        // Validações básicas\n        if (!body.nome_servico || body.nome_servico.trim().length < 2) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Nome do serviço deve ter pelo menos 2 caracteres'\n            }, {\n                status: 400\n            });\n        }\n        if (!body.duracao_minutos || body.duracao_minutos < 15) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Duração deve ser de pelo menos 15 minutos'\n            }, {\n                status: 400\n            });\n        }\n        if (!body.preco || body.preco < 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Preço deve ser maior ou igual a zero'\n            }, {\n                status: 400\n            });\n        }\n        // Criar serviço\n        const { data: novoServico, error: criarError } = await supabase.from('servicos').insert([\n            {\n                empresa_id: empresa.empresa_id,\n                nome_servico: body.nome_servico.trim(),\n                descricao: body.descricao?.trim() || '',\n                duracao_minutos: body.duracao_minutos,\n                preco: body.preco,\n                categoria: body.categoria || 'Outros',\n                ativo: body.ativo !== false\n            }\n        ]).select().single();\n        if (criarError) {\n            console.error('Erro ao criar serviço:', criarError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao criar serviço'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: novoServico,\n            message: 'Serviço criado com sucesso'\n        });\n    } catch (error) {\n        console.error('Erro geral ao criar serviço:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/servicos/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/servicos.ts":
/*!*******************************!*\
  !*** ./src/types/servicos.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CATEGORIAS_SERVICOS: () => (/* binding */ CATEGORIAS_SERVICOS),\n/* harmony export */   LIMITES_SERVICOS: () => (/* binding */ LIMITES_SERVICOS)\n/* harmony export */ });\n// Tipos para o módulo de serviços\n// Interface principal do serviço (baseada no schema do banco)\n// Categorias predefinidas de serviços\nconst CATEGORIAS_SERVICOS = [\n    'Cabelo',\n    'Barba',\n    'Estética',\n    'Manicure/Pedicure',\n    'Massagem',\n    'Depilação',\n    'Sobrancelha',\n    'Maquiagem',\n    'Tratamentos Faciais',\n    'Outros'\n];\n// Limites por plano\nconst LIMITES_SERVICOS = {\n    essencial: 6,\n    premium: 12\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/servicos.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Cliente administrativo para operações que requerem service role\nfunction createAdminClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // No-op for admin client\n            }\n        },\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fservicos%2Froute&page=%2Fapi%2Fservicos%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fservicos%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();