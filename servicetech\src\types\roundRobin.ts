/**
 * Tipos TypeScript para o sistema de Round-Robin
 * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin
 */

export interface ColaboradorRoundRobin {
  colaborador_user_id: string;
  name: string;
  email?: string;
  total_agendamentos: number;
  ultimo_agendamento?: string; // ISO date string
}

export interface EstatisticasRoundRobin {
  total_colaboradores: number;
  total_agendamentos: number;
  distribuicao: {
    colaborador_user_id: string;
    name: string;
    count: number;
    percentual: number;
  }[];
  colaborador_menos_utilizado: string;
  colaborador_mais_utilizado: string;
  diferenca_maxima: number; // Diferença entre o mais e menos utilizado
}

export interface ParametrosRoundRobin {
  empresa_id: number;
  servico_id: number;
  colaboradores_disponiveis: ColaboradorRoundRobin[];
  data_hora_inicio: string;
  data_hora_fim: string;
}

export interface ResultadoRoundRobin {
  colaborador_selecionado: string;
  motivo_selecao: 'round_robin' | 'menos_agendamentos' | 'ultimo_disponivel';
  estatisticas_antes: EstatisticasRoundRobin;
  estatisticas_depois?: EstatisticasRoundRobin;
}

export interface ConfiguracaoRoundRobin {
  /**
   * Período em dias para considerar no cálculo de distribuição
   * Default: 30 dias
   */
  periodo_dias: number;
  
  /**
   * Se deve considerar apenas agendamentos confirmados
   * Default: false (considera pendentes e confirmados)
   */
  apenas_confirmados: boolean;
  
  /**
   * Peso para agendamentos mais recentes (0-1)
   * Default: 1 (todos os agendamentos têm peso igual)
   */
  peso_temporal: number;
  
  /**
   * Se deve priorizar colaboradores que não tiveram agendamentos recentes
   * Default: true
   */
  priorizar_inativos: boolean;
}
