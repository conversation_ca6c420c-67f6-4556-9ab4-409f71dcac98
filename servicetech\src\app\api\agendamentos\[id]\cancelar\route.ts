import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { CancelarAgendamentoData, CancelamentoResponse } from '@/types/politicas';
import { CancelamentoUtils } from '@/utils/cancelamento';
import { StatusAgendamento } from '@/types/agendamentos';
import stripe from '@/utils/stripe/server';

// POST - Cancelar agendamento
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient();
    const resolvedParams = await params;
    const agendamentoId = parseInt(resolvedParams.id);

    if (isNaN(agendamentoId)) {
      return NextResponse.json(
        { success: false, error: 'ID do agendamento inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Obter dados da requisição
    const body: CancelarAgendamentoData = await request.json();
    const { motivo, cancelado_por } = body;

    // Buscar agendamento com dados completos
    const { data: agendamento, error: agendamentoError } = await supabase
      .from('agendamentos')
      .select(`
        agendamento_id,
        cliente_user_id,
        empresa_id,
        colaborador_user_id,
        data_hora_inicio,
        status_agendamento,
        forma_pagamento,
        status_pagamento,
        valor_total,
        stripe_payment_intent_id,
        empresas!inner (
          nome_empresa,
          proprietario_user_id,
          politica_cancelamento
        ),
        servicos!inner (
          nome_servico
        )
      `)
      .eq('agendamento_id', agendamentoId)
      .single();

    if (agendamentoError || !agendamento) {
      return NextResponse.json(
        { success: false, error: 'Agendamento não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se agendamento já foi cancelado
    if (agendamento.status_agendamento === 'Cancelado') {
      return NextResponse.json(
        { success: false, error: 'Agendamento já foi cancelado' },
        { status: 400 }
      );
    }

    // Verificar permissões
    const userRole = user.user_metadata?.role;
    const isCliente = agendamento.cliente_user_id === user.id;
    const empresa = Array.isArray(agendamento.empresas) ? agendamento.empresas[0] : agendamento.empresas;
    const isProprietario = empresa?.proprietario_user_id === user.id;
    const isColaborador = agendamento.colaborador_user_id === user.id;
    const isAdmin = userRole === 'Administrador';

    const podeAcessar = isCliente || isProprietario || isColaborador || isAdmin;
    
    if (!podeAcessar) {
      return NextResponse.json(
        { success: false, error: 'Sem permissão para cancelar este agendamento' },
        { status: 403 }
      );
    }

    // Verificar se o tipo de cancelamento está correto
    if (cancelado_por === 'cliente' && !isCliente && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Apenas o cliente pode cancelar como cliente' },
        { status: 403 }
      );
    }

    if (cancelado_por === 'empresa' && !isProprietario && !isColaborador && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Apenas proprietário ou colaborador podem cancelar pela empresa' },
        { status: 403 }
      );
    }

    // Obter política de cancelamento da empresa
    const politica = CancelamentoUtils.mesclarComPadrao(
      empresa?.politica_cancelamento
    );

    // Calcular regras de cancelamento
    const regras = CancelamentoUtils.calcularRegrasCancelamento(
      agendamento.data_hora_inicio,
      agendamento.status_agendamento as StatusAgendamento,
      politica,
      cancelado_por
    );

    // Verificar se pode cancelar
    if (!regras.pode_cancelar) {
      return NextResponse.json(
        { 
          success: false, 
          error: regras.motivo_regra,
          details: {
            prazo_expirado: regras.prazo_expirado,
            horas_restantes: regras.horas_ate_agendamento
          }
        },
        { status: 400 }
      );
    }

    // Calcular reembolso
    const calculoReembolso = CancelamentoUtils.calcularReembolso(
      agendamento.valor_total,
      regras.percentual_reembolso,
      regras.motivo_regra
    );

    let reembolsoProcessado = false;

    // Processar reembolso se necessário
    if (
      agendamento.forma_pagamento === 'Online' &&
      agendamento.status_pagamento === 'Pago' &&
      agendamento.stripe_payment_intent_id &&
      calculoReembolso.pode_reembolsar
    ) {
      try {
        const refund = await stripe.refunds.create({
          payment_intent: agendamento.stripe_payment_intent_id,
          amount: Math.round(calculoReembolso.valor_reembolso * 100), // Converter para centavos
          metadata: {
            agendamento_id: agendamentoId.toString(),
            motivo: motivo ?? calculoReembolso.motivo,
            cancelado_por: cancelado_por,
            processado_por: user.id,
            tipo: 'cancelamento'
          },
          reason: 'requested_by_customer'
        });

        reembolsoProcessado = true;

        console.log('✅ Reembolso processado:', {
          agendamento_id: agendamentoId,
          refund_id: refund.id,
          valor_reembolsado: calculoReembolso.valor_reembolso
        });

      } catch (stripeError) {
        console.error('❌ Erro ao processar reembolso no Stripe:', stripeError);
        // Continuar com o cancelamento mesmo se o reembolso falhar
      }
    }

    // Atualizar status do agendamento
    const novoStatusPagamento = reembolsoProcessado 
      ? (calculoReembolso.valor_reembolso >= agendamento.valor_total ? 'Reembolsado' : 'Pago')
      : agendamento.status_pagamento;

    const { error: updateError } = await supabase
      .from('agendamentos')
      .update({
        status_agendamento: 'Cancelado',
        status_pagamento: novoStatusPagamento,
        updated_at: new Date().toISOString()
      })
      .eq('agendamento_id', agendamentoId);

    if (updateError) {
      console.error('Erro ao atualizar agendamento:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao cancelar agendamento' },
        { status: 500 }
      );
    }

    console.log('✅ Agendamento cancelado:', {
      agendamento_id: agendamentoId,
      cancelado_por: cancelado_por,
      usuario: user.id,
      reembolso_processado: reembolsoProcessado,
      valor_reembolsado: calculoReembolso.valor_reembolso
    });

    // Enviar notificações (não bloquear a resposta)
    try {
      const { notificarCancelamentoAgendamento } = await import('@/utils/notificationHelpers');
      notificarCancelamentoAgendamento(agendamentoId).catch(error => {
        console.error('❌ Erro ao enviar notificações de cancelamento:', error);
      });
    } catch (error) {
      console.error('❌ Erro ao importar helper de notificações:', error);
    }

    const response: CancelamentoResponse = {
      success: true,
      agendamento_cancelado: true,
      reembolso_processado: reembolsoProcessado,
      valor_reembolsado: calculoReembolso.valor_reembolso,
      percentual_aplicado: regras.percentual_reembolso,
      motivo_reembolso: calculoReembolso.motivo,
      message: `Agendamento cancelado com sucesso. ${
        reembolsoProcessado 
          ? `Reembolso de R$ ${calculoReembolso.valor_reembolso.toFixed(2)} processado.`
          : calculoReembolso.pode_reembolsar 
            ? 'Reembolso será processado em breve.'
            : 'Sem reembolso conforme política de cancelamento.'
      }`
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Erro ao cancelar agendamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
