// Tipos para o módulo de busca de empresas

// Interface para filtros de busca
export interface FiltrosBusca {
  termo?: string;
  cidade?: string;
  estado?: string;
  bairro?: string;
  categorias_servicos?: string[];
  preco_minimo?: number;
  preco_maximo?: number;
  ordenacao?: TipoOrdenacao;
  pagina?: number;
  limite?: number;
}

// Tipos de ordenação disponíveis
export type TipoOrdenacao = 
  | 'relevancia'
  | 'preco_menor'
  | 'preco_maior'
  | 'nome_az'
  | 'nome_za'
  | 'mais_servicos'
  | 'avaliacao'; // Para futuro

// Interface para empresa nos resultados de busca
export interface EmpresaBusca {
  empresa_id: number;
  nome_empresa: string;
  logo_url: string | null;
  endereco: string;
  numero: string;
  bairro: string;
  cidade: string;
  estado: string;
  descricao: string | null;
  slug: string;
  segmento: string | null;
  // Dados agregados dos serviços
  total_servicos: number;
  preco_minimo: number;
  preco_maximo: number;
  categorias_servicos: string[];
  // Dados de localização formatados
  endereco_completo: string;
  // Futura implementação de avaliações
  avaliacao_media?: number;
  total_avaliacoes?: number;
}

// Interface para resultados paginados
export interface ResultadoBusca {
  empresas: EmpresaBusca[];
  total: number;
  pagina: number;
  limite: number;
  total_paginas: number;
  tem_proxima_pagina: boolean;
  tem_pagina_anterior: boolean;
}

// Interface para resposta da API
export interface BuscaApiResponse {
  success: boolean;
  data?: ResultadoBusca;
  error?: string;
  message?: string;
}

// Interface para estatísticas de busca
export interface EstatisticasBusca {
  total_empresas: number;
  cidades_disponiveis: string[];
  estados_disponiveis: string[];
  categorias_disponiveis: string[];
  faixa_precos: {
    minimo: number;
    maximo: number;
  };
}

// Interface para sugestões de busca
export interface SugestoesBusca {
  empresas: Array<{
    empresa_id: number;
    nome_empresa: string;
    cidade: string;
    estado: string;
  }>;
  cidades: string[];
  categorias: string[];
}

// Constantes para filtros
export const OPCOES_ORDENACAO = [
  { value: 'relevancia', label: 'Mais Relevantes' },
  { value: 'preco_menor', label: 'Menor Preço' },
  { value: 'preco_maior', label: 'Maior Preço' },
  { value: 'nome_az', label: 'Nome A-Z' },
  { value: 'nome_za', label: 'Nome Z-A' },
  { value: 'mais_servicos', label: 'Mais Serviços' },
  { value: 'avaliacao', label: 'Melhor Avaliação' }
] as const;

export const LIMITE_PADRAO = 12;
export const LIMITE_MAXIMO = 50;

// Estados brasileiros para filtro
export const ESTADOS_BRASIL = [
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 
  'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 
  'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'
] as const;

// Faixas de preço predefinidas para filtros rápidos
export const FAIXAS_PRECO = [
  { label: 'Até R$ 50', min: 0, max: 50 },
  { label: 'R$ 50 - R$ 100', min: 50, max: 100 },
  { label: 'R$ 100 - R$ 200', min: 100, max: 200 },
  { label: 'R$ 200 - R$ 500', min: 200, max: 500 },
  { label: 'Acima de R$ 500', min: 500, max: null }
] as const;
