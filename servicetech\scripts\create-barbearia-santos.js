/**
 * <PERSON>ript para criar empresa "Barbearia Santos" completa no Supabase
 * Inclui empresa, serviços, colaborador e atualizações de user_metadata
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

// Cliente administrativo do Supabase
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// IDs dos usuários criados anteriormente
const MARIA_SANTOS_ID = 'c7c4df3a-00fa-43b8-8d91-7980e9fe0345'; // Proprietária
const PEDRO_OLIVEIRA_ID = '22df7916-1c36-4dda-8d0d-6de61a13ad27'; // Colaborador

// Dados da empresa
const empresaData = {
  nome_empresa: 'Barbearia Santos',
  cnpj: '12345678000195', // CNPJ fictício mas válido
  telefone: '(11) 3456-7890',
  endereco: 'Rua das Flores',
  numero: '123',
  complemento: 'Loja A',
  bairro: 'Vila Madalena',
  cidade: 'São Paulo',
  estado: 'SP',
  cep: '05435-020',
  segmento: 'Barbearia',
  proprietario_user_id: MARIA_SANTOS_ID,
  status: 'ativo',
  horario_funcionamento: {
    segunda: { inicio: '09:00', fim: '19:00', ativo: true },
    terca: { inicio: '09:00', fim: '19:00', ativo: true },
    quarta: { inicio: '09:00', fim: '19:00', ativo: true },
    quinta: { inicio: '09:00', fim: '19:00', ativo: true },
    sexta: { inicio: '09:00', fim: '20:00', ativo: true },
    sabado: { inicio: '08:00', fim: '18:00', ativo: true },
    domingo: { inicio: null, fim: null, ativo: false }
  }
};

// Serviços da barbearia
const servicosData = [
  {
    nome_servico: 'Corte Masculino Tradicional',
    descricao: 'Corte de cabelo masculino clássico com acabamento na navalha',
    duracao_minutos: 30,
    preco: 25.00,
    categoria: 'Cabelo',
    ativo: true
  },
  {
    nome_servico: 'Barba Completa',
    descricao: 'Aparar e modelar barba com navalha, incluindo hidratação',
    duracao_minutos: 25,
    preco: 20.00,
    categoria: 'Barba',
    ativo: true
  },
  {
    nome_servico: 'Corte + Barba',
    descricao: 'Combo completo: corte de cabelo + barba com desconto especial',
    duracao_minutos: 50,
    preco: 40.00,
    categoria: 'Combo',
    ativo: true
  },
  {
    nome_servico: 'Bigode',
    descricao: 'Aparar e modelar bigode com precisão',
    duracao_minutos: 15,
    preco: 10.00,
    categoria: 'Barba',
    ativo: true
  },
  {
    nome_servico: 'Sobrancelha Masculina',
    descricao: 'Aparar e modelar sobrancelhas masculinas',
    duracao_minutos: 20,
    preco: 15.00,
    categoria: 'Sobrancelha',
    ativo: true
  },
  {
    nome_servico: 'Lavagem e Hidratação',
    descricao: 'Lavagem completa com shampoo e condicionador premium',
    duracao_minutos: 20,
    preco: 12.00,
    categoria: 'Cabelo',
    ativo: true
  },
  {
    nome_servico: 'Relaxamento Capilar',
    descricao: 'Tratamento para alisar e relaxar cabelos crespos',
    duracao_minutos: 90,
    preco: 60.00,
    categoria: 'Tratamentos',
    ativo: true
  }
];

async function criarEmpresa() {
  try {
    console.log('🏢 Criando empresa Barbearia Santos...');
    
    const { data: empresa, error } = await supabase
      .from('empresas')
      .insert([empresaData])
      .select()
      .single();

    if (error) {
      console.error('❌ Erro ao criar empresa:', error);
      return null;
    }

    console.log('✅ Empresa criada com sucesso!');
    console.log(`   ID: ${empresa.empresa_id}`);
    console.log(`   Nome: ${empresa.nome_empresa}`);
    console.log(`   CNPJ: ${empresa.cnpj}`);
    console.log(`   Endereço: ${empresa.endereco}, ${empresa.numero} - ${empresa.bairro}`);
    console.log(`   Cidade: ${empresa.cidade}/${empresa.estado}`);
    console.log(`   CEP: ${empresa.cep}`);
    console.log(`   Telefone: ${empresa.telefone}\n`);

    return empresa;
  } catch (error) {
    console.error('❌ Erro inesperado ao criar empresa:', error);
    return null;
  }
}

async function criarServicos(empresaId) {
  try {
    console.log('🛠️ Criando serviços da barbearia...');
    
    const servicosComEmpresa = servicosData.map(servico => ({
      ...servico,
      empresa_id: empresaId
    }));

    const { data: servicos, error } = await supabase
      .from('servicos')
      .insert(servicosComEmpresa)
      .select();

    if (error) {
      console.error('❌ Erro ao criar serviços:', error);
      return [];
    }

    console.log(`✅ ${servicos.length} serviços criados com sucesso!`);
    servicos.forEach((servico, index) => {
      console.log(`   ${index + 1}. ${servico.nome_servico} - R$ ${servico.preco} (${servico.duracao_minutos}min)`);
    });
    console.log('');

    return servicos;
  } catch (error) {
    console.error('❌ Erro inesperado ao criar serviços:', error);
    return [];
  }
}

async function associarColaborador(empresaId) {
  try {
    console.log('👨‍💼 Associando colaborador Pedro Oliveira à empresa...');
    
    const colaboradorData = {
      colaborador_user_id: PEDRO_OLIVEIRA_ID,
      empresa_id: empresaId,
      ativo_como_prestador: true,
      convite_aceito: true,
      ativo: true,
      data_aceite: new Date().toISOString(),
      percentual_comissao: 30.00, // 30% de comissão
      custos_operacionais: 0.00
    };

    const { data: colaborador, error } = await supabase
      .from('colaboradores_empresa')
      .insert([colaboradorData])
      .select()
      .single();

    if (error) {
      console.error('❌ Erro ao associar colaborador:', error);
      return null;
    }

    console.log('✅ Colaborador associado com sucesso!');
    console.log(`   Colaborador ID: ${colaborador.colaborador_user_id}`);
    console.log(`   Empresa ID: ${colaborador.empresa_id}`);
    console.log(`   Ativo como prestador: ${colaborador.ativo_como_prestador}`);
    console.log(`   Comissão: ${colaborador.percentual_comissao}%\n`);

    return colaborador;
  } catch (error) {
    console.error('❌ Erro inesperado ao associar colaborador:', error);
    return null;
  }
}

async function atualizarUserMetadata(empresaId) {
  try {
    console.log('👤 Atualizando user_metadata dos usuários...');
    
    // Atualizar Maria Santos (Proprietária)
    const { error: errorMaria } = await supabase.auth.admin.updateUserById(MARIA_SANTOS_ID, {
      user_metadata: {
        name: 'Maria Santos',
        full_name: 'Maria Santos',
        phone: '(11) 99999-2222',
        role: 'Proprietario',
        empresa_id: empresaId,
        onboarding_concluido: true
      }
    });

    if (errorMaria) {
      console.error('❌ Erro ao atualizar Maria Santos:', errorMaria);
    } else {
      console.log('✅ Maria Santos atualizada com empresa_id');
    }

    // Atualizar Pedro Oliveira (Colaborador)
    const { error: errorPedro } = await supabase.auth.admin.updateUserById(PEDRO_OLIVEIRA_ID, {
      user_metadata: {
        name: 'Pedro Oliveira',
        full_name: 'Pedro Oliveira',
        phone: '(11) 99999-3333',
        role: 'Colaborador',
        empresa_id: empresaId
      }
    });

    if (errorPedro) {
      console.error('❌ Erro ao atualizar Pedro Oliveira:', errorPedro);
    } else {
      console.log('✅ Pedro Oliveira atualizado com empresa_id');
    }

    console.log('');
  } catch (error) {
    console.error('❌ Erro inesperado ao atualizar user_metadata:', error);
  }
}

async function validarCriacao(empresaId) {
  try {
    console.log('🔍 Validando criação e políticas RLS...');
    
    // Verificar empresa
    const { data: empresa } = await supabase
      .from('empresas')
      .select('*')
      .eq('empresa_id', empresaId)
      .single();

    // Verificar serviços
    const { data: servicos } = await supabase
      .from('servicos')
      .select('*')
      .eq('empresa_id', empresaId);

    // Verificar colaborador
    const { data: colaborador } = await supabase
      .from('colaboradores_empresa')
      .select('*')
      .eq('empresa_id', empresaId);

    console.log('📊 Resumo da validação:');
    console.log(`   ✅ Empresa: ${empresa ? 'Criada' : 'Erro'}`);
    console.log(`   ✅ Serviços: ${servicos?.length || 0} criados`);
    console.log(`   ✅ Colaboradores: ${colaborador?.length || 0} associados`);
    console.log('');

    return { empresa, servicos, colaborador };
  } catch (error) {
    console.error('❌ Erro na validação:', error);
    return null;
  }
}

async function main() {
  try {
    console.log('🚀 Iniciando criação da Barbearia Santos...\n');

    // 1. Criar empresa
    const empresa = await criarEmpresa();
    if (!empresa) {
      console.log('❌ Falha ao criar empresa. Abortando...');
      return;
    }

    // 2. Criar serviços
    const servicos = await criarServicos(empresa.empresa_id);
    
    // 3. Associar colaborador
    const colaborador = await associarColaborador(empresa.empresa_id);
    
    // 4. Atualizar user_metadata
    await atualizarUserMetadata(empresa.empresa_id);
    
    // 5. Validar criação
    const validacao = await validarCriacao(empresa.empresa_id);

    console.log('=' .repeat(60));
    console.log('🎉 BARBEARIA SANTOS CRIADA COM SUCESSO!');
    console.log('=' .repeat(60));
    console.log(`📍 Empresa ID: ${empresa.empresa_id}`);
    console.log(`🏢 Nome: ${empresa.nome_empresa}`);
    console.log(`📄 CNPJ: ${empresa.cnpj}`);
    console.log(`📞 Telefone: ${empresa.telefone}`);
    console.log(`📍 Endereço: ${empresa.endereco}, ${empresa.numero} - ${empresa.bairro}`);
    console.log(`🏙️ Cidade: ${empresa.cidade}/${empresa.estado} - ${empresa.cep}`);
    console.log(`👩‍💼 Proprietária: Maria Santos (${MARIA_SANTOS_ID})`);
    console.log(`👨‍💼 Colaborador: Pedro Oliveira (${PEDRO_OLIVEIRA_ID})`);
    console.log(`🛠️ Serviços: ${servicos.length} criados`);
    console.log('');
    console.log('📝 PRÓXIMOS PASSOS:');
    console.log('1. Faça <NAME_EMAIL> para gerenciar a empresa');
    console.log('2. Faça <NAME_EMAIL> para ver a associação');
    console.log('3. Teste as políticas RLS com diferentes usuários');
    console.log('4. Verifique se os dados aparecem corretamente na interface');

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar script
main();
