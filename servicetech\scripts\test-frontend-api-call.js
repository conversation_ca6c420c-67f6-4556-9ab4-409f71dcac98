/**
 * Script para simular exatamente o que o frontend está fazendo
 */

async function testFrontendAPICall() {
  try {
    console.log('🧪 TESTE: Simulando chamada do frontend');
    console.log('=' .repeat(60));
    
    const identificador = 'barbearia-santos-3';
    const url = `/api/empresas/${identificador}`;
    const fullUrl = `http://localhost:3000${url}`;
    
    console.log('📡 URL que o frontend usa:', url);
    console.log('📡 URL completa:', fullUrl);
    
    // Simular fetch como o frontend faz
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log('📊 Response status:', response.status, response.statusText);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📊 Response text length:', responseText.length);
    console.log('📊 Response text (first 200 chars):', responseText.substring(0, 200));
    
    if (!response.ok) {
      console.error('❌ Response não OK');
      console.error('❌ Status:', response.status);
      console.error('❌ Response text:', responseText);
      return;
    }
    
    let result;
    try {
      result = JSON.parse(responseText);
      console.log('✅ JSON parse bem-sucedido');
      console.log('📊 result.success:', result.success);
      console.log('📊 result.data existe:', !!result.data);
      
      if (result.data && result.data.empresa) {
        console.log('✅ Empresa encontrada:', result.data.empresa.nome_empresa);
        console.log('📊 Serviços:', result.data.servicos?.length || 0);
        console.log('📊 Colaboradores:', result.data.colaboradores?.length || 0);
      } else {
        console.error('❌ Dados da empresa não encontrados');
      }
      
    } catch (parseError) {
      console.error('❌ Erro ao fazer parse do JSON:', parseError);
      console.error('❌ Response text que causou erro:', responseText);
    }
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎯 TESTE CONCLUÍDO');
    
  } catch (error) {
    console.error('❌ Erro geral no teste:', error);
  }
}

// Executar teste
testFrontendAPICall();
