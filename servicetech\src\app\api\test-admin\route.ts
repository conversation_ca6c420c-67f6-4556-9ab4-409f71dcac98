import { NextResponse } from 'next/server';
import { supabaseAdmin } from '@/utils/supabase/admin';

export async function GET() {
  try {
    console.log('🧪 Testando operações administrativas...');
    
    const tests: {
      success: boolean;
      timestamp: string;
      client_type: string;
      tests: Record<string, any>;
    } = {
      success: true,
      timestamp: new Date().toISOString(),
      client_type: 'admin',
      tests: {}
    };

    // Teste 1: Verificar se conseguimos acessar tabelas sem RLS
    console.log('1️⃣ Testando acesso direto às tabelas...');
    
    try {
      // Usar o cliente admin diretamente
      const adminClient = (supabaseAdmin as any).client;
      
      const { data: empresas, error: empresasError } = await adminClient
        .from('empresas')
        .select('empresa_id, nome_empresa, cnpj')
        .limit(1);

      tests.tests.empresas = {
        success: !empresasError,
        error: empresasError?.message,
        count: empresas?.length ?? 0,
        sample: empresas?.[0] ?? null
      };

      const { data: servicos, error: servicosError } = await adminClient
        .from('servicos')
        .select('servico_id, nome_servico, empresa_id')
        .limit(1);

      tests.tests.servicos = {
        success: !servicosError,
        error: servicosError?.message,
        count: servicos?.length ?? 0,
        sample: servicos?.[0] ?? null
      };

    } catch (error: any) {
      console.error('❌ Erro nos testes de tabela:', error);
      tests.tests.table_access = {
        success: false,
        error: error.message
      };
    }

    // Teste 2: Verificar operações da classe admin
    console.log('2️⃣ Testando métodos da classe administrativa...');
    
    try {
      // Teste de verificação de usuário (se existir algum)
      const testUserId = 'test-user-id';
      
      try {
        const userStatus = await supabaseAdmin.getUserStatus(testUserId);
        tests.tests.admin_methods = {
          success: true,
          message: 'Métodos administrativos funcionando',
          sample_call: 'getUserStatus'
        };
      } catch (userError: any) {
        // Esperado falhar se usuário não existir
        tests.tests.admin_methods = {
          success: true,
          message: 'Métodos administrativos funcionando (erro esperado para usuário inexistente)',
          error: userError.message
        };
      }

    } catch (error: any) {
      console.error('❌ Erro nos métodos admin:', error);
      tests.tests.admin_methods = {
        success: false,
        error: error.message
      };
    }

    console.log('✅ Testes concluídos:', tests);
    return NextResponse.json(tests);

  } catch (error: any) {
    console.error('❌ Erro geral no teste admin:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, userId } = body;

    console.log(`🧪 Testando ação administrativa: ${action}`);

    switch (action) {
      case 'test-user-status':
        if (!userId) {
          return NextResponse.json({
            success: false,
            error: 'userId é obrigatório'
          }, { status: 400 });
        }

        try {
          const userStatus = await supabaseAdmin.getUserStatus(userId);
          return NextResponse.json({
            success: true,
            message: 'Status do usuário obtido com sucesso',
            data: userStatus
          });
        } catch (error: any) {
          return NextResponse.json({
            success: false,
            error: 'Erro ao obter status do usuário',
            details: error.message
          }, { status: 400 });
        }

      case 'test-webhook-simulation':
        const testData = {
          userId: userId || 'test-user-id',
          plano: 'essencial',
          cnpj: '12345678000199',
          estabelecimento: 'Empresa Teste Admin'
        };

        try {
          const empresaId = await supabaseAdmin.processWebhookPayment(testData);
          return NextResponse.json({
            success: true,
            message: 'Webhook simulado com sucesso',
            data: { empresaId, testData }
          });
        } catch (error: any) {
          return NextResponse.json({
            success: false,
            error: 'Erro na simulação do webhook',
            details: error.message
          }, { status: 400 });
        }

      default:
        return NextResponse.json({
          success: false,
          error: 'Ação não reconhecida'
        }, { status: 400 });
    }

  } catch (error: any) {
    console.error('❌ Erro geral no teste POST admin:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error.message
    }, { status: 500 });
  }
}
