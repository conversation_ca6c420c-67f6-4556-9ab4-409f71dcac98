import { <PERSON><PERSON><PERSON> } from 'twilio';
import { EnviarSMSData, SMSResponse } from '@/types/notifications';

export class SMSService {
  private client: Twilio;
  private fromNumber: string;

  constructor() {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    this.fromNumber = process.env.TWILIO_PHONE_NUMBER || '';

    if (!accountSid || !authToken) {
      throw new Error('Credenciais do Twilio não configuradas');
    }

    if (!this.fromNumber) {
      throw new Error('Número do Twilio não configurado');
    }

    this.client = new Twilio(accountSid, authToken);
  }

  /**
   * Envia SMS usando Twilio
   */
  async enviarSMS(data: EnviarSMSData): Promise<SMSResponse> {
    try {
      console.log(`📱 Enviando SMS para: ${data.to}`);

      // Validar formato do número (deve incluir código do país)
      const numeroFormatado = this.formatarNumero(data.to);

      const message = await this.client.messages.create({
        body: data.message,
        from: data.from || this.fromNumber,
        to: numeroFormatado
      });

      console.log(`✅ SMS enviado com sucesso: ${message.sid}`);

      return {
        success: true,
        messageId: message.sid
      };

    } catch (error) {
      console.error('❌ Erro ao enviar SMS:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao enviar SMS'
      };
    }
  }

  /**
   * Formatar número de telefone para padrão internacional
   */
  private formatarNumero(numero: string): string {
    // Remove caracteres não numéricos
    const apenasNumeros = numero.replace(/\D/g, '');

    // Se já tem código do país (+55), retorna como está
    if (apenasNumeros.startsWith('55') && apenasNumeros.length === 13) {
      return `+${apenasNumeros}`;
    }

    // Se é número brasileiro sem código do país, adiciona +55
    if (apenasNumeros.length === 11) {
      return `+55${apenasNumeros}`;
    }

    // Se é número brasileiro com DDD sem 9, adiciona 9
    if (apenasNumeros.length === 10) {
      const ddd = apenasNumeros.substring(0, 2);
      const numero = apenasNumeros.substring(2);
      return `+55${ddd}9${numero}`;
    }

    // Retorna como está se não conseguir formatar
    return numero;
  }

  /**
   * Validar se o número é válido para SMS
   */
  validarNumero(numero: string): boolean {
    const apenasNumeros = numero.replace(/\D/g, '');
    
    // Número brasileiro deve ter 11 dígitos (DDD + 9 + 8 dígitos)
    // ou 13 dígitos com código do país (55 + DDD + 9 + 8 dígitos)
    return apenasNumeros.length === 11 || 
           (apenasNumeros.length === 13 && apenasNumeros.startsWith('55'));
  }

  /**
   * Gerar template de SMS baseado no tipo de notificação
   */
  gerarTemplateSMS(tipo: string, contexto: any): string {
    switch (tipo) {
      case 'novo_agendamento':
        return `ServiceTech: Seu agendamento de ${contexto.servico_nome} foi solicitado para ${this.formatarDataSMS(contexto.data_hora_inicio)} em ${contexto.empresa_nome}. Aguarde confirmação.`;

      case 'agendamento_confirmado':
        return `ServiceTech: Agendamento confirmado! ${contexto.servico_nome} em ${contexto.empresa_nome} no dia ${this.formatarDataSMS(contexto.data_hora_inicio)}. Código: ${contexto.codigo_confirmacao}`;

      case 'agendamento_recusado':
        return `ServiceTech: Seu agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} foi recusado. Tente outro horário ou estabelecimento.`;

      case 'agendamento_cancelado':
        return `ServiceTech: Agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} foi cancelado. Reembolso processado automaticamente.`;

      case 'lembrete_confirmacao':
        return `ServiceTech: Lembrete! Agendamento de ${contexto.cliente_nome} para ${contexto.servico_nome} expira em breve. Confirme ou recuse.`;

      case 'lembrete_agendamento':
        return `ServiceTech: Lembrete! Seu agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} é amanhã às ${this.formatarHoraSMS(contexto.data_hora_inicio)}.`;

      case 'pagamento_confirmado':
        return `ServiceTech: Pagamento confirmado! Agendamento de ${contexto.servico_nome} em ${contexto.empresa_nome} para ${this.formatarDataSMS(contexto.data_hora_inicio)}.`;

      default:
        return `ServiceTech: Você tem uma nova notificação. Acesse o app para mais detalhes.`;
    }
  }

  /**
   * Formatar data para SMS (formato brasileiro)
   */
  private formatarDataSMS(dataISO: string): string {
    const data = new Date(dataISO);
    return data.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formatar hora para SMS
   */
  private formatarHoraSMS(dataISO: string): string {
    const data = new Date(dataISO);
    return data.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
}
