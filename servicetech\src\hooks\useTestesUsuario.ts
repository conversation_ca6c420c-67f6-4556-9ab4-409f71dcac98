'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  CenarioTeste, 
  SessaoTeste, 
  ParticipanteTeste, 
  ExecucaoTeste, 
  FeedbackTeste,
  MetricasSessao,
  FiltrosSessoes,
  FiltrosCenarios,
  TestesUsuarioApiResponse 
} from '@/types/testesUsuario';

export function useTestesUsuario() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Estados para diferentes entidades
  const [cenarios, setCenarios] = useState<CenarioTeste[]>([]);
  const [sessoes, setSessoes] = useState<SessaoTeste[]>([]);
  const [participantes, setParticipantes] = useState<ParticipanteTeste[]>([]);
  const [execucoes, setExecucoes] = useState<ExecucaoTeste[]>([]);
  const [metricas, setMetricas] = useState<MetricasSessao | null>(null);

  // Função auxiliar para fazer requisições
  const makeRequest = useCallback(async (
    url: string, 
    options: RequestInit = {}
  ): Promise<TestesUsuarioApiResponse> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro na requisição');
      }

      return data;
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // ===== CENÁRIOS DE TESTE =====
  
  const buscarCenarios = useCallback(async (filtros?: FiltrosCenarios) => {
    const params = new URLSearchParams();
    if (filtros?.papel_usuario) params.append('papel_usuario', filtros.papel_usuario);
    if (filtros?.categoria) params.append('categoria', filtros.categoria);
    if (filtros?.dificuldade) params.append('dificuldade', filtros.dificuldade);
    if (filtros?.ativo !== undefined) params.append('ativo', filtros.ativo.toString());

    const response = await makeRequest(`/api/testes-usuario/cenarios?${params}`);
    if (response.success) {
      setCenarios(response.data);
    }
    return response;
  }, [makeRequest]);

  const criarCenario = useCallback(async (cenario: Omit<CenarioTeste, 'cenario_id' | 'created_at' | 'updated_at'>) => {
    const response = await makeRequest('/api/testes-usuario/cenarios', {
      method: 'POST',
      body: JSON.stringify(cenario),
    });
    
    if (response.success) {
      await buscarCenarios(); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarCenarios]);

  const atualizarCenario = useCallback(async (cenarioId: number, dados: Partial<CenarioTeste>) => {
    const response = await makeRequest(`/api/testes-usuario/cenarios/${cenarioId}`, {
      method: 'PATCH',
      body: JSON.stringify(dados),
    });
    
    if (response.success) {
      await buscarCenarios(); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarCenarios]);

  const excluirCenario = useCallback(async (cenarioId: number) => {
    const response = await makeRequest(`/api/testes-usuario/cenarios/${cenarioId}`, {
      method: 'DELETE',
    });
    
    if (response.success) {
      await buscarCenarios(); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarCenarios]);

  // ===== SESSÕES DE TESTE =====
  
  const buscarSessoes = useCallback(async (filtros?: FiltrosSessoes) => {
    const params = new URLSearchParams();
    if (filtros?.status) params.append('status', filtros.status);
    if (filtros?.data_inicio) params.append('data_inicio', filtros.data_inicio);
    if (filtros?.data_fim) params.append('data_fim', filtros.data_fim);
    if (filtros?.moderador) params.append('moderador', filtros.moderador);

    const response = await makeRequest(`/api/testes-usuario/sessoes?${params}`);
    if (response.success) {
      setSessoes(response.data);
    }
    return response;
  }, [makeRequest]);

  const criarSessao = useCallback(async (sessao: Omit<SessaoTeste, 'sessao_id' | 'created_at' | 'updated_at'>) => {
    const response = await makeRequest('/api/testes-usuario/sessoes', {
      method: 'POST',
      body: JSON.stringify(sessao),
    });
    
    if (response.success) {
      await buscarSessoes(); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarSessoes]);

  const atualizarSessao = useCallback(async (sessaoId: number, dados: Partial<SessaoTeste>) => {
    const response = await makeRequest(`/api/testes-usuario/sessoes/${sessaoId}`, {
      method: 'PATCH',
      body: JSON.stringify(dados),
    });
    
    if (response.success) {
      await buscarSessoes(); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarSessoes]);

  // ===== PARTICIPANTES =====
  
  const buscarParticipantes = useCallback(async (sessaoId: number) => {
    const response = await makeRequest(`/api/testes-usuario/sessoes/${sessaoId}/participantes`);
    if (response.success) {
      setParticipantes(response.data);
    }
    return response;
  }, [makeRequest]);

  const adicionarParticipante = useCallback(async (participante: Omit<ParticipanteTeste, 'participante_id'>) => {
    const response = await makeRequest('/api/testes-usuario/participantes', {
      method: 'POST',
      body: JSON.stringify(participante),
    });
    
    if (response.success && participante.sessao_id) {
      await buscarParticipantes(participante.sessao_id); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarParticipantes]);

  const atualizarParticipante = useCallback(async (participanteId: number, dados: Partial<ParticipanteTeste>) => {
    const response = await makeRequest(`/api/testes-usuario/participantes/${participanteId}`, {
      method: 'PATCH',
      body: JSON.stringify(dados),
    });
    return response;
  }, [makeRequest]);

  // ===== EXECUÇÕES DE TESTE =====
  
  const iniciarExecucao = useCallback(async (dados: {
    sessao_id: number;
    participante_id: number;
    cenario_id: number;
  }) => {
    const response = await makeRequest('/api/testes-usuario/execucoes', {
      method: 'POST',
      body: JSON.stringify({
        ...dados,
        data_inicio: new Date().toISOString(),
        status: 'Iniciado',
      }),
    });
    return response;
  }, [makeRequest]);

  const atualizarExecucao = useCallback(async (execucaoId: number, dados: Partial<ExecucaoTeste>) => {
    const response = await makeRequest(`/api/testes-usuario/execucoes/${execucaoId}`, {
      method: 'PATCH',
      body: JSON.stringify(dados),
    });
    return response;
  }, [makeRequest]);

  const finalizarExecucao = useCallback(async (execucaoId: number, dados: {
    sucesso_completo: boolean;
    passos_completados: number;
    erros_encontrados: number;
    observacoes_participante?: string;
    observacoes_moderador?: string;
  }) => {
    const response = await makeRequest(`/api/testes-usuario/execucoes/${execucaoId}`, {
      method: 'PATCH',
      body: JSON.stringify({
        ...dados,
        data_fim: new Date().toISOString(),
        status: 'Concluido',
      }),
    });
    return response;
  }, [makeRequest]);

  // ===== FEEDBACK =====
  
  const enviarFeedback = useCallback(async (feedback: Omit<FeedbackTeste, 'feedback_id' | 'created_at'>) => {
    const response = await makeRequest('/api/testes-usuario/feedback', {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
    return response;
  }, [makeRequest]);

  // ===== MÉTRICAS E RELATÓRIOS =====
  
  const buscarMetricasSessao = useCallback(async (sessaoId: number) => {
    const response = await makeRequest(`/api/testes-usuario/sessoes/${sessaoId}/metricas`);
    if (response.success) {
      setMetricas(response.data);
    }
    return response;
  }, [makeRequest]);

  const gerarRelatorioSessao = useCallback(async (sessaoId: number, formato: 'pdf' | 'excel' = 'pdf') => {
    const response = await makeRequest(`/api/testes-usuario/sessoes/${sessaoId}/relatorio?formato=${formato}`);
    return response;
  }, [makeRequest]);

  // ===== TEMPLATES E UTILITÁRIOS =====
  
  const buscarTemplatesCenarios = useCallback(async () => {
    const response = await makeRequest('/api/testes-usuario/templates');
    return response;
  }, [makeRequest]);

  const criarCenarioDeTemplate = useCallback(async (templateId: number, customizacoes?: any) => {
    const response = await makeRequest('/api/testes-usuario/templates/criar-cenario', {
      method: 'POST',
      body: JSON.stringify({ templateId, customizacoes }),
    });
    
    if (response.success) {
      await buscarCenarios(); // Recarregar lista
    }
    return response;
  }, [makeRequest, buscarCenarios]);

  return {
    // Estados
    loading,
    error,
    cenarios,
    sessoes,
    participantes,
    execucoes,
    metricas,

    // Cenários
    buscarCenarios,
    criarCenario,
    atualizarCenario,
    excluirCenario,

    // Sessões
    buscarSessoes,
    criarSessao,
    atualizarSessao,

    // Participantes
    buscarParticipantes,
    adicionarParticipante,
    atualizarParticipante,

    // Execuções
    iniciarExecucao,
    atualizarExecucao,
    finalizarExecucao,

    // Feedback
    enviarFeedback,

    // Métricas
    buscarMetricasSessao,
    gerarRelatorioSessao,

    // Templates
    buscarTemplatesCenarios,
    criarCenarioDeTemplate,

    // Utilitários
    setError,
  };
}
