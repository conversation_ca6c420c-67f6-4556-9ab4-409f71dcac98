# Guia de Notificações SMS e Push - ServiceTech

## 📱 Visão Geral

O ServiceTech implementa um sistema completo de notificações multicanal que suporta:
- **Email** (via Resend)
- **SMS** (via Twilio)
- **Push Notifications** (via Firebase Cloud Messaging)

## 🚀 Configuração Inicial

### 1. Configuração do Twilio (SMS)

1. **<PERSON><PERSON>r <PERSON>ta <PERSON>wilio**:
   - Acesse https://twilio.com e crie uma conta
   - Verifique seu número de telefone

2. **Obter Credenciais**:
   - Account SID: Encontrado no Console Dashboard
   - Auth Token: Encontrado no Console Dashboard
   - Phone Number: Compre um número no Console

3. **Configurar Variáveis de Ambiente**:
   ```env
   TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   TWILIO_AUTH_TOKEN=your_auth_token_here
   TWILIO_PHONE_NUMBER=+*************
   ```

### 2. Configuração do Firebase (Push)

1. **Criar Projeto Firebase**:
   - Acesse https://console.firebase.google.com
   - Crie um novo projeto
   - Ative Cloud Messaging

2. **Gerar Chave de Serviço**:
   - Vá em Project Settings > Service Accounts
   - Clique em "Generate new private key"
   - Baixe o arquivo JSON

3. **Configurar VAPID Key**:
   - Em Project Settings > Cloud Messaging
   - Gere uma VAPID key para Web Push

4. **Configurar Variáveis de Ambiente**:
   ```env
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
   FIREBASE_CLIENT_EMAIL=<EMAIL>
   NEXT_PUBLIC_FIREBASE_VAPID_KEY=your_vapid_key
   NEXT_PUBLIC_FIREBASE_CONFIG={"apiKey":"...","authDomain":"..."}
   ```

### 3. Configuração do Banco de Dados

As tabelas `preferencias_notificacao` e `device_tokens` são necessárias para o sistema de notificações SMS e Push. Elas já estão incluídas e detalhadas no documento principal do [Esquema do Banco de Dados](../database_schema.md). Consulte esse documento para a estrutura completa e políticas RLS.

## 📋 Como Usar

### 1. Preferências de Notificação

Os usuários podem configurar suas preferências sobre quais notificações desejam receber e por quais canais.
- **Interface do Usuário**: Geralmente em uma seção de "Configurações de Notificação" no perfil do usuário, utilizando o componente `NotificationPreferences`.
- **APIs Envolvidas**:
    - `GET /api/notifications/preferences`: Para buscar as preferências atuais do usuário.
    - `PUT /api/notifications/preferences`: Para atualizar as preferências.
    (Consulte a [Documentação de APIs](../technical/api-documentation.md#apis-de-notificações) para detalhes dos endpoints.)

```typescript
// Exemplo de uso do hook
const { preferences, toggleChannel, toggleNotificationType } = useNotificationPreferences();

// Alternar canal SMS
await toggleChannel('sms');

// Alternar tipo de notificação
await toggleNotificationType('novo_agendamento');
```

### 2. Tokens de Dispositivo (Push)

Para que as notificações push funcionem, o token do dispositivo do usuário (FCM Token) deve ser registrado.
- **Registro**: Realizado pelo frontend quando o usuário permite notificações push.
- **API Envolvida**: `POST /api/notifications/device-tokens` para registrar o token.
  (Consulte a [Documentação de APIs](../technical/api-documentation.md#apis-de-notificações) para detalhes.)

```typescript
// Exemplo de uso do hook
const { registerToken, tokens } = useDeviceTokens();

// Registrar token FCM
await registerToken('fcm_token_here', 'web');
```

### 3. Envio de Notificações

O sistema automaticamente determina quais canais usar baseado nas preferências:

```typescript
// Via NotificationService
const notificationService = new NotificationService();

await notificationService.processarNotificacao({
  tipo: 'novo_agendamento',
  destinatario_id: 'user_uuid',
  contexto: {
    agendamento_id: 123,
    cliente_nome: 'João Silva',
    servico_nome: 'Corte de Cabelo',
    // ... outros dados
  },
  // canal: 'sms' // Opcional: forçar canal específico
});
```

## 🧪 Testes

### 1. Página de Testes Administrativos

Acesse a página de gerenciamento e testes de notificações no painel administrativo (geralmente `/admin/notifications-sms-push` ou similar) para:
- Testar envio de SMS e Push
- Verificar status dos serviços
- Monitorar configurações

### 2. Testes Programáticos

```typescript
// Teste SMS direto
const smsService = new SMSService();
const resultado = await smsService.enviarSMS({
  to: '+*************',
  message: 'Teste de SMS'
});

// Teste Push direto
const pushService = new PushService();
const resultado = await pushService.enviarPush({
  token: 'fcm_token',
  title: 'Teste',
  body: 'Mensagem de teste'
});
```

## 📊 Monitoramento

### 1. Status dos Serviços

Verifique o status da integração com os serviços de SMS e Push através dos endpoints de teste ou seções de diagnóstico no painel administrativo. A API `GET /api/notifications/batch` (descrita na [Documentação de APIs](../technical/api-documentation.md#apis-de-notificações)) também pode fornecer estatísticas de envio.

### 2. Logs

Os serviços geram logs detalhados:
- ✅ Envios bem-sucedidos
- ❌ Falhas com detalhes do erro
- 📱 Informações de formatação de números
- 🔔 Status de tokens FCM

## 🔧 Troubleshooting

### Problemas Comuns com SMS

1. **Número Inválido**:
   - Verifique o formato: +*************
   - Use o método `validarNumero()` do SMSService

2. **Falha no Envio**:
   - Verifique credenciais do Twilio
   - Confirme que o número Twilio está ativo
   - Verifique saldo da conta Twilio

### Problemas Comuns com Push

1. **Token Inválido**:
   - Regenere o token FCM
   - Verifique se o Service Worker está registrado

2. **Permissão Negada**:
   - Solicite permissão novamente
   - Verifique configurações do navegador

3. **Service Worker**:
   - Verifique se `/sw.js` está acessível
   - Confirme registro do Service Worker

## 🔒 Segurança

### 1. Políticas RLS

As tabelas `preferencias_notificacao` e `device_tokens` têm políticas RLS:
- Usuários só acessam seus próprios dados
- Administradores têm acesso completo para monitoramento

### 2. Validações

- Números de telefone são validados antes do envio
- Tokens FCM são verificados antes do registro
- Preferências são validadas contra tipos permitidos

### 3. Rate Limiting

Implemente rate limiting nas APIs para evitar abuso:
- Limite de SMS por usuário/hora
- Limite de registros de token por usuário

## 📈 Métricas e Analytics

### 1. Métricas Disponíveis

- Taxa de entrega por canal
- Preferências mais comuns
- Dispositivos ativos por usuário
- Falhas de envio por tipo

### 2. Implementação

```sql
-- Exemplo de query para métricas
SELECT 
  canal,
  COUNT(*) as total_envios,
  SUM(CASE WHEN enviada THEN 1 ELSE 0 END) as sucessos,
  AVG(CASE WHEN enviada THEN 1.0 ELSE 0.0 END) * 100 as taxa_sucesso
FROM notificacoes 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY canal;
```

## 🚀 Próximos Passos

1. **Rich Notifications**: Adicionar imagens e ações personalizadas
2. **Scheduling**: Agendar notificações para horários específicos
3. **A/B Testing**: Testar diferentes templates e canais
4. **Analytics Avançados**: Dashboard com métricas detalhadas
5. **Webhooks**: Notificar sistemas externos sobre eventos
