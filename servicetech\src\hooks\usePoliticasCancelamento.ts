import { useState, useCallback } from 'react';
import { 
  PoliticaCancelamento, 
  AtualizarPoliticaCancelamentoData,
  CancelarAgendamentoData,
  CancelamentoResponse,
  RegrasCancelamento,
  CalculoReembolso
} from '@/types/politicas';
import { StatusAgendamento } from '@/types/agendamentos';
import { CancelamentoUtils } from '@/utils/cancelamento';

interface UsePoliticasCancelamentoReturn {
  // Estados
  politica: PoliticaCancelamento | null;
  loading: boolean;
  error: string | null;
  
  // Funções
  buscarPolitica: (empresaId: number) => Promise<void>;
  atualizarPolitica: (empresaId: number, dados: AtualizarPoliticaCancelamentoData) => Promise<boolean>;
  cancelarAgendamento: (agendamentoId: number, dados: CancelarAgendamentoData) => Promise<CancelamentoResponse | null>;
  calcularRegrasCancelamento: (
    dataHoraAgendamento: string,
    statusAgendamento: StatusAgendamento,
    canceladoPor: 'cliente' | 'empresa'
  ) => RegrasCancelamento | null;
  calcularReembolso: (valorOriginal: number, percentualReembolso: number, motivo: string) => CalculoReembolso;
  formatarTempoRestante: (horasRestantes: number) => string;
  
  // Utilitários
  limparError: () => void;
}

export function usePoliticasCancelamento(): UsePoliticasCancelamentoReturn {
  const [politica, setPolitica] = useState<PoliticaCancelamento | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Buscar política de cancelamento da empresa
  const buscarPolitica = useCallback(async (empresaId: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/empresas/${empresaId}/politicas`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar política de cancelamento');
      }

      setPolitica(result.data);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao buscar política:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Atualizar política de cancelamento
  const atualizarPolitica = useCallback(async (
    empresaId: number, 
    dados: AtualizarPoliticaCancelamentoData
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/empresas/${empresaId}/politicas`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar política de cancelamento');
      }

      setPolitica(result.data);
      return true;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao atualizar política:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Cancelar agendamento
  const cancelarAgendamento = useCallback(async (
    agendamentoId: number,
    dados: CancelarAgendamentoData
  ): Promise<CancelamentoResponse | null> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/agendamentos/${agendamentoId}/cancelar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao cancelar agendamento');
      }

      return result as CancelamentoResponse;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao cancelar agendamento:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Calcular regras de cancelamento
  const calcularRegrasCancelamento = useCallback((
    dataHoraAgendamento: string,
    statusAgendamento: StatusAgendamento,
    canceladoPor: 'cliente' | 'empresa'
  ): RegrasCancelamento | null => {
    if (!politica) return null;

    return CancelamentoUtils.calcularRegrasCancelamento(
      dataHoraAgendamento,
      statusAgendamento,
      politica,
      canceladoPor
    );
  }, [politica]);

  // Calcular reembolso
  const calcularReembolso = useCallback((
    valorOriginal: number,
    percentualReembolso: number,
    motivo: string
  ): CalculoReembolso => {
    return CancelamentoUtils.calcularReembolso(valorOriginal, percentualReembolso, motivo);
  }, []);

  // Formatar tempo restante
  const formatarTempoRestante = useCallback((horasRestantes: number): string => {
    return CancelamentoUtils.formatarTempoRestante(horasRestantes);
  }, []);

  // Limpar erro
  const limparError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Estados
    politica,
    loading,
    error,
    
    // Funções
    buscarPolitica,
    atualizarPolitica,
    cancelarAgendamento,
    calcularRegrasCancelamento,
    calcularReembolso,
    formatarTempoRestante,
    
    // Utilitários
    limparError
  };
}
