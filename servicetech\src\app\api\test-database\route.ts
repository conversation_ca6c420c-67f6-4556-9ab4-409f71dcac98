import { NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    console.log('🧪 Testando conexão com banco de dados...');

    const supabase = createAdminClient();

    // Teste 1: Verificar conexão básica (sem autenticação de usuário)
    console.log('1️⃣ Testando conexão básica...');

    // Para cliente admin, não precisamos verificar usuário autenticado
    // Vamos testar diretamente as operações do banco

    // Teste 2: Verificar acesso à tabela empresas
    console.log('2️⃣ Testando acesso à tabela empresas...');
    const { data: empresas, error: empresasError } = await supabase
      .from('empresas')
      .select('empresa_id, nome_empresa, cnpj')
      .limit(1);

    if (empresasError) {
      console.error('❌ Erro ao acessar tabela empresas:', empresasError);
    }

    // Teste 3: Verificar acesso à tabela servicos
    console.log('3️⃣ Testando acesso à tabela servicos...');
    const { data: servicos, error: servicosError } = await supabase
      .from('servicos')
      .select('servico_id, nome_servico, empresa_id')
      .limit(1);

    if (servicosError) {
      console.error('❌ Erro ao acessar tabela servicos:', servicosError);
    }

    // Teste 4: Verificar estrutura das tabelas
    console.log('4️⃣ Verificando estrutura das tabelas...');

    const resultado = {
      success: true,
      timestamp: new Date().toISOString(),
      client_type: 'admin',
      tests: {
        connection: {
          success: true,
          message: 'Cliente administrativo conectado'
        },
        empresas: {
          success: !empresasError,
          error: empresasError?.message,
          count: empresas?.length ?? 0,
          sample: empresas?.[0] ?? null
        },
        servicos: {
          success: !servicosError,
          error: servicosError?.message,
          count: servicos?.length ?? 0,
          sample: servicos?.[0] ?? null
        }
      }
    };

    console.log('✅ Teste concluído:', resultado);

    return NextResponse.json(resultado);

  } catch (error: any) {
    console.error('❌ Erro geral no teste:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, data } = body;

    console.log(`🧪 Testando ação: ${action}`);
    
    const supabase = createAdminClient();

    switch (action) {
      case 'test-insert-empresa':
        console.log('📝 Testando inserção de empresa...');
        const { data: novaEmpresa, error: insertError } = await supabase
          .from('empresas')
          .insert([{
            nome_empresa: 'Teste Empresa',
            cnpj: '12345678000199',
            telefone: '(11) 99999-9999',
            endereco: 'Rua Teste, 123',
            numero: '123',
            bairro: 'Centro',
            cidade: 'São Paulo',
            estado: 'SP',
            cep: '01234-567',
            segmento: 'Teste',
            proprietario_user_id: data.userId,
            status: 'ativo',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select();

        if (insertError) {
          console.error('❌ Erro ao inserir empresa:', insertError);
          return NextResponse.json({
            success: false,
            error: 'Erro ao inserir empresa',
            details: insertError
          }, { status: 400 });
        }

        console.log('✅ Empresa inserida:', novaEmpresa);
        return NextResponse.json({
          success: true,
          message: 'Empresa inserida com sucesso',
          data: novaEmpresa
        });

      case 'test-insert-servico':
        console.log('📝 Testando inserção de serviço...');
        const { data: novoServico, error: servicoError } = await supabase
          .from('servicos')
          .insert([{
            empresa_id: data.empresaId,
            nome_servico: 'Serviço Teste',
            descricao: 'Descrição do serviço teste',
            duracao_minutos: 60,
            preco: 100.00,
            ativo: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }])
          .select();

        if (servicoError) {
          console.error('❌ Erro ao inserir serviço:', servicoError);
          return NextResponse.json({
            success: false,
            error: 'Erro ao inserir serviço',
            details: servicoError
          }, { status: 400 });
        }

        console.log('✅ Serviço inserido:', novoServico);
        return NextResponse.json({
          success: true,
          message: 'Serviço inserido com sucesso',
          data: novoServico
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Ação não reconhecida'
        }, { status: 400 });
    }

  } catch (error: any) {
    console.error('❌ Erro geral no teste POST:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error.message
    }, { status: 500 });
  }
}
