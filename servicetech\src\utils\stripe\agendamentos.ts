import Stripe from 'stripe';

// Inicializar o cliente Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

/**
 * Utilitários para pagamentos de agendamentos com Stripe
 */
export class StripeAgendamentosUtils {
  
  /**
   * Criar Payment Intent para agendamento
   */
  static async criarPaymentIntent(dados: {
    agendamento_id: number;
    valor: number;
    cliente_email: string;
    cliente_nome?: string;
    descricao: string;
    metadata: Record<string, string>;
  }): Promise<Stripe.PaymentIntent> {
    
    // Buscar ou criar cliente no Stripe
    let stripeCustomer;
    try {
      const customers = await stripe.customers.list({
        email: dados.cliente_email,
        limit: 1
      });

      if (customers.data.length > 0) {
        stripeCustomer = customers.data[0];
      } else {
        stripeCustomer = await stripe.customers.create({
          email: dados.cliente_email,
          name: dados.cliente_nome || 'Cliente',
          metadata: {
            agendamento_id: dados.agendamento_id.toString()
          }
        });
      }
    } catch (error) {
      console.error('Erro ao criar/buscar cliente Stripe:', error);
      // Continuar sem cliente se houver erro
    }

    // Criar Payment Intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(dados.valor * 100), // Converter para centavos
      currency: 'brl',
      customer: stripeCustomer?.id,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        ...dados.metadata,
        agendamento_id: dados.agendamento_id.toString(),
        tipo_pagamento: 'agendamento'
      },
      description: dados.descricao,
    });

    return paymentIntent;
  }

  /**
   * Processar reembolso de agendamento
   */
  static async processarReembolso(dados: {
    payment_intent_id: string;
    valor_reembolso: number;
    motivo?: string;
    agendamento_id: number;
    processado_por: string;
  }): Promise<Stripe.Refund> {
    
    const refund = await stripe.refunds.create({
      payment_intent: dados.payment_intent_id,
      amount: Math.round(dados.valor_reembolso * 100), // Converter para centavos
      metadata: {
        agendamento_id: dados.agendamento_id.toString(),
        motivo: dados.motivo || 'Cancelamento de agendamento',
        processado_por: dados.processado_por,
        tipo: 'agendamento'
      },
      reason: 'requested_by_customer'
    });

    return refund;
  }

  /**
   * Verificar status de Payment Intent
   */
  static async verificarStatusPaymentIntent(paymentIntentId: string): Promise<{
    status: string;
    valor: number;
    cliente_email?: string;
    metadata: Record<string, string>;
  }> {
    
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    
    return {
      status: paymentIntent.status,
      valor: paymentIntent.amount / 100, // Converter de centavos para reais
      cliente_email: paymentIntent.receipt_email || undefined,
      metadata: paymentIntent.metadata
    };
  }

  /**
   * Listar reembolsos de um Payment Intent
   */
  static async listarReembolsos(paymentIntentId: string): Promise<Stripe.Refund[]> {
    const refunds = await stripe.refunds.list({
      payment_intent: paymentIntentId,
      limit: 100
    });

    return refunds.data;
  }

  /**
   * Cancelar Payment Intent (se ainda não foi confirmado)
   */
  static async cancelarPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    const paymentIntent = await stripe.paymentIntents.cancel(paymentIntentId);
    return paymentIntent;
  }

  /**
   * Calcular taxa do Stripe (aproximada)
   */
  static calcularTaxaStripe(valor: number): number {
    // Taxa aproximada do Stripe no Brasil: 3.4% + R$ 0,60
    const percentual = 0.034;
    const taxaFixa = 0.60;
    return (valor * percentual) + taxaFixa;
  }

  /**
   * Validar valor mínimo para pagamento
   */
  static validarValorMinimo(valor: number): boolean {
    // Valor mínimo do Stripe: R$ 0,50
    return valor >= 0.50;
  }

  /**
   * Formatar valor para exibição
   */
  static formatarValor(valor: number): string {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  }

  /**
   * Verificar se Payment Intent pode ser reembolsado
   */
  static podeSerReembolsado(status: string): boolean {
    return status === 'succeeded';
  }

  /**
   * Calcular valor líquido após taxas
   */
  static calcularValorLiquido(valorBruto: number): number {
    const taxa = this.calcularTaxaStripe(valorBruto);
    return valorBruto - taxa;
  }

  /**
   * Gerar descrição padrão para Payment Intent
   */
  static gerarDescricao(dados: {
    servico_nome: string;
    empresa_nome: string;
    agendamento_id: number;
  }): string {
    return `Agendamento #${dados.agendamento_id} - ${dados.servico_nome} na ${dados.empresa_nome}`;
  }

  /**
   * Extrair informações de erro do Stripe
   */
  static extrairErroStripe(error: any): {
    codigo: string;
    mensagem: string;
    tipo: string;
  } {
    if (error.type === 'StripeCardError') {
      return {
        codigo: error.code || 'card_error',
        mensagem: error.message || 'Erro no cartão',
        tipo: 'card_error'
      };
    }

    if (error.type === 'StripeInvalidRequestError') {
      return {
        codigo: error.code || 'invalid_request',
        mensagem: 'Dados inválidos na solicitação',
        tipo: 'invalid_request'
      };
    }

    if (error.type === 'StripeAPIError') {
      return {
        codigo: error.code || 'api_error',
        mensagem: 'Erro interno do Stripe',
        tipo: 'api_error'
      };
    }

    return {
      codigo: 'unknown_error',
      mensagem: error.message || 'Erro desconhecido',
      tipo: 'unknown'
    };
  }

  /**
   * Verificar se webhook é válido
   */
  static verificarWebhook(payload: string, signature: string): Stripe.Event {
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET não configurado');
    }

    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);
  }
}
