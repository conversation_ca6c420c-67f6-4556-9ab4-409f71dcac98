'use client';

import { Card, CardContent } from '@/components/ui/Card';

interface Colaborador {
  colaborador_user_id: string;
  name: string;
  ativo_como_prestador: boolean;
}

interface SeletorColaboradorProps {
  colaboradores: Colaborador[];
  colaboradorSelecionado?: {
    colaborador_user_id: string;
    name: string;
  };
  onSelecionarColaborador: (colaborador?: {
    colaborador_user_id: string;
    name: string;
  }) => void;
  loading?: boolean;
  permitirQualquerColaborador?: boolean;
}

export function SeletorColaborador({
  colaboradores,
  colaboradorSelecionado,
  onSelecionarColaborador,
  loading = false,
  permitirQualquerColaborador = true
}: SeletorColaboradorProps) {

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
          <p className="text-[var(--text-secondary)] mt-4">Carregando colaboradores...</p>
        </div>
      </div>
    );
  }

  const colaboradoresAtivos = colaboradores.filter(c => c.ativo_como_prestador);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
          Escolha um Profissional
        </h3>
        <p className="text-[var(--text-secondary)] text-sm">
          Selecione um profissional específico ou deixe que escolhamos o melhor disponível para você
        </p>
      </div>

      {/* Opção "Qualquer Colaborador" */}
      {permitirQualquerColaborador && (
        <Card
          className={`cursor-pointer transition-all hover:shadow-lg ${
            !colaboradorSelecionado
              ? 'ring-2 ring-[var(--primary)] bg-[var(--primary-light)]'
              : 'hover:ring-1 hover:ring-[var(--primary-light)]'
          }`}
          onClick={() => onSelecionarColaborador(undefined)}
        >
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-[var(--primary)] to-[var(--primary-dark)] rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)] mb-1">
                  Qualquer Profissional Disponível
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  Deixe que escolhemos o melhor profissional disponível no horário selecionado
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-xs bg-[var(--success-light)] text-[var(--success)] px-2 py-1 rounded">
                    Recomendado
                  </span>
                  <span className="text-xs text-[var(--text-secondary)]">
                    Mais opções de horário
                  </span>
                </div>
              </div>

              {!colaboradorSelecionado && (
                <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de colaboradores */}
      {colaboradoresAtivos.length > 0 ? (
        <div className="space-y-3">
          <h4 className="text-lg font-medium text-[var(--text-primary)]">
            Profissionais Disponíveis ({colaboradoresAtivos.length})
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {colaboradoresAtivos.map((colaborador) => (
              <Card
                key={colaborador.colaborador_user_id}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  colaboradorSelecionado?.colaborador_user_id === colaborador.colaborador_user_id
                    ? 'ring-2 ring-[var(--primary)] bg-[var(--primary-light)]'
                    : 'hover:ring-1 hover:ring-[var(--primary-light)]'
                }`}
                onClick={() => onSelecionarColaborador({
                  colaborador_user_id: colaborador.colaborador_user_id,
                  name: colaborador.name
                })}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    {/* Avatar do colaborador */}
                    <div className="w-12 h-12 bg-gradient-to-br from-[var(--secondary)] to-[var(--secondary-dark)] rounded-full flex items-center justify-center">
                      <span className="text-white font-medium text-lg">
                        {colaborador.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="flex-1">
                      <h5 className="font-medium text-[var(--text-primary)] mb-1">
                        {colaborador.name}
                      </h5>
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-[var(--success-light)] text-[var(--success)] px-2 py-1 rounded">
                          Disponível
                        </span>
                      </div>
                    </div>

                    {colaboradorSelecionado?.colaborador_user_id === colaborador.colaborador_user_id && (
                      <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <p className="text-[var(--text-secondary)] text-lg">Nenhum profissional disponível</p>
          <p className="text-[var(--text-secondary)] text-sm mt-2">
            Entre em contato com o estabelecimento para mais informações
          </p>
        </div>
      )}

      {/* Colaborador selecionado - resumo */}
      {colaboradorSelecionado && (
        <Card className="bg-[var(--primary-light)] border-[var(--primary)]">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-[var(--primary)] rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)]">
                  Profissional Selecionado: {colaboradorSelecionado.name}
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  Agendamento será feito especificamente com este profissional
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Informação sobre seleção automática */}
      {!colaboradorSelecionado && permitirQualquerColaborador && (
        <Card className="bg-[var(--info-light)] border-[var(--info)]">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-[var(--info)] rounded-full flex items-center justify-center mt-0.5">
                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)] mb-1">
                  Seleção Automática Ativada
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  O sistema escolherá automaticamente o melhor profissional disponível no horário que você selecionar. 
                  Isso oferece mais opções de horários disponíveis.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
