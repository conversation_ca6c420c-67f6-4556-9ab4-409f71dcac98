import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { AtualizarComboData, ComboApiResponse } from '@/types/combos';
import { validarCombo } from '@/utils/combos';

// GET - Buscar combo específico
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const comboId = parseInt(id);

    if (isNaN(comboId)) {
      return NextResponse.json(
        { success: false, error: 'ID do combo inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usu<PERSON>rio não autenticado' },
        { status: 401 }
      );
    }

    // Buscar combo com itens
    const { data: combo, error: comboError } = await supabase
      .from('combos_servicos')
      .select(`
        *,
        combo_itens (
          *,
          servicos (
            servico_id,
            nome_servico,
            descricao,
            preco,
            duracao_minutos,
            categoria
          )
        )
      `)
      .eq('combo_id', comboId)
      .single();

    if (comboError || !combo) {
      return NextResponse.json(
        { success: false, error: 'Combo não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se o combo pertence à empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .eq('empresa_id', combo.empresa_id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Transformar dados
    const itens = combo.combo_itens?.map((item: any) => ({
      ...item,
      servico: item.servicos
    })) || [];

    const valor_original = itens.reduce((total: number, item: any) => {
      return total + (item.servico.preco * item.quantidade);
    }, 0);

    let valor_com_desconto = valor_original;
    if (combo.desconto_valor_fixo && combo.desconto_valor_fixo > 0) {
      valor_com_desconto = Math.max(0, valor_original - combo.desconto_valor_fixo);
    } else if (combo.desconto_percentual && combo.desconto_percentual > 0) {
      const desconto = (valor_original * combo.desconto_percentual) / 100;
      valor_com_desconto = Math.max(0, valor_original - desconto);
    }

    const economia = valor_original - valor_com_desconto;

    const comboCompleto = {
      ...combo,
      itens,
      valor_original,
      valor_com_desconto,
      economia
    };

    const response: ComboApiResponse = {
      success: true,
      data: comboCompleto
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Erro na API de combo:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PATCH - Atualizar combo
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const comboId = parseInt(id);

    if (isNaN(comboId)) {
      return NextResponse.json(
        { success: false, error: 'ID do combo inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar combo existente
    const { data: comboExistente, error: comboError } = await supabase
      .from('combos_servicos')
      .select('combo_id, empresa_id')
      .eq('combo_id', comboId)
      .single();

    if (comboError || !comboExistente) {
      return NextResponse.json(
        { success: false, error: 'Combo não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se o combo pertence à empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .eq('empresa_id', comboExistente.empresa_id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Extrair dados do corpo da requisição
    const dadosAtualizacao: AtualizarComboData = await request.json();

    // Validar dados se fornecidos
    if (Object.keys(dadosAtualizacao).length > 1 || !('ativo' in dadosAtualizacao)) {
      const validacao = validarCombo(dadosAtualizacao);
      if (!validacao.valido) {
        return NextResponse.json(
          { success: false, error: validacao.erros.join(', ') },
          { status: 400 }
        );
      }
    }

    // Atualizar combo
    const dadosCombo: any = {};
    if (dadosAtualizacao.nome_combo !== undefined) dadosCombo.nome_combo = dadosAtualizacao.nome_combo;
    if (dadosAtualizacao.descricao !== undefined) dadosCombo.descricao = dadosAtualizacao.descricao;
    if (dadosAtualizacao.desconto_valor_fixo !== undefined) dadosCombo.desconto_valor_fixo = dadosAtualizacao.desconto_valor_fixo;
    if (dadosAtualizacao.desconto_percentual !== undefined) dadosCombo.desconto_percentual = dadosAtualizacao.desconto_percentual;
    if (dadosAtualizacao.ativo !== undefined) dadosCombo.ativo = dadosAtualizacao.ativo;
    if (dadosAtualizacao.data_inicio !== undefined) dadosCombo.data_inicio = dadosAtualizacao.data_inicio;
    if (dadosAtualizacao.data_fim !== undefined) dadosCombo.data_fim = dadosAtualizacao.data_fim;
    if (dadosAtualizacao.limite_usos !== undefined) dadosCombo.limite_usos = dadosAtualizacao.limite_usos;

    const { data: comboAtualizado, error: updateError } = await supabase
      .from('combos_servicos')
      .update(dadosCombo)
      .eq('combo_id', comboId)
      .select()
      .single();

    if (updateError) {
      console.error('Erro ao atualizar combo:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao atualizar combo' },
        { status: 500 }
      );
    }

    // Atualizar itens se fornecidos
    if (dadosAtualizacao.itens) {
      // Remover itens existentes
      await supabase
        .from('combo_itens')
        .delete()
        .eq('combo_id', comboId);

      // Verificar se os serviços existem
      const servicosIds = dadosAtualizacao.itens.map(item => item.servico_id);
      const { data: servicos, error: servicosError } = await supabase
        .from('servicos')
        .select('servico_id')
        .eq('empresa_id', empresa.empresa_id)
        .in('servico_id', servicosIds)
        .eq('ativo', true);

      if (servicosError || !servicos || servicos.length !== servicosIds.length) {
        return NextResponse.json(
          { success: false, error: 'Um ou mais serviços não foram encontrados' },
          { status: 400 }
        );
      }

      // Criar novos itens
      const novosItens = dadosAtualizacao.itens.map(item => ({
        combo_id: comboId,
        servico_id: item.servico_id,
        quantidade: item.quantidade,
        ordem_execucao: item.ordem_execucao,
        obrigatorio: item.obrigatorio
      }));

      const { error: itensError } = await supabase
        .from('combo_itens')
        .insert(novosItens);

      if (itensError) {
        console.error('Erro ao atualizar itens do combo:', itensError);
        return NextResponse.json(
          { success: false, error: 'Erro ao atualizar itens do combo' },
          { status: 500 }
        );
      }
    }

    const response: ComboApiResponse = {
      success: true,
      data: comboAtualizado,
      message: 'Combo atualizado com sucesso'
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Erro na API de combo:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Excluir combo
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();
    const { id } = await params;
    const comboId = parseInt(id);

    if (isNaN(comboId)) {
      return NextResponse.json(
        { success: false, error: 'ID do combo inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar combo existente
    const { data: combo, error: comboError } = await supabase
      .from('combos_servicos')
      .select('combo_id, empresa_id, nome_combo')
      .eq('combo_id', comboId)
      .single();

    if (comboError || !combo) {
      return NextResponse.json(
        { success: false, error: 'Combo não encontrado' },
        { status: 404 }
      );
    }

    // Verificar se o combo pertence à empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id')
      .eq('proprietario_user_id', user.id)
      .eq('empresa_id', combo.empresa_id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Excluir combo (os itens serão excluídos automaticamente por CASCADE)
    const { error: deleteError } = await supabase
      .from('combos_servicos')
      .delete()
      .eq('combo_id', comboId);

    if (deleteError) {
      console.error('Erro ao excluir combo:', deleteError);
      return NextResponse.json(
        { success: false, error: 'Erro ao excluir combo' },
        { status: 500 }
      );
    }

    const response: ComboApiResponse = {
      success: true,
      message: `Combo "${combo.nome_combo}" excluído com sucesso`
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Erro na API de combo:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
