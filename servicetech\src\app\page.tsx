import { Layout } from "@/components/layout/Layout";
import { ClientButtons } from "@/components/ClientButtons";
import { CTABottomButtons } from "@/components/CTABottomButtons";
import { <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardContent } from "@/components/ui/Card";

export default function Home() {
  return (
    <Layout>
      <main id="main-content" tabIndex={-1}>
        {/* Hero Section */}
        <section
          className="w-full py-20 md:py-32 lg:py-40 bg-gradient-to-r from-blue-600 to-purple-700 text-white text-center"
          aria-labelledby="hero-title"
        >
          <div className="container mx-auto px-4">
            <h1
              id="hero-title"
              className="text-4xl md:text-6xl font-bold mb-4 leading-tight"
            >
              ServiceTech: Conectando Você aos Melhores Serviços Pessoais
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              <PERSON><PERSON>, gerencie e descubra estabelecimentos de beleza, bem-estar e muito mais, com facilidade e eficiência.
            </p>
            <ClientButtons />
          </div>
        </section>

        {/* Explanation Section */}
        <section
          className="w-full py-16 md:py-24 bg-[var(--background)] text-center"
          aria-labelledby="explanation-title"
        >
          <div className="container mx-auto px-4">
            <h2
              id="explanation-title"
              className="text-3xl md:text-4xl font-bold mb-12 text-[var(--text-primary)]"
            >
              Como o ServiceTech Transforma Seu Dia
            </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-[var(--primary)]">Para Estabelecimentos</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg text-[var(--text-secondary)]">
                  Otimize sua gestão de agendamentos, equipe e finanças. Aumente sua visibilidade e atraia mais clientes com ferramentas de marketing integradas. Foco total no seu negócio, nós cuidamos da tecnologia.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-[var(--accent)]">Para Usuários Finais</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg text-[var(--text-secondary)]">
                  Encontre facilmente os melhores profissionais e serviços perto de você. Agende com poucos cliques, receba lembretes e gerencie seus compromissos de forma prática e segura. Sua beleza e bem-estar na palma da mão.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section (Mockups Iniciais) */}
      <section className="w-full py-16 md:py-24 bg-[var(--surface)] text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-[var(--text-primary)]">
            O Que Nossos Usuários Dizem
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="pt-6"> {/* Adjusted padding as there's no CardHeader */}
                <p className="text-lg italic text-[var(--text-secondary)] mb-4">
                  "O ServiceTech revolucionou a forma como gerencio minha barbearia. Agendamentos mais fáceis e clientes mais satisfeitos!"
                </p>
                <p className="font-semibold text-[var(--primary)]">- João, Proprietário de Barbearia</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <p className="text-lg italic text-[var(--text-secondary)] mb-4">
                  "Nunca foi tão simples encontrar e agendar meu salão de beleza favorito. Adoro a praticidade!"
                </p>
                <p className="font-semibold text-[var(--accent)]">- Maria, Cliente</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <p className="text-lg italic text-[var(--text-secondary)] mb-4">
                  "A ferramenta de gestão de equipe é incrível. Consigo organizar tudo sem dores de cabeça."
                </p>
                <p className="font-semibold text-[var(--primary)]">- Ana, Gerente de Clínica de Estética</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section - Bottom */}
      <section className="w-full py-16 md:py-24 bg-gradient-to-r from-purple-700 to-blue-600 text-white text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-8">
            Pronto para Transformar Seu Negócio ou Sua Rotina?
          </h2>
          <CTABottomButtons />
        </div>
      </section>
      </main>
    </Layout>
  );
}
