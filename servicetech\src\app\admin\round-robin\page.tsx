/**
 * Página de demonstração e teste do sistema Round-Robin
 * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin
 * 
 * Esta página permite aos administradores:
 * - Visualizar estatísticas de round-robin por empresa
 * - Testar a lógica de seleção de colaboradores
 * - Monitorar a distribuição de agendamentos
 */

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { EstatisticasRoundRobin } from '@/components/agendamentos/EstatisticasRoundRobin';
import { createClient } from '@/utils/supabase/client';

interface Empresa {
  empresa_id: number;
  nome_empresa: string;
  status: string;
}

interface Servico {
  servico_id: number;
  nome_servico: string;
  ativo: boolean;
}

export default function RoundRobinAdminPage() {
  const [empresas, setEmpresas] = useState<Empresa[]>([]);
  const [servicos, setServicos] = useState<Servico[]>([]);
  const [empresaSelecionada, setEmpresaSelecionada] = useState<number | null>(null);
  const [servicoSelecionado, setServicoSelecionado] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Buscar empresas ativas
  useEffect(() => {
    async function buscarEmpresas() {
      try {
        const supabase = createClient();
        
        const { data, error } = await supabase
          .from('empresas')
          .select('empresa_id, nome_empresa, status')
          .eq('status', 'ativo')
          .order('nome_empresa');

        if (error) throw error;

        setEmpresas(data || []);
        
        // Selecionar primeira empresa por padrão
        if (data && data.length > 0) {
          setEmpresaSelecionada(data[0].empresa_id);
        }

      } catch (err: unknown) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    buscarEmpresas();
  }, []);

  // Buscar serviços da empresa selecionada
  useEffect(() => {
    async function buscarServicos() {
      if (!empresaSelecionada) {
        setServicos([]);
        return;
      }

      try {
        const supabase = createClient();

        const { data, error } = await supabase
          .from('servicos')
          .select('servico_id, nome_servico, ativo')
          .eq('empresa_id', empresaSelecionada)
          .eq('ativo', true)
          .order('nome_servico');

        if (error) throw error;

        setServicos(data || []);
        setServicoSelecionado(null); // Reset seleção de serviço

      } catch (err: unknown) {
        console.error('Erro ao buscar serviços:', err);
        setServicos([]);
      }
    }

    buscarServicos();
  }, [empresaSelecionada]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary)] mx-auto mb-4"></div>
            <p className="text-[var(--text-secondary)]">Carregando dados...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-red-500 mb-2">❌ Erro ao carregar dados</div>
            <p className="text-[var(--text-secondary)]">{error}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const empresaAtual = empresas.find(e => e.empresa_id === empresaSelecionada);
  const servicoAtual = servicos.find(s => s.servico_id === servicoSelecionado);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-[var(--text-primary)] mb-2">
          🎯 Sistema Round-Robin
        </h1>
        <p className="text-[var(--text-secondary)]">
          Monitoramento e análise da distribuição de agendamentos entre colaboradores
        </p>
      </div>

      {/* Seletores */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filtros de Análise</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Seletor de Empresa */}
            <div>
              <label className="block text-sm font-medium mb-2">Empresa</label>
              <select
                value={empresaSelecionada || ''}
                onChange={(e) => setEmpresaSelecionada(Number(e.target.value) || null)}
                className="w-full px-3 py-2 border border-[var(--border)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
              >
                <option value="">Selecione uma empresa</option>
                {empresas.map((empresa) => (
                  <option key={empresa.empresa_id} value={empresa.empresa_id}>
                    {empresa.nome_empresa}
                  </option>
                ))}
              </select>
            </div>

            {/* Seletor de Serviço */}
            <div>
              <label className="block text-sm font-medium mb-2">
                Serviço (opcional)
              </label>
              <select
                value={servicoSelecionado || ''}
                onChange={(e) => setServicoSelecionado(Number(e.target.value) || null)}
                className="w-full px-3 py-2 border border-[var(--border)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                disabled={!empresaSelecionada || servicos.length === 0}
              >
                <option value="">Todos os serviços</option>
                {servicos.map((servico) => (
                  <option key={servico.servico_id} value={servico.servico_id}>
                    {servico.nome_servico}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {empresaAtual && (
            <div className="mt-4 p-3 bg-[var(--surface)] rounded-lg">
              <p className="text-sm">
                <strong>Empresa selecionada:</strong> {empresaAtual.nome_empresa}
                {servicoAtual && (
                  <>
                    <br />
                    <strong>Serviço:</strong> {servicoAtual.nome_servico}
                  </>
                )}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Estatísticas Round-Robin */}
      {empresaSelecionada ? (
        <EstatisticasRoundRobin
          empresa_id={empresaSelecionada}
          servico_id={servicoSelecionado || undefined}
        />
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="text-[var(--text-secondary)]">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-lg font-medium mb-2">Selecione uma empresa</h3>
              <p>Escolha uma empresa acima para visualizar as estatísticas de distribuição de agendamentos.</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Informações sobre o Sistema */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>ℹ️ Como funciona o Round-Robin</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Algoritmo de Seleção:</h4>
              <ul className="list-disc list-inside space-y-1 text-[var(--text-secondary)]">
                <li>Prioriza colaboradores com <strong>menos agendamentos</strong> no período</li>
                <li>Em caso de empate, prioriza quem teve o <strong>último agendamento mais antigo</strong></li>
                <li>Como último critério, usa <strong>ordem alfabética</strong> para consistência</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Benefícios:</h4>
              <ul className="list-disc list-inside space-y-1 text-[var(--text-secondary)]">
                <li><strong>Distribuição equitativa</strong> de trabalho entre colaboradores</li>
                <li><strong>Redução de sobrecarga</strong> em colaboradores específicos</li>
                <li><strong>Melhoria na satisfação</strong> da equipe</li>
                <li><strong>Otimização da utilização</strong> de recursos humanos</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Monitoramento:</h4>
              <ul className="list-disc list-inside space-y-1 text-[var(--text-secondary)]">
                <li>Acompanhe a <strong>diferença máxima</strong> entre colaboradores</li>
                <li>Verifique se a distribuição está <strong>equilibrada</strong></li>
                <li>Analise as <strong>recomendações</strong> para melhorias</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
