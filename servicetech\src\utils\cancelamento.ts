import { 
  PoliticaCancelamento, 
  CalculoReembolso, 
  RegrasCancelamento,
  POLITICA_CANCELAMENTO_PADRAO 
} from '@/types/politicas';
import { StatusAgendamento } from '@/types/agendamentos';

/**
 * Utilitários para cálculo de cancelamento e reembolso
 */
export class CancelamentoUtils {
  
  /**
   * Calcular regras de cancelamento aplicáveis
   */
  static calcularRegrasCancelamento(
    dataHoraAgendamento: string,
    statusAgendamento: StatusAgendamento,
    politica: PoliticaCancelamento,
    canceladoPor: 'cliente' | 'empresa'
  ): RegrasCancelamento {
    
    const agora = new Date();
    const dataAgendamento = new Date(dataHoraAgendamento);
    const horasAteAgendamento = (dataAgendamento.getTime() - agora.getTime()) / (1000 * 60 * 60);
    const agendamentoConfirmado = statusAgendamento === 'Confirmado';
    
    // Se cancelamento é pela empresa, sempre pode cancelar com 100% reembolso
    if (canceladoPor === 'empresa') {
      return {
        pode_cancelar: true,
        prazo_expirado: false,
        horas_ate_agendamento: horasAteAgendamento,
        agendamento_confirmado: agendamentoConfirmado,
        percentual_reembolso: politica.empresa.percentual_reembolso,
        motivo_regra: 'Cancelamento pela empresa - reembolso integral'
      };
    }
    
    // Verificar se cliente pode cancelar
    if (!politica.configuracoes.permitir_cancelamento_cliente) {
      return {
        pode_cancelar: false,
        prazo_expirado: true,
        horas_ate_agendamento: horasAteAgendamento,
        agendamento_confirmado: agendamentoConfirmado,
        percentual_reembolso: 0,
        motivo_regra: 'Cancelamento pelo cliente não permitido'
      };
    }
    
    // Verificar prazo mínimo para cancelamento
    const prazoMinimo = politica.configuracoes.prazo_cancelamento_cliente;
    if (horasAteAgendamento < prazoMinimo) {
      return {
        pode_cancelar: false,
        prazo_expirado: true,
        horas_ate_agendamento: horasAteAgendamento,
        agendamento_confirmado: agendamentoConfirmado,
        percentual_reembolso: 0,
        motivo_regra: `Prazo mínimo de ${prazoMinimo}h para cancelamento expirado`
      };
    }
    
    // Aplicar regras baseadas no tempo e status
    let percentualReembolso = 0;
    let motivoRegra = '';
    
    // Cancelamento com mais de 24h e não confirmado
    if (horasAteAgendamento > 24 && !agendamentoConfirmado) {
      percentualReembolso = politica.cliente.antecedencia_24h_nao_confirmado.percentual_reembolso;
      motivoRegra = 'Cancelamento com mais de 24h de antecedência (não confirmado)';
    }
    // Cancelamento com menos de 24h e confirmado
    else if (horasAteAgendamento <= 24 && agendamentoConfirmado) {
      percentualReembolso = politica.cliente.antecedencia_24h_confirmado.percentual_reembolso;
      motivoRegra = 'Cancelamento com menos de 24h de antecedência (confirmado)';
    }
    // Cancelamento no mesmo dia
    else if (horasAteAgendamento <= 24) {
      percentualReembolso = politica.cliente.mesmo_dia.percentual_reembolso;
      motivoRegra = 'Cancelamento no mesmo dia';
    }
    // Caso padrão - mais de 24h
    else {
      percentualReembolso = politica.cliente.antecedencia_24h_nao_confirmado.percentual_reembolso;
      motivoRegra = 'Cancelamento com antecedência';
    }
    
    return {
      pode_cancelar: true,
      prazo_expirado: false,
      horas_ate_agendamento: horasAteAgendamento,
      agendamento_confirmado: agendamentoConfirmado,
      percentual_reembolso: percentualReembolso,
      motivo_regra: motivoRegra
    };
  }
  
  /**
   * Calcular valor do reembolso
   */
  static calcularReembolso(
    valorOriginal: number,
    percentualReembolso: number,
    motivoRegra: string
  ): CalculoReembolso {
    
    const valorReembolso = (valorOriginal * percentualReembolso) / 100;
    const taxaAplicada = valorOriginal - valorReembolso;
    
    return {
      valor_original: valorOriginal,
      percentual_reembolso: percentualReembolso,
      valor_reembolso: valorReembolso,
      taxa_aplicada: taxaAplicada,
      motivo: motivoRegra,
      pode_reembolsar: valorReembolso > 0
    };
  }
  
  /**
   * Validar política de cancelamento
   */
  static validarPolitica(politica: Partial<PoliticaCancelamento>): {
    valida: boolean;
    erros: string[];
  } {
    const erros: string[] = [];
    
    // Validar percentuais
    if (politica.cliente?.antecedencia_24h_confirmado?.percentual_reembolso !== undefined) {
      const percentual = politica.cliente.antecedencia_24h_confirmado.percentual_reembolso;
      if (percentual < 0 || percentual > 100) {
        erros.push('Percentual de reembolso deve estar entre 0% e 100%');
      }
    }
    
    if (politica.cliente?.mesmo_dia?.percentual_reembolso !== undefined) {
      const percentual = politica.cliente.mesmo_dia.percentual_reembolso;
      if (percentual < 0 || percentual > 100) {
        erros.push('Percentual de reembolso para mesmo dia deve estar entre 0% e 100%');
      }
    }
    
    // Validar prazo de cancelamento
    if (politica.configuracoes?.prazo_cancelamento_cliente !== undefined) {
      const prazo = politica.configuracoes.prazo_cancelamento_cliente;
      if (prazo < 0 || prazo > 72) {
        erros.push('Prazo de cancelamento deve estar entre 0 e 72 horas');
      }
    }
    
    return {
      valida: erros.length === 0,
      erros
    };
  }
  
  /**
   * Mesclar política com padrão
   */
  static mesclarComPadrao(politicaPersonalizada?: Partial<PoliticaCancelamento>): PoliticaCancelamento {
    if (!politicaPersonalizada) {
      return { ...POLITICA_CANCELAMENTO_PADRAO };
    }
    
    return {
      cliente: {
        antecedencia_24h_nao_confirmado: {
          ...POLITICA_CANCELAMENTO_PADRAO.cliente.antecedencia_24h_nao_confirmado,
          ...politicaPersonalizada.cliente?.antecedencia_24h_nao_confirmado
        },
        antecedencia_24h_confirmado: {
          ...POLITICA_CANCELAMENTO_PADRAO.cliente.antecedencia_24h_confirmado,
          ...politicaPersonalizada.cliente?.antecedencia_24h_confirmado
        },
        mesmo_dia: {
          ...POLITICA_CANCELAMENTO_PADRAO.cliente.mesmo_dia,
          ...politicaPersonalizada.cliente?.mesmo_dia
        }
      },
      empresa: {
        ...POLITICA_CANCELAMENTO_PADRAO.empresa,
        ...politicaPersonalizada.empresa
      },
      configuracoes: {
        ...POLITICA_CANCELAMENTO_PADRAO.configuracoes,
        ...politicaPersonalizada.configuracoes
      },
      created_at: politicaPersonalizada.created_at || POLITICA_CANCELAMENTO_PADRAO.created_at,
      updated_at: new Date().toISOString()
    };
  }
  
  /**
   * Formatar tempo restante para cancelamento
   */
  static formatarTempoRestante(horasRestantes: number): string {
    if (horasRestantes < 0) {
      return 'Prazo expirado';
    }
    
    if (horasRestantes < 1) {
      const minutos = Math.floor(horasRestantes * 60);
      return `${minutos} minuto${minutos !== 1 ? 's' : ''}`;
    }
    
    if (horasRestantes < 24) {
      const horas = Math.floor(horasRestantes);
      const minutos = Math.floor((horasRestantes - horas) * 60);
      return `${horas}h${minutos > 0 ? ` ${minutos}min` : ''}`;
    }
    
    const dias = Math.floor(horasRestantes / 24);
    const horas = Math.floor(horasRestantes % 24);
    return `${dias} dia${dias !== 1 ? 's' : ''}${horas > 0 ? ` ${horas}h` : ''}`;
  }
}
