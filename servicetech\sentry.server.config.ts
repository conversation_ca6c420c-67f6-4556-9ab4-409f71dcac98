/**
 * Configuração do Sentry para o Servidor (Node.js)
 */

import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  
  // Configurações de ambiente
  environment: process.env.NODE_ENV ?? 'development',
  release: process.env.NEXT_PUBLIC_APP_VERSION ?? '1.0.0',
  
  // Configurações de performance
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // Configurações de debug
  debug: process.env.NODE_ENV === 'development',
  
  // Filtrar dados sensíveis
  beforeSend(event, hint) {
    // Lista de campos sensíveis para filtrar
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'cookie', 'session', 'credit_card', 'ssn', 'cpf', 'cnpj',
      'stripe_secret', 'supabase_key', 'jwt', 'bearer'
    ];

    // Função para filtrar dados sensíveis
    const filterSensitiveData = (obj: any): any => {
      if (!obj || typeof obj !== 'object') return obj;

      const filtered = Array.isArray(obj) ? [] : {};
      
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = sensitiveFields.some(field => lowerKey.includes(field));
        
        if (isSensitive) {
          (filtered as any)[key] = '[FILTERED]';
        } else if (typeof value === 'object' && value !== null) {
          (filtered as any)[key] = filterSensitiveData(value);
        } else {
          (filtered as any)[key] = value;
        }
      }
      
      return filtered;
    };

    // Filtrar dados do evento
    if (event.request?.data) {
      event.request.data = filterSensitiveData(event.request.data);
    }
    
    if (event.extra) {
      event.extra = filterSensitiveData(event.extra);
    }

    // Filtrar headers HTTP
    if (event.request?.headers) {
      event.request.headers = filterSensitiveData(event.request.headers);
    }

    // Filtrar variáveis de ambiente
    if (event.contexts?.runtime?.env) {
      event.contexts.runtime.env = filterSensitiveData(event.contexts.runtime.env);
    }

    return event;
  },

  // Configurações de integração (usar integrações padrão do Next.js)

  // Configurações de contexto inicial
  initialScope: {
    tags: {
      component: 'server',
      platform: 'node'
    }
  },

  // Configurações de erro
  ignoreErrors: [
    // Erros comuns do Node.js que não são relevantes
    'ECONNRESET',
    'ENOTFOUND',
    'ECONNREFUSED',
    'ETIMEDOUT',
    // Erros de rate limiting
    'Rate limit exceeded',
    // Erros de validação que já são tratados
    'ValidationError',
  ],

  // Configurações de breadcrumbs
  maxBreadcrumbs: 100,

  // Configurações de contexto
  attachStacktrace: true,
  sendDefaultPii: false, // Não enviar informações pessoais por padrão

  // Configurações específicas do servidor
  serverName: process.env.SERVER_NAME ?? 'servicetech-server',
  
  // Configurações de transporte (padrão do Node.js)

  // Configurações de profiling (apenas em produção)
  profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0.0,
});
