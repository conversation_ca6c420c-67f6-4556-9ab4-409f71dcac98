import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { CriarServicoData, LIMITES_SERVICOS } from '@/types/servicos';

// GET - Listar serviços da empresa do usuário
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem gerenciar serviços.' },
        { status: 403 }
      );
    }

    // Buscar empresa do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, plano_saas_id')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Buscar parâmetros de filtro
    const { searchParams } = new URL(request.url);
    const ativo = searchParams.get('ativo');
    const categoria = searchParams.get('categoria');
    const busca = searchParams.get('busca');

    // Construir query
    let query = supabase
      .from('servicos')
      .select('*')
      .eq('empresa_id', empresa.empresa_id)
      .order('created_at', { ascending: false });

    // Aplicar filtros
    if (ativo !== null) {
      query = query.eq('ativo', ativo === 'true');
    }
    if (categoria) {
      query = query.eq('categoria', categoria);
    }
    if (busca) {
      query = query.or(`nome_servico.ilike.%${busca}%,descricao.ilike.%${busca}%`);
    }

    const { data: servicos, error: servicosError } = await query;

    if (servicosError) {
      console.error('Erro ao buscar serviços:', servicosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar serviços' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: servicos,
      empresa_id: empresa.empresa_id
    });

  } catch (error: any) {
    console.error('Erro geral na API de serviços:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// POST - Criar novo serviço
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem criar serviços.' },
        { status: 403 }
      );
    }

    // Buscar empresa e plano do usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        planos_saas!inner(nome_plano)
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar limite de serviços
    const { data: servicosExistentes, error: countError } = await supabase
      .from('servicos')
      .select('servico_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true);

    if (countError) {
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar limite de serviços' },
        { status: 500 }
      );
    }

    const plano = Array.isArray(empresa.planos_saas) ? empresa.planos_saas[0] : empresa.planos_saas;
    const planoNome = plano?.nome_plano?.toLowerCase() || 'essencial';
    const limite = planoNome === 'premium' ? LIMITES_SERVICOS.premium : LIMITES_SERVICOS.essencial;
    
    if (servicosExistentes.length >= limite) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Limite de ${limite} serviços atingido para o plano ${planoNome}. Considere fazer upgrade para o plano Premium.` 
        },
        { status: 400 }
      );
    }

    // Validar dados do corpo da requisição
    const body: CriarServicoData = await request.json();
    
    // Validações básicas
    if (!body.nome_servico || body.nome_servico.trim().length < 2) {
      return NextResponse.json(
        { success: false, error: 'Nome do serviço deve ter pelo menos 2 caracteres' },
        { status: 400 }
      );
    }

    if (!body.duracao_minutos || body.duracao_minutos < 15) {
      return NextResponse.json(
        { success: false, error: 'Duração deve ser de pelo menos 15 minutos' },
        { status: 400 }
      );
    }

    if (!body.preco || body.preco < 0) {
      return NextResponse.json(
        { success: false, error: 'Preço deve ser maior ou igual a zero' },
        { status: 400 }
      );
    }

    // Criar serviço
    const { data: novoServico, error: criarError } = await supabase
      .from('servicos')
      .insert([{
        empresa_id: empresa.empresa_id,
        nome_servico: body.nome_servico.trim(),
        descricao: body.descricao?.trim() || '',
        duracao_minutos: body.duracao_minutos,
        preco: body.preco,
        categoria: body.categoria || 'Outros',
        ativo: body.ativo !== false
      }])
      .select()
      .single();

    if (criarError) {
      console.error('Erro ao criar serviço:', criarError);
      return NextResponse.json(
        { success: false, error: 'Erro ao criar serviço' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: novoServico,
      message: 'Serviço criado com sucesso'
    });

  } catch (error: any) {
    console.error('Erro geral ao criar serviço:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
