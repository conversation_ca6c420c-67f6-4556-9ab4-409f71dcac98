import { NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';
import { EstatisticasBusca } from '@/types/busca';

export async function GET() {
  try {
    // Usar cliente administrativo para bypassa RLS e evitar recursão infinita
    const supabase = createAdminClient();

    // Buscar estatísticas gerais das empresas ativas
    const { data: empresas, error: empresasError } = await supabase
      .from('empresas')
      .select('empresa_id, cidade, estado')
      .eq('status', 'ativo');

    if (empresasError) {
      console.error('Erro ao buscar empresas para estatísticas:', empresasError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar estatísticas' },
        { status: 500 }
      );
    }

    // Buscar categorias e preços dos serviços ativos
    const { data: servicos, error: servicosError } = await supabase
      .from('servicos')
      .select(`
        categoria,
        preco,
        empresas!inner (
          empresa_id,
          status
        )
      `)
      .eq('ativo', true)
      .eq('empresas.status', 'ativo');

    if (servicosError) {
      console.error('Erro ao buscar serviços para estatísticas:', servicosError);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar estatísticas de serviços' },
        { status: 500 }
      );
    }

    // Processar dados
    const cidadesSet = new Set<string>();
    const estadosSet = new Set<string>();
    const categoriasSet = new Set<string>();
    const precos: number[] = [];

    // Processar empresas
    empresas?.forEach(empresa => {
      if (empresa.cidade) cidadesSet.add(empresa.cidade);
      if (empresa.estado) estadosSet.add(empresa.estado);
    });

    // Processar serviços
    servicos?.forEach((servico: any) => {
      if (servico.categoria) categoriasSet.add(servico.categoria);
      if (servico.preco) precos.push(servico.preco);
    });

    // Calcular faixa de preços
    const precoMinimo = precos.length > 0 ? Math.min(...precos) : 0;
    const precoMaximo = precos.length > 0 ? Math.max(...precos) : 0;

    // Montar resposta
    const estatisticas: EstatisticasBusca = {
      total_empresas: empresas?.length || 0,
      cidades_disponiveis: Array.from(cidadesSet).sort(),
      estados_disponiveis: Array.from(estadosSet).sort(),
      categorias_disponiveis: Array.from(categoriasSet).sort(),
      faixa_precos: {
        minimo: precoMinimo,
        maximo: precoMaximo
      }
    };

    return NextResponse.json({
      success: true,
      data: estatisticas
    });

  } catch (error: any) {
    console.error('Erro geral na API de estatísticas de busca:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
