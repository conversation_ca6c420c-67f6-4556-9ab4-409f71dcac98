'use client';

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { BotaoCancelamento } from './BotaoCancelamento';
import { AgendamentoCompleto, StatusAgendamento } from '@/types/agendamentos';
import { format, isAfter, addHours, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface CardAgendamentoProps {
  agendamento: AgendamentoCompleto;
  onConfirmar?: (id: number) => void;
  onRecusar?: (id: number) => void;
  onCancelar?: (id: number) => void;
  onConcluir?: (id: number) => void;
  onMarcarPago?: (id: number) => void;
  onVerDetalhes?: (agendamento: AgendamentoCompleto) => void;
  mostrarAcoes?: boolean;
  loading?: boolean;
  userRole?: string;
}

export function CardAgendamento({
  agendamento,
  onConfirmar,
  onRecusar,
  onCancelar,
  onConcluir,
  onMarcarPago,
  onVerDetalhes,
  mostrarAcoes = true,
  loading = false,
  userRole
}: CardAgendamentoProps) {
  
  // Formatação de data e hora
  const dataHoraInicio = parseISO(agendamento.data_hora_inicio);
  const dataHoraFim = parseISO(agendamento.data_hora_fim);
  const prazoConfirmacao = parseISO(agendamento.prazo_confirmacao);
  const agora = new Date();

  // Verificar se está próximo do prazo
  const proximoPrazo = agendamento.status_agendamento === 'Pendente' && 
    isAfter(agora, addHours(prazoConfirmacao, -2)); // 2 horas antes do prazo

  const prazoExpirado = agendamento.status_agendamento === 'Pendente' && 
    isAfter(agora, prazoConfirmacao);

  // Cores e ícones por status
  const getStatusConfig = (status: StatusAgendamento) => {
    switch (status) {
      case 'Pendente':
        return {
          color: prazoExpirado ? 'text-red-600 bg-red-50 border-red-200' : 
                proximoPrazo ? 'text-orange-600 bg-orange-50 border-orange-200' :
                'text-yellow-600 bg-yellow-50 border-yellow-200',
          icon: '⏳',
          label: prazoExpirado ? 'Expirado' : 'Pendente'
        };
      case 'Confirmado':
        return {
          color: 'text-blue-600 bg-blue-50 border-blue-200',
          icon: '✅',
          label: 'Confirmado'
        };
      case 'Recusado':
        return {
          color: 'text-red-600 bg-red-50 border-red-200',
          icon: '❌',
          label: 'Recusado'
        };
      case 'Cancelado':
        return {
          color: 'text-gray-600 bg-gray-50 border-gray-200',
          icon: '🚫',
          label: 'Cancelado'
        };
      case 'Concluido':
        return {
          color: 'text-green-600 bg-green-50 border-green-200',
          icon: '✨',
          label: 'Concluído'
        };
      default:
        return {
          color: 'text-gray-600 bg-gray-50 border-gray-200',
          icon: '❓',
          label: status
        };
    }
  };

  const statusConfig = getStatusConfig(agendamento.status_agendamento);

  // Verificar quais ações são permitidas
  const podeConfirmar = agendamento.status_agendamento === 'Pendente' && !prazoExpirado &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');
  
  const podeRecusar = agendamento.status_agendamento === 'Pendente' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');
  
  const podeCancelar = ['Pendente', 'Confirmado'].includes(agendamento.status_agendamento);
  
  const podeConcluir = agendamento.status_agendamento === 'Confirmado' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');
  
  const podeMarcarPago = agendamento.forma_pagamento === 'Local' && 
    agendamento.status_pagamento === 'Pendente' &&
    (userRole === 'Proprietario' || userRole === 'Colaborador');

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${proximoPrazo ? 'ring-2 ring-orange-200' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusConfig.color}`}>
                {statusConfig.icon} {statusConfig.label}
              </span>
              {proximoPrazo && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-orange-600 bg-orange-100">
                  ⚠️ Prazo próximo
                </span>
              )}
            </div>
            
            <h3 className="text-lg font-semibold text-[var(--text-primary)]">
              {agendamento.servico.nome_servico}
            </h3>
            
            <div className="text-sm text-[var(--text-secondary)] space-y-1">
              <div className="flex items-center gap-2">
                <span>📅</span>
                <span>
                  {format(dataHoraInicio, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span>🕐</span>
                <span>
                  {format(dataHoraInicio, 'HH:mm')} - {format(dataHoraFim, 'HH:mm')}
                </span>
              </div>
              {userRole === 'Usuario' ? (
                // Para clientes, mostrar empresa e colaborador
                <>
                  <div className="flex items-center gap-2">
                    <span>🏢</span>
                    <span>{agendamento.empresa?.nome_empresa || 'Empresa não identificada'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>👨‍💼</span>
                    <span>{agendamento.colaborador?.name || 'Colaborador não identificado'}</span>
                  </div>
                </>
              ) : (
                // Para proprietários e colaboradores, mostrar cliente
                <>
                  <div className="flex items-center gap-2">
                    <span>👤</span>
                    <span>{agendamento.cliente?.name || 'Cliente não identificado'}</span>
                  </div>
                  {userRole === 'Proprietario' && (
                    <div className="flex items-center gap-2">
                      <span>👨‍💼</span>
                      <span>{agendamento.colaborador?.name || 'Colaborador não identificado'}</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-lg font-bold text-[var(--primary)]">
              R$ {agendamento.valor_total.toFixed(2)}
            </div>
            <div className="text-xs text-[var(--text-secondary)]">
              {agendamento.forma_pagamento}
            </div>
            {agendamento.forma_pagamento === 'Local' && (
              <div className={`text-xs ${
                agendamento.status_pagamento === 'Pago' 
                  ? 'text-green-600' 
                  : 'text-orange-600'
              }`}>
                {agendamento.status_pagamento === 'Pago' ? '✅ Pago' : '⏳ Pendente'}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {agendamento.observacoes_cliente && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-sm font-medium text-[var(--text-primary)] mb-1">
              Observações do cliente:
            </div>
            <div className="text-sm text-[var(--text-secondary)]">
              {agendamento.observacoes_cliente}
            </div>
          </div>
        )}

        {agendamento.status_agendamento === 'Pendente' && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              <strong>Prazo para confirmação:</strong>{' '}
              {format(prazoConfirmacao, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
            </div>
            {proximoPrazo && (
              <div className="text-xs text-orange-600 mt-1">
                ⚠️ Confirme ou recuse em breve para evitar cancelamento automático
              </div>
            )}
          </div>
        )}

        {mostrarAcoes && (
          <div className="flex flex-wrap gap-2">
            {podeConfirmar && onConfirmar && (
              <Button
                size="sm"
                onClick={() => onConfirmar(agendamento.agendamento_id)}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700"
              >
                ✅ Confirmar
              </Button>
            )}
            
            {podeRecusar && onRecusar && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onRecusar(agendamento.agendamento_id)}
                disabled={loading}
                className="border-red-300 text-red-600 hover:bg-red-50"
              >
                ❌ Recusar
              </Button>
            )}
            
            {podeConcluir && onConcluir && (
              <Button
                size="sm"
                onClick={() => onConcluir(agendamento.agendamento_id)}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                ✨ Concluir
              </Button>
            )}
            
            {podeMarcarPago && onMarcarPago && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onMarcarPago(agendamento.agendamento_id)}
                disabled={loading}
                className="border-green-300 text-green-600 hover:bg-green-50"
              >
                💰 Marcar como Pago
              </Button>
            )}
            
            {podeCancelar && (
              <BotaoCancelamento
                agendamento={agendamento}
                onCancelado={() => {
                  // Callback para quando o agendamento for cancelado
                  if (onCancelar) {
                    onCancelar(agendamento.agendamento_id);
                  }
                }}
                size="sm"
                variant="outline"
                className="border-gray-300 text-gray-600 hover:bg-gray-50"
              />
            )}
            
            {onVerDetalhes && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onVerDetalhes(agendamento)}
                disabled={loading}
              >
                👁️ Ver Detalhes
              </Button>
            )}
          </div>
        )}

        {agendamento.codigo_confirmacao && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-[var(--text-secondary)]">
              <strong>Código:</strong> {agendamento.codigo_confirmacao}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
