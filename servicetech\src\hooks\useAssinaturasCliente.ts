import { useState, useEffect, useCallback } from 'react';
import { 
  PlanoAssinaturaExpandido,
  BeneficiosAssinatura,
  AssinarPlanoData,
  UseAssinaturasResult 
} from '@/types/planosAssinatura';

export function useAssinaturasCliente(): UseAssinaturasResult {
  const [assinaturas, setAssinaturas] = useState<PlanoAssinaturaExpandido[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Buscar assinaturas do cliente
  const buscarAssinaturas = useCallback(async (filtros?: {
    empresa_id?: number;
    servico_id?: number;
    status?: string;
    apenas_ativos?: boolean;
  }) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (filtros?.empresa_id) params.append('empresa_id', filtros.empresa_id.toString());
      if (filtros?.servico_id) params.append('servico_id', filtros.servico_id.toString());
      if (filtros?.status) params.append('status', filtros.status);
      if (filtros?.apenas_ativos) params.append('apenas_ativos', 'true');

      const response = await fetch(`/api/assinaturas-cliente?${params.toString()}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar assinaturas');
      }

      setAssinaturas(data.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao buscar assinaturas:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Criar nova assinatura
  const criarAssinatura = useCallback(async (dadosAssinatura: AssinarPlanoData): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/assinaturas-cliente', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAssinatura),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao criar assinatura');
      }

      // Se retornou client_secret, é necessário completar pagamento
      if (data.client_secret) {
        // Aqui você pode integrar com Stripe Elements para completar o pagamento
        console.log('Client secret para pagamento:', data.client_secret);
      }

      // Atualizar lista de assinaturas
      await buscarAssinaturas();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao criar assinatura:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarAssinaturas]);

  // Cancelar assinatura
  const cancelarAssinatura = useCallback(async (id: number, motivo?: string): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/assinaturas-cliente/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'cancelar', motivo }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao cancelar assinatura');
      }

      // Atualizar lista de assinaturas
      await buscarAssinaturas();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao cancelar assinatura:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarAssinaturas]);

  // Pausar assinatura
  const pausarAssinatura = useCallback(async (id: number): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/assinaturas-cliente/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'pausar' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao pausar assinatura');
      }

      // Atualizar lista de assinaturas
      await buscarAssinaturas();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao pausar assinatura:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarAssinaturas]);

  // Reativar assinatura
  const reativarAssinatura = useCallback(async (id: number): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/assinaturas-cliente/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'reativar' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao reativar assinatura');
      }

      // Atualizar lista de assinaturas
      await buscarAssinaturas();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao reativar assinatura:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarAssinaturas]);

  // Obter benefícios de assinatura para um serviço específico
  const obterBeneficios = useCallback(async (servicoId: number): Promise<BeneficiosAssinatura | null> => {
    try {
      const response = await fetch(`/api/assinaturas-cliente/beneficios?servico_id=${servicoId}`);
      const data = await response.json();

      if (!response.ok) {
        return null;
      }

      return data.beneficios;
    } catch (err) {
      console.error('Erro ao obter benefícios:', err);
      return null;
    }
  }, []);

  // Refresh geral
  const refresh = useCallback(() => {
    buscarAssinaturas();
  }, [buscarAssinaturas]);

  // Carregar dados iniciais
  useEffect(() => {
    refresh();
  }, [refresh]);

  return {
    assinaturas,
    loading,
    error,
    criarAssinatura,
    cancelarAssinatura,
    pausarAssinatura,
    reativarAssinatura,
    obterBeneficios,
    refresh,
  };
}

// Hook para uma assinatura específica
export function useAssinatura(assinaturaId: number) {
  const [assinatura, setAssinatura] = useState<PlanoAssinaturaExpandido | null>(null);
  const [beneficios, setBeneficios] = useState<BeneficiosAssinatura | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const buscarAssinatura = useCallback(async () => {
    if (!assinaturaId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/assinaturas-cliente/${assinaturaId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar assinatura');
      }

      setAssinatura(data.data);
      setBeneficios(data.beneficios);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao buscar assinatura:', err);
    } finally {
      setLoading(false);
    }
  }, [assinaturaId]);

  useEffect(() => {
    buscarAssinatura();
  }, [buscarAssinatura]);

  return {
    assinatura,
    beneficios,
    loading,
    error,
    refresh: buscarAssinatura,
  };
}

// Hook para verificar se cliente tem assinatura ativa para um serviço
export function useVerificarAssinatura(empresaId?: number, servicoId?: number) {
  const [temAssinatura, setTemAssinatura] = useState(false);
  const [beneficios, setBeneficios] = useState<BeneficiosAssinatura | null>(null);
  const [loading, setLoading] = useState(false);

  const verificar = useCallback(async () => {
    if (!empresaId || !servicoId) {
      setTemAssinatura(false);
      setBeneficios(null);
      return;
    }

    try {
      setLoading(true);

      const params = new URLSearchParams({
        empresa_id: empresaId.toString(),
        servico_id: servicoId.toString(),
        apenas_ativos: 'true'
      });

      const response = await fetch(`/api/assinaturas-cliente?${params.toString()}`);
      const data = await response.json();

      if (response.ok && data.data && data.data.length > 0) {
        setTemAssinatura(true);
        
        // Buscar benefícios específicos
        const beneficiosResponse = await fetch(`/api/assinaturas-cliente/beneficios?servico_id=${servicoId}`);
        const beneficiosData = await beneficiosResponse.json();
        
        if (beneficiosResponse.ok) {
          setBeneficios(beneficiosData.beneficios);
        }
      } else {
        setTemAssinatura(false);
        setBeneficios(null);
      }
    } catch (err) {
      console.error('Erro ao verificar assinatura:', err);
      setTemAssinatura(false);
      setBeneficios(null);
    } finally {
      setLoading(false);
    }
  }, [empresaId, servicoId]);

  useEffect(() => {
    verificar();
  }, [verificar]);

  return {
    temAssinatura,
    beneficios,
    loading,
    refresh: verificar,
  };
}
