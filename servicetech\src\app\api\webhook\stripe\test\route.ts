import { NextResponse } from 'next/server';

export async function GET() {
  console.log('🔍 Verificando configuração do webhook...');
  
  // Verificar variáveis de ambiente
  const checks = {
    STRIPE_SECRET_KEY: !!process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: !!process.env.STRIPE_WEBHOOK_SECRET,
    SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    NEXT_PUBLIC_SUPABASE_URL: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
  };

  const allConfigured = Object.values(checks).every(Boolean);

  const response = {
    status: allConfigured ? 'success' : 'error',
    message: allConfigured 
      ? 'Todas as variáveis de ambiente estão configuradas!' 
      : 'Algumas variáveis de ambiente estão faltando',
    checks,
    timestamp: new Date().toISOString()
  };

  console.log('✅ Resultado da verificação:', response);

  return NextResponse.json(response, { 
    status: allConfigured ? 200 : 500 
  });
}
