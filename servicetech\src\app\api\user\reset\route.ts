import { NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'userId é obrigatório'
      }, { status: 400 });
    }

    console.log(`🔄 Resetando usuário para papel normal: ${userId}`);

    const supabase = createAdminClient();

    // Obter dados atuais do usuário
    const { data: { user }, error: getUserError } = await supabase.auth.admin.getUserById(userId);

    if (getUserError || !user) {
      console.error('❌ Erro ao obter usuário:', getUserError);
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado'
      }, { status: 404 });
    }

    console.log('👤 Dados atuais do usuário:', {
      id: user.id,
      email: user.email,
      current_role: user.user_metadata?.role,
      pagamento_confirmado: user.user_metadata?.pagamento_confirmado,
      onboarding_pendente: user.user_metadata?.onboarding_pendente
    });

    // Resetar metadados do usuário para estado inicial
    const resetData = {
      role: 'Usuario',
      pagamento_confirmado: false,
      onboarding_pendente: false,
      onboarding_concluido: false,
      plano_selecionado: null,
      empresa_id: null,
      stripe_payment_id: null,
      data_pagamento: null,
      reset_timestamp: new Date().toISOString()
    };

    console.log('📝 Dados para reset:', resetData);

    const { error: updateError } = await supabase.auth.admin.updateUserById(userId, {
      user_metadata: resetData
    });

    if (updateError) {
      console.error('❌ Erro ao resetar usuário:', updateError);
      return NextResponse.json({
        success: false,
        error: 'Erro ao resetar usuário',
        details: updateError
      }, { status: 500 });
    }

    // Verificar se o reset foi aplicado
    const { data: { user: updatedUser }, error: getUpdatedError } = await supabase.auth.admin.getUserById(userId);

    if (getUpdatedError || !updatedUser) {
      console.error('❌ Erro ao verificar usuário atualizado:', getUpdatedError);
      return NextResponse.json({
        success: false,
        error: 'Erro ao verificar dados atualizados'
      }, { status: 500 });
    }

    const finalUserData = {
      id: updatedUser.id,
      email: updatedUser.email,
      role: updatedUser.user_metadata?.role || 'Usuario',
      pagamento_confirmado: updatedUser.user_metadata?.pagamento_confirmado || false,
      onboarding_pendente: updatedUser.user_metadata?.onboarding_pendente || false,
      onboarding_concluido: updatedUser.user_metadata?.onboarding_concluido || false,
      plano_selecionado: updatedUser.user_metadata?.plano_selecionado || null,
      empresa_id: updatedUser.user_metadata?.empresa_id || null,
      reset_timestamp: updatedUser.user_metadata?.reset_timestamp,
      updated_at: updatedUser.updated_at
    };

    console.log('✅ Usuário resetado com sucesso:', finalUserData);

    return NextResponse.json({
      success: true,
      message: 'Usuário resetado para papel normal com sucesso!',
      user: finalUserData,
      changes: {
        before: {
          role: user.user_metadata?.role || 'Usuario',
          pagamento_confirmado: user.user_metadata?.pagamento_confirmado || false,
          onboarding_pendente: user.user_metadata?.onboarding_pendente || false
        },
        after: {
          role: finalUserData.role,
          pagamento_confirmado: finalUserData.pagamento_confirmado,
          onboarding_pendente: finalUserData.onboarding_pendente
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Erro geral ao resetar usuário:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro interno',
      details: error.message
    }, { status: 500 });
  }
}
