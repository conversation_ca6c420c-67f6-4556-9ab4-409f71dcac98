import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { StripeConnectUtils } from '@/utils/stripe/connect';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem configurar pagamentos.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { return_url, refresh_url } = body;

    if (!return_url || !refresh_url) {
      return NextResponse.json(
        { success: false, error: 'URLs de retorno são obrigatórias' },
        { status: 400 }
      );
    }

    // Buscar dados da empresa do proprietário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('*')
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se já tem conta Stripe conectada
    if (empresa.stripe_account_id) {
      // Se já tem conta, verificar status
      try {
        const statusConta = await StripeConnectUtils.verificarStatusConta(empresa.stripe_account_id);
        
        if (statusConta.details_submitted) {
          return NextResponse.json(
            { success: false, error: 'Conta Stripe já está configurada' },
            { status: 400 }
          );
        }

        // Se não completou o onboarding, criar novo link
        const onboardingUrl = await StripeConnectUtils.criarLinkOnboarding(
          empresa.stripe_account_id,
          return_url,
          refresh_url
        );

        return NextResponse.json({
          success: true,
          data: {
            onboarding_url: onboardingUrl,
            account_id: empresa.stripe_account_id,
            is_existing_account: true
          }
        });

      } catch (error) {
        console.error('Erro ao verificar conta existente:', error);
        // Se der erro, criar nova conta
      }
    }

    // Criar nova conta conectada
    const dadosConta = {
      email: user.email || '',
      nome_empresa: empresa.nome_empresa,
      cnpj: empresa.cnpj,
      telefone: empresa.telefone,
      endereco: empresa.endereco ? {
        linha1: `${empresa.endereco}, ${empresa.numero}`,
        cidade: empresa.cidade,
        estado: empresa.estado,
        cep: empresa.cep,
        pais: 'BR'
      } : undefined
    };

    const contaConectada = await StripeConnectUtils.criarContaConectada(dadosConta);

    // Salvar ID da conta na empresa
    const { error: updateError } = await supabase
      .from('empresas')
      .update({
        stripe_account_id: contaConectada.id,
        stripe_account_status: 'pending',
        stripe_charges_enabled: false,
        stripe_payouts_enabled: false,
        updated_at: new Date().toISOString()
      })
      .eq('empresa_id', empresa.empresa_id);

    if (updateError) {
      console.error('Erro ao salvar conta Stripe na empresa:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao salvar configuração' },
        { status: 500 }
      );
    }

    // Criar link de onboarding
    const onboardingUrl = await StripeConnectUtils.criarLinkOnboarding(
      contaConectada.id,
      return_url,
      refresh_url
    );

    console.log('✅ Conta Stripe Connect criada:', {
      empresa_id: empresa.empresa_id,
      account_id: contaConectada.id,
      proprietario: user.id
    });

    return NextResponse.json({
      success: true,
      data: {
        onboarding_url: onboardingUrl,
        account_id: contaConectada.id,
        is_existing_account: false
      }
    });

  } catch (error: any) {
    console.error('Erro ao processar OAuth do Stripe Connect:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('account_id');

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    if (!accountId) {
      return NextResponse.json(
        { success: false, error: 'Account ID é obrigatório' },
        { status: 400 }
      );
    }

    // Verificar se a conta pertence ao usuário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('*')
      .eq('proprietario_user_id', user.id)
      .eq('stripe_account_id', accountId)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Conta não encontrada ou não autorizada' },
        { status: 404 }
      );
    }

    // Verificar status da conta no Stripe
    const statusConta = await StripeConnectUtils.verificarStatusConta(accountId);
    const statusLegivel = StripeConnectUtils.obterStatusLegivel(statusConta);

    // Atualizar status na base de dados
    const { error: updateError } = await supabase
      .from('empresas')
      .update({
        stripe_account_status: statusLegivel.status,
        stripe_charges_enabled: statusConta.charges_enabled,
        stripe_payouts_enabled: statusConta.payouts_enabled,
        updated_at: new Date().toISOString()
      })
      .eq('empresa_id', empresa.empresa_id);

    if (updateError) {
      console.error('Erro ao atualizar status da conta:', updateError);
    }

    return NextResponse.json({
      success: true,
      data: {
        account_id: statusConta.id,
        status: statusLegivel,
        charges_enabled: statusConta.charges_enabled,
        payouts_enabled: statusConta.payouts_enabled,
        details_submitted: statusConta.details_submitted,
        requirements: statusConta.requirements,
        capabilities: statusConta.capabilities,
        pode_receber_pagamentos: StripeConnectUtils.podeReceberPagamentos(statusConta)
      }
    });

  } catch (error: any) {
    console.error('Erro ao verificar status da conta Stripe:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
