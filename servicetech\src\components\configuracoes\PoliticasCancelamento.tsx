'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { usePoliticasCancelamento } from '@/hooks/usePoliticasCancelamento';
import { useAuth } from '@/contexts/AuthContext';

interface PoliticasCancelamentoProps {
  empresaId: number;
}

export function PoliticasCancelamento({ empresaId }: PoliticasCancelamentoProps) {
  const { user } = useAuth();
  const {
    politica,
    loading,
    error,
    buscarPolitica,
    atualizarPolitica,
    limparError
  } = usePoliticasCancelamento();

  // Estados do formulário
  const [formData, setFormData] = useState({
    percentual_24h_confirmado: 50,
    percentual_mesmo_dia: 0,
    prazo_cancelamento: 2,
    permitir_cancelamento: true,
    notificar_cancelamentos: true,
    motivo_obrigatorio: false
  });

  const [salvando, setSalvando] = useState(false);
  const [mensagemSucesso, setMensagemSucesso] = useState('');

  // Carregar política ao montar o componente
  useEffect(() => {
    if (empresaId) {
      buscarPolitica(empresaId);
    }
  }, [empresaId, buscarPolitica]);

  // Atualizar formulário quando política for carregada
  useEffect(() => {
    if (politica) {
      setFormData({
        percentual_24h_confirmado: politica.cliente.antecedencia_24h_confirmado.percentual_reembolso,
        percentual_mesmo_dia: politica.cliente.mesmo_dia.percentual_reembolso,
        prazo_cancelamento: politica.configuracoes.prazo_cancelamento_cliente,
        permitir_cancelamento: politica.configuracoes.permitir_cancelamento_cliente,
        notificar_cancelamentos: politica.configuracoes.notificar_cancelamentos,
        motivo_obrigatorio: politica.configuracoes.motivo_obrigatorio
      });
    }
  }, [politica]);

  // Limpar mensagens após um tempo
  useEffect(() => {
    if (mensagemSucesso) {
      const timer = setTimeout(() => setMensagemSucesso(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [mensagemSucesso]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => limparError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, limparError]);

  // Atualizar campo do formulário
  const atualizarCampo = (campo: string, valor: any) => {
    setFormData(prev => ({
      ...prev,
      [campo]: valor
    }));
  };

  // Salvar política
  const salvarPolitica = async () => {
    try {
      setSalvando(true);
      limparError();

      const dadosAtualizacao = {
        cliente: {
          antecedencia_24h_confirmado: {
            percentual_reembolso: formData.percentual_24h_confirmado,
            ativo: true
          },
          mesmo_dia: {
            percentual_reembolso: formData.percentual_mesmo_dia,
            ativo: true
          }
        },
        configuracoes: {
          prazo_cancelamento_cliente: formData.prazo_cancelamento,
          permitir_cancelamento_cliente: formData.permitir_cancelamento,
          notificar_cancelamentos: formData.notificar_cancelamentos,
          motivo_obrigatorio: formData.motivo_obrigatorio
        }
      };

      const sucesso = await atualizarPolitica(empresaId, dadosAtualizacao);
      
      if (sucesso) {
        setMensagemSucesso('Política de cancelamento atualizada com sucesso!');
      }

    } catch (err) {
      console.error('Erro ao salvar política:', err);
    } finally {
      setSalvando(false);
    }
  };

  if (loading && !politica) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
            <span className="ml-3 text-[var(--text-secondary)]">Carregando política de cancelamento...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Mensagens */}
      {error && (
        <Card className="bg-[var(--error-light)] border-[var(--error)]">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-[var(--error)] rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <p className="text-[var(--text-primary)]">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {mensagemSucesso && (
        <Card className="bg-[var(--success-light)] border-[var(--success)]">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-6 h-6 bg-[var(--success)] rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-[var(--text-primary)]">{mensagemSucesso}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Configurações de Reembolso */}
      <Card>
        <CardContent className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
              Política de Reembolso
            </h3>
            <p className="text-sm text-[var(--text-secondary)]">
              Configure os percentuais de reembolso para diferentes situações de cancelamento
            </p>
          </div>

          <div className="space-y-6">
            {/* Cancelamento com menos de 24h (confirmado) */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-[var(--text-primary)]">
                Cancelamento com menos de 24h (agendamento confirmado)
              </label>
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.percentual_24h_confirmado}
                    onChange={(e) => atualizarCampo('percentual_24h_confirmado', parseInt(e.target.value) || 0)}
                    className="w-full"
                  />
                </div>
                <span className="text-sm text-[var(--text-secondary)] min-w-[80px]">
                  % de reembolso
                </span>
              </div>
              <p className="text-xs text-[var(--text-secondary)]">
                Percentual reembolsado quando cliente cancela com menos de 24h de antecedência e o agendamento já foi confirmado
              </p>
            </div>

            {/* Cancelamento no mesmo dia */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-[var(--text-primary)]">
                Cancelamento no mesmo dia
              </label>
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.percentual_mesmo_dia}
                    onChange={(e) => atualizarCampo('percentual_mesmo_dia', parseInt(e.target.value) || 0)}
                    className="w-full"
                  />
                </div>
                <span className="text-sm text-[var(--text-secondary)] min-w-[80px]">
                  % de reembolso
                </span>
              </div>
              <p className="text-xs text-[var(--text-secondary)]">
                Percentual reembolsado para cancelamentos feitos no mesmo dia do agendamento
              </p>
            </div>

            {/* Regras fixas */}
            <div className="bg-[var(--info-light)] border border-[var(--info)] rounded-lg p-4">
              <h4 className="font-medium text-[var(--text-primary)] mb-3">Regras Fixas</h4>
              <div className="space-y-2 text-sm text-[var(--text-secondary)]">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[var(--success)] rounded-full"></div>
                  <span>Cancelamento com +24h (não confirmado): <strong>100% de reembolso</strong></span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[var(--success)] rounded-full"></div>
                  <span>Cancelamento pela empresa: <strong>100% de reembolso</strong></span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configurações Gerais */}
      <Card>
        <CardContent className="p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
              Configurações de Cancelamento
            </h3>
            <p className="text-sm text-[var(--text-secondary)]">
              Configure as regras gerais para cancelamento de agendamentos
            </p>
          </div>

          <div className="space-y-6">
            {/* Prazo mínimo */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-[var(--text-primary)]">
                Prazo mínimo para cancelamento
              </label>
              <div className="flex items-center gap-4">
                <div className="flex-1 max-w-[200px]">
                  <Input
                    type="number"
                    min="0"
                    max="72"
                    value={formData.prazo_cancelamento}
                    onChange={(e) => atualizarCampo('prazo_cancelamento', parseInt(e.target.value) || 0)}
                    className="w-full"
                  />
                </div>
                <span className="text-sm text-[var(--text-secondary)]">
                  horas antes do agendamento
                </span>
              </div>
              <p className="text-xs text-[var(--text-secondary)]">
                Tempo mínimo que o cliente deve respeitar para cancelar um agendamento
              </p>
            </div>

            {/* Opções booleanas */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-[var(--text-primary)]">
                    Permitir cancelamento pelo cliente
                  </label>
                  <p className="text-xs text-[var(--text-secondary)]">
                    Se desabilitado, apenas a empresa pode cancelar agendamentos
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => atualizarCampo('permitir_cancelamento', !formData.permitir_cancelamento)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    formData.permitir_cancelamento ? 'bg-[var(--primary)]' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      formData.permitir_cancelamento ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-[var(--text-primary)]">
                    Notificar cancelamentos
                  </label>
                  <p className="text-xs text-[var(--text-secondary)]">
                    Enviar notificações por email quando agendamentos forem cancelados
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => atualizarCampo('notificar_cancelamentos', !formData.notificar_cancelamentos)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    formData.notificar_cancelamentos ? 'bg-[var(--primary)]' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      formData.notificar_cancelamentos ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-[var(--text-primary)]">
                    Motivo obrigatório
                  </label>
                  <p className="text-xs text-[var(--text-secondary)]">
                    Exigir que o cliente informe um motivo ao cancelar
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => atualizarCampo('motivo_obrigatorio', !formData.motivo_obrigatorio)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    formData.motivo_obrigatorio ? 'bg-[var(--primary)]' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      formData.motivo_obrigatorio ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botão Salvar */}
      <div className="flex justify-end">
        <Button
          onClick={salvarPolitica}
          disabled={salvando || loading}
          className="min-w-[120px]"
        >
          {salvando ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Salvando...
            </div>
          ) : (
            'Salvar Política'
          )}
        </Button>
      </div>
    </div>
  );
}
