# 🔗 Configuração do Stripe Connect

Sistema que permite proprietários receberem pagamentos diretamente em suas contas Stripe.

## 🏗️ Arquitetura

### Fluxo de Pagamento
```
Cliente → Agendamento → Payment Intent → Stripe → Estabelecimento (- comissão)
```

### Estrutura de Dados
**Campos na tabela `empresas`:**
- `stripe_account_id` - ID da conta conectada
- `stripe_account_status` - Status: not_connected, pending, active, restricted
- `stripe_charges_enabled` - Pode receber pagamentos
- `stripe_payouts_enabled` - Pode receber transferências
- `pagamentos_online_habilitados` - Habilitado na plataforma
- `percentual_comissao_plataforma` - Comissão (padrão 5%)

## 🚀 Configuração

### 1. Variáveis de Ambiente
```env
# Stripe
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxxxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxxxx
STRIPE_CONNECT_CLIENT_ID=ca_xxxxxxxxxxxxxxx

# URL base
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 2. Dashboard do Stripe
1. **Ativar Stripe Connect**:
   - Connect → Settings → Ativar "Express accounts"

2. **Configurar Webhook**:
   - Endpoint: `https://seudominio.com/api/webhook/stripe`
   - Eventos: `payment_intent.succeeded`, `payment_intent.payment_failed`, `account.updated`

3. **URLs de Redirecionamento**:
   - Adicionar domínio autorizado: `https://seudominio.com`

## 📱 Como Usar

### Para Proprietários
1. **Dashboard → Configurações → Pagamentos**
2. **Conectar com Stripe** (OAuth flow)
3. **Completar onboarding** no Stripe
4. **Habilitar pagamentos online** na plataforma

### Para Clientes

#### Com Stripe Connect Configurado
- **Opções**: "Pagar no Local" OU "Pagar Online"
- **Pagamento Online**: Cartão ou Pix via Stripe
- **Confirmação**: Automática após pagamento

#### Sem Stripe Connect
- **Opção**: Apenas "Pagar no Local"
- **Pagamento**: No estabelecimento
- **Status**: Pendente de confirmação manual

## 💰 Estrutura de Comissões

### Exemplo (R$ 100,00)
```
Valor do Agendamento:           R$ 100,00
├── Comissão da Plataforma (5%): R$   5,00  → Plataforma
├── Taxa do Stripe (3,4% + 0,60): R$   4,00  → Stripe
└── Valor Líquido:              R$  91,00  → Estabelecimento
```

### Configuração
- **Padrão**: 5% para a plataforma
- **Transparente**: Cliente vê total, estabelecimento vê líquido
- **Configurável**: Por estabelecimento (futuro)

## 🔧 APIs Implementadas

### Stripe Connect
- `GET /api/stripe/connect/oauth` - Inicia OAuth
- `GET /api/stripe/connect/callback` - Callback OAuth
- `GET /api/stripe/connect/status` - Status da conta
- `POST /api/stripe/connect/disconnect` - Desconectar conta

### Pagamentos
- `POST /api/agendamentos/payment-intent` - Payment Intent com conta conectada
- `POST /api/webhook/stripe` - Processa eventos Stripe

## 🧪 Testes

### Dados de Teste
**Cartões**:
- Sucesso: `************** 4242`
- Falha: `************** 0002`
- Autenticação: `************** 3155`

**Empresa**:
- CNPJ: `11.222.333/0001-81`
- Telefone: `(11) 99999-9999`

### Fluxo de Teste
1. Criar conta proprietário
2. Cadastrar empresa
3. Conectar com Stripe (dados teste)
4. Habilitar pagamentos online
5. Criar agendamento como cliente
6. Testar pagamento online

## 🚨 Validações de Segurança

### Verificações
- ✅ Webhook signature
- ✅ Propriedade do agendamento
- ✅ Status da conta ativa
- ✅ Permissões por role
- ✅ Prevenção de desconexão com pagamentos pendentes

### Tratamento de Erros
- **Conta não configurada**: Mensagem para proprietário
- **Pagamento falhou**: Página de erro com opções
- **Conta restrita**: Instruções para resolver
- **Webhook falhou**: Retry automático e logs

## 📊 Monitoramento

### Logs Importantes
```typescript
// Conexão bem-sucedida
console.log('✅ Conta Stripe Connect criada:', {
  empresa_id, account_id, proprietario
});

// Pagamento processado
console.log('✅ Pagamento processado:', {
  agendamento_id, payment_intent_id, valor
});
```

### Métricas Sugeridas
- Taxa de conversão de onboarding
- Tempo médio para ativação
- Volume de transações
- Taxa de falhas de pagamento
- Disputas e chargebacks

## 🔄 Próximos Passos

1. **Políticas de Cancelamento**: Regras por estabelecimento
2. **Dashboard Financeiro**: Relatórios para proprietários
3. **Pagamentos Recorrentes**: Assinaturas de serviços
4. **Multi-moeda**: Suporte a outras moedas
5. **Marketplace**: Múltiplos estabelecimentos

## 📞 Suporte

### Para Proprietários
- **Documentação**: Este guia
- **Suporte Stripe**: Dashboard → Support
- **Suporte Plataforma**: Contato da plataforma

### Para Desenvolvedores
- **Stripe Docs**: https://stripe.com/docs/connect
- **Webhook Testing**: https://stripe.com/docs/webhooks/test
- **Connect Examples**: https://github.com/stripe-samples/connect-examples

---

**Stripe Connect** - Pagamentos diretos para estabelecimentos 💳
