/**
 * Middleware de Segurança para APIs
 * Aplica rate limiting, validação e auditoria
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getRequestIdentifier, withRateLimit } from './rateLimiting';
import { validateData, ValidationSchema, detectSQLInjection, detectXSS } from './validation';
import { AuditLogger } from './audit';
import { logger } from '@/services/LoggingService';

export interface SecurityMiddlewareOptions {
  requireAuth?: boolean;
  requiredRoles?: string[];
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
  validation?: {
    schema: ValidationSchema;
    sanitize?: boolean;
  };
  auditAction?: string;
  auditResource?: string;
}

/**
 * Middleware principal de segurança
 */
export async function withSecurity(
  request: NextRequest,
  options: SecurityMiddlewareOptions = {}
) {
  const startTime = Date.now();
  let user: any = null;
  let securityContext: any = {};

  try {
    // 1. Verificar autenticação se necessário
    if (options.requireAuth !== false) {
      const supabase = await createClient();
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

      if (authError || !authUser) {
        AuditLogger.securityViolation(
          undefined,
          'Unauthorized API access attempt',
          getRequestIdentifier(request),
          { endpoint: request.url, method: request.method }
        );
        
        return NextResponse.json(
          { success: false, error: 'Não autorizado' },
          { status: 401 }
        );
      }

      user = authUser;
      securityContext.userId = user.id;
      securityContext.userRole = user.user_metadata?.role;

      // Verificar papéis se especificado
      if (options.requiredRoles && options.requiredRoles.length > 0) {
        const userRole = user.user_metadata?.role;
        if (!options.requiredRoles.includes(userRole)) {
          AuditLogger.securityViolation(
            user.id,
            'Insufficient permissions for API access',
            getRequestIdentifier(request),
            { 
              endpoint: request.url, 
              method: request.method,
              userRole,
              requiredRoles: options.requiredRoles
            }
          );
          
          return NextResponse.json(
            { success: false, error: 'Permissões insuficientes' },
            { status: 403 }
          );
        }
      }
    }

    // 2. Aplicar rate limiting
    if (options.rateLimit) {
      try {
        const identifier = getRequestIdentifier(request, user?.id);
        const endpoint = new URL(request.url).pathname;
        
        withRateLimit(identifier, endpoint, options.rateLimit);
      } catch (rateLimitError: any) {
        AuditLogger.rateLimitExceeded(
          getRequestIdentifier(request, user?.id),
          new URL(request.url).pathname,
          getRequestIdentifier(request)
        );
        
        return NextResponse.json(
          { 
            success: false, 
            error: 'Rate limit excedido',
            retryAfter: rateLimitError.retryAfter 
          },
          { 
            status: 429,
            headers: {
              'Retry-After': rateLimitError.retryAfter?.toString() ?? '60'
            }
          }
        );
      }
    }

    // 3. Validar dados de entrada se for POST/PUT/PATCH
    let validatedData: any = null;
    if (['POST', 'PUT', 'PATCH'].includes(request.method) && options.validation) {
      try {
        const body = await request.json();
        
        // Detectar tentativas de injeção
        const bodyString = JSON.stringify(body);
        if (detectSQLInjection(bodyString)) {
          AuditLogger.securityViolation(
            user?.id,
            'SQL injection attempt detected',
            getRequestIdentifier(request),
            { endpoint: request.url, body: bodyString.substring(0, 500) }
          );
          
          return NextResponse.json(
            { success: false, error: 'Dados de entrada inválidos' },
            { status: 400 }
          );
        }
        
        if (detectXSS(bodyString)) {
          AuditLogger.securityViolation(
            user?.id,
            'XSS attempt detected',
            getRequestIdentifier(request),
            { endpoint: request.url, body: bodyString.substring(0, 500) }
          );
          
          return NextResponse.json(
            { success: false, error: 'Dados de entrada inválidos' },
            { status: 400 }
          );
        }

        // Validar usando schema
        const validationResult = validateData(body, options.validation.schema);
        if (!validationResult.isValid) {
          AuditLogger.securityViolation(
            user?.id,
            'Invalid input data',
            getRequestIdentifier(request),
            { 
              endpoint: request.url, 
              validationErrors: validationResult.errors 
            }
          );
          
          return NextResponse.json(
            { 
              success: false, 
              error: 'Dados de entrada inválidos',
              validationErrors: validationResult.errors 
            },
            { status: 400 }
          );
        }

        validatedData = validationResult.sanitizedData;
      } catch (error: any) {
        AuditLogger.securityViolation(
          user?.id,
          'Invalid JSON in request body',
          getRequestIdentifier(request),
          { endpoint: request.url, error: error.message }
        );
        
        return NextResponse.json(
          { success: false, error: 'Formato de dados inválido' },
          { status: 400 }
        );
      }
    }

    // 4. Log de auditoria se especificado
    if (options.auditAction && options.auditResource) {
      AuditLogger.dataAccess(
        user?.id ?? 'anonymous',
        options.auditResource,
        undefined,
        true
      );
    }

    // Retornar contexto de segurança para uso na API
    securityContext.validatedData = validatedData;
    securityContext.processingTime = Date.now() - startTime;
    
    return {
      success: true,
      user,
      securityContext
    };

  } catch (error: any) {
    logger.error('Erro no middleware de segurança', error, {
      userId: user?.id,
      endpoint: request.url,
      processingTime: Date.now() - startTime
    });

    AuditLogger.securityViolation(
      user?.id,
      'Security middleware error',
      getRequestIdentifier(request),
      {
        endpoint: request.url,
        error: error.message,
        processingTime: Date.now() - startTime
      }
    );

    return NextResponse.json(
      { success: false, error: 'Erro interno de segurança' },
      { status: 500 }
    );
  }
}

/**
 * Wrapper para APIs com segurança
 */
export function createSecureApiHandler(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>,
  options: SecurityMiddlewareOptions = {}
) {
  return async (request: NextRequest) => {
    // Aplicar middleware de segurança
    const securityResult = await withSecurity(request, options);
    
    // Se retornou NextResponse, é um erro de segurança
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }
    
    // Se chegou aqui, passou na validação de segurança
    try {
      const result = await handler(request, securityResult);

      // Log de sucesso da API
      logger.info('API request successful', {
        endpoint: request.url,
        method: request.method,
        userId: securityResult.user?.id,
        processingTime: securityResult.securityContext.processingTime
      });

      return result;
    } catch (error: any) {
      logger.error('Erro no handler da API', error, {
        endpoint: request.url,
        method: request.method,
        userId: securityResult.user?.id
      });

      AuditLogger.securityViolation(
        securityResult.user?.id,
        'API handler error',
        getRequestIdentifier(request),
        {
          endpoint: request.url,
          error: error.message
        }
      );

      return NextResponse.json(
        { success: false, error: 'Erro interno do servidor' },
        { status: 500 }
      );
    }
  };
}

/**
 * Configurações predefinidas para diferentes tipos de API
 */
export const SECURITY_PRESETS = {
  // APIs públicas (sem autenticação)
  public: {
    requireAuth: false,
    rateLimit: { maxRequests: 100, windowMs: 60 * 60 * 1000 }
  },
  
  // APIs de usuário autenticado
  authenticated: {
    requireAuth: true,
    rateLimit: { maxRequests: 200, windowMs: 60 * 60 * 1000 }
  },
  
  // APIs de proprietário
  owner: {
    requireAuth: true,
    requiredRoles: ['Proprietario'],
    rateLimit: { maxRequests: 300, windowMs: 60 * 60 * 1000 }
  },
  
  // APIs de colaborador
  collaborator: {
    requireAuth: true,
    requiredRoles: ['Colaborador'],
    rateLimit: { maxRequests: 150, windowMs: 60 * 60 * 1000 }
  },
  
  // APIs administrativas
  admin: {
    requireAuth: true,
    requiredRoles: ['Administrador'],
    rateLimit: { maxRequests: 1000, windowMs: 60 * 60 * 1000 }
  },
  
  // APIs de autenticação (mais restritivas)
  auth: {
    requireAuth: false,
    rateLimit: { maxRequests: 10, windowMs: 15 * 60 * 1000 }
  },
  
  // APIs de pagamento (muito restritivas)
  payment: {
    requireAuth: true,
    rateLimit: { maxRequests: 20, windowMs: 60 * 60 * 1000 }
  }
} as const;

/**
 * Helper para aplicar preset de segurança
 */
export function withSecurityPreset(
  preset: keyof typeof SECURITY_PRESETS,
  additionalOptions: Partial<SecurityMiddlewareOptions> = {}
) {
  return {
    ...SECURITY_PRESETS[preset],
    ...additionalOptions
  };
}
