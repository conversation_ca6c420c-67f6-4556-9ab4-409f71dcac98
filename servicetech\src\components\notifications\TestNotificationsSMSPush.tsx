'use client';

import { useState } from 'react';
import { TipoNotificacao } from '@/types/notifications';
import { useFCMToken, usePushNotificationSupport } from '@/hooks/useDeviceTokens';

interface TestNotificationsSMSPushProps {
  className?: string;
}

export default function TestNotificationsSMSPush({ className = '' }: TestNotificationsSMSPushProps) {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<{ [key: string]: any }>({});
  const [selectedType, setSelectedType] = useState<TipoNotificacao>('novo_agendamento');
  const [selectedChannel, setSelectedChannel] = useState<'email' | 'sms' | 'push' | 'all'>('email');
  
  const { 
    isSupported: pushSupported, 
    isGranted: pushGranted, 
    requestPermission 
  } = usePushNotificationSupport();
  
  const { token: fcmToken, generateToken } = useFCMToken();

  const tiposNotificacao: { value: TipoNotificacao; label: string }[] = [
    { value: 'novo_agendamento', label: 'Novo Agendamento' },
    { value: 'agendamento_confirmado', label: 'Agendamento Confirmado' },
    { value: 'agendamento_recusado', label: 'Agendamento Recusado' },
    { value: 'agendamento_cancelado', label: 'Agendamento Cancelado' },
    { value: 'lembrete_confirmacao', label: 'Lembrete de Confirmação' },
    { value: 'lembrete_agendamento', label: 'Lembrete de Agendamento' },
    { value: 'pagamento_confirmado', label: 'Pagamento Confirmado' }
  ];

  const canais = [
    { value: 'email' as const, label: 'Email', icon: '📧' },
    { value: 'sms' as const, label: 'SMS', icon: '📱' },
    { value: 'push' as const, label: 'Push', icon: '🔔' },
    { value: 'all' as const, label: 'Todos', icon: '📢' }
  ];

  // Contexto de teste para agendamento
  const contextoTeste = {
    agendamento_id: 999,
    codigo_confirmacao: 'TEST123',
    cliente_nome: 'João Silva',
    cliente_email: '<EMAIL>',
    empresa_nome: 'Salão Teste',
    empresa_endereco: 'Rua Teste, 123 - Centro',
    servico_nome: 'Corte de Cabelo',
    servico_preco: 30.00,
    colaborador_nome: 'Maria Santos',
    data_hora_inicio: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Amanhã
    data_hora_fim: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(), // Amanhã + 1h
    forma_pagamento: 'Cartão de Crédito',
    valor_total: 30.00,
    observacoes_cliente: 'Teste de notificação'
  };

  const enviarNotificacaoTeste = async () => {
    try {
      setLoading(true);
      setResults({});

      // Se for push e não tiver permissão, solicitar
      if ((selectedChannel === 'push' || selectedChannel === 'all') && pushSupported && !pushGranted) {
        const granted = await requestPermission();
        if (!granted) {
          setResults({ error: 'Permissão para notificações push é necessária' });
          return;
        }
      }

      // Gerar token FCM se necessário
      if ((selectedChannel === 'push' || selectedChannel === 'all') && !fcmToken) {
        await generateToken();
      }

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tipo: selectedType,
          destinatario_id: 'current_user', // Será substituído pelo ID do usuário atual no backend
          contexto: contextoTeste,
          canal: selectedChannel === 'all' ? undefined : selectedChannel,
          agendamento_id: 999,
          empresa_id: 1
        })
      });

      const result = await response.json();
      setResults(result);

    } catch (error) {
      console.error('Erro ao enviar notificação teste:', error);
      setResults({
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const testarSMSDirecto = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/notifications/test-sms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: 'Teste de SMS do ServiceTech! Se você recebeu esta mensagem, o sistema está funcionando corretamente.',
          to: '+5511999999999' // Número de teste
        })
      });

      const result = await response.json();
      setResults(result);

    } catch (error) {
      console.error('Erro ao testar SMS:', error);
      setResults({
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const testarPushDirecto = async () => {
    try {
      setLoading(true);

      if (!pushSupported) {
        setResults({ error: 'Push notifications não suportadas neste navegador' });
        return;
      }

      if (!pushGranted) {
        const granted = await requestPermission();
        if (!granted) {
          setResults({ error: 'Permissão para notificações push negada' });
          return;
        }
      }

      // Enviar notificação de teste diretamente pelo navegador
      const notification = new Notification('ServiceTech - Teste', {
        body: 'Esta é uma notificação de teste do ServiceTech!',
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        tag: 'test-notification',
        requireInteraction: true
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      setResults({
        success: true,
        message: 'Notificação push enviada diretamente pelo navegador'
      });

    } catch (error) {
      console.error('Erro ao testar push:', error);
      setResults({
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Teste de Notificações SMS e Push
      </h3>

      {/* Configurações do Teste */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tipo de Notificação
          </label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as TipoNotificacao)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {tiposNotificacao.map(tipo => (
              <option key={tipo.value} value={tipo.value}>
                {tipo.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Canal
          </label>
          <select
            value={selectedChannel}
            onChange={(e) => setSelectedChannel(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {canais.map(canal => (
              <option key={canal.value} value={canal.value}>
                {canal.icon} {canal.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Botões de Teste */}
      <div className="flex flex-wrap gap-3 mb-6">
        <button
          onClick={enviarNotificacaoTeste}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Enviando...' : 'Enviar Notificação Teste'}
        </button>

        <button
          onClick={testarSMSDirecto}
          disabled={loading}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          📱 Teste SMS Direto
        </button>

        <button
          onClick={testarPushDirecto}
          disabled={loading || !pushSupported}
          className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          🔔 Teste Push Direto
        </button>
      </div>

      {/* Status do Push */}
      {pushSupported && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Status Push Notifications</h4>
          <div className="text-sm text-gray-600">
            <p>Suporte: ✅ Disponível</p>
            <p>Permissão: {pushGranted ? '✅ Concedida' : '❌ Não concedida'}</p>
            <p>Token FCM: {fcmToken ? '✅ Gerado' : '❌ Não gerado'}</p>
          </div>
        </div>
      )}

      {/* Resultados */}
      {Object.keys(results).length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Resultado:</h4>
          <div className={`p-3 rounded-md ${results.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <pre className="text-sm whitespace-pre-wrap">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
