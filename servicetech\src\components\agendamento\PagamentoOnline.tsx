'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { usePagamentoAgendamento } from '@/hooks/usePagamentoAgendamento';

// Carregar Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string);

interface PagamentoOnlineProps {
  agendamentoId: number;
  valor: number;
  nomeServico: string;
  nomeEmpresa: string;
  onSucesso?: () => void;
  onErro?: (erro: string) => void;
  onVoltar?: () => void;
}

// Componente interno que usa Stripe Elements
function FormularioPagamento({ 
  agendamentoId, 
  valor, 
  nomeServico, 
  nomeEmpresa,
  onSucesso, 
  onErro,
  onVoltar 
}: PagamentoOnlineProps) {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    try {
      // Confirmar o pagamento
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/agendamento/pagamento-sucesso?agendamento_id=${agendamentoId}`,
        },
      });

      if (error) {
        setErrorMessage(error.message || 'Ocorreu um erro ao processar o pagamento.');
        onErro?.(error.message || 'Erro no pagamento');
      } else {
        // Pagamento bem-sucedido será tratado pelo return_url
        onSucesso?.();
      }
    } catch (error: any) {
      setErrorMessage('Erro inesperado ao processar pagamento.');
      onErro?.(error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
          Pagamento Online
        </h3>
        <p className="text-[var(--text-secondary)] text-sm">
          Complete o pagamento para confirmar seu agendamento
        </p>
      </div>

      {/* Resumo do pagamento */}
      <Card className="bg-[var(--primary-light)] border-[var(--primary)]">
        <CardContent className="p-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="font-medium text-[var(--text-primary)]">Serviço:</span>
              <span className="text-[var(--text-secondary)]">{nomeServico}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium text-[var(--text-primary)]">Estabelecimento:</span>
              <span className="text-[var(--text-secondary)]">{nomeEmpresa}</span>
            </div>
            <hr className="border-[var(--border)]" />
            <div className="flex justify-between items-center">
              <span className="font-semibold text-[var(--text-primary)]">Total:</span>
              <span className="text-xl font-bold text-[var(--primary)]">
                {formatarPreco(valor)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Formulário de pagamento */}
      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <h4 className="font-medium text-[var(--text-primary)] mb-4">
                Dados do Pagamento
              </h4>
              <PaymentElement 
                options={{
                  layout: 'tabs',
                  paymentMethodOrder: ['card', 'pix']
                }}
              />
            </div>

            {errorMessage && (
              <div className="p-4 bg-[var(--error-light)] border border-[var(--error)] rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-[var(--error)] rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-[var(--error)] mb-1">
                      Erro no Pagamento
                    </h4>
                    <p className="text-sm text-[var(--text-secondary)]">
                      {errorMessage}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Botões de ação */}
            <div className="flex flex-col sm:flex-row gap-4">
              {onVoltar && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onVoltar}
                  className="flex-1"
                  disabled={loading}
                >
                  Voltar
                </Button>
              )}
              <Button
                type="submit"
                className="flex-1"
                disabled={!stripe || loading}
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Processando...
                  </div>
                ) : (
                  `Pagar ${formatarPreco(valor)}`
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Informações de segurança */}
      <Card className="bg-[var(--info-light)] border-[var(--info)]">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-[var(--info)] rounded-full flex items-center justify-center mt-0.5">
              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-[var(--text-primary)] mb-1">
                Pagamento Seguro
              </h4>
              <p className="text-sm text-[var(--text-secondary)]">
                Seus dados são protegidos por criptografia SSL e processados pelo Stripe, 
                uma das plataformas de pagamento mais seguras do mundo.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Componente principal que envolve com Elements
export function PagamentoOnline(props: PagamentoOnlineProps) {
  const { criarPaymentIntent, loading, error, paymentIntent } = usePagamentoAgendamento();
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  useEffect(() => {
    const inicializarPagamento = async () => {
      const result = await criarPaymentIntent({
        agendamento_id: props.agendamentoId,
        valor: props.valor,
        descricao: `Pagamento do agendamento - ${props.nomeServico} na ${props.nomeEmpresa}`
      });

      if (result) {
        setClientSecret(result.client_secret);
      } else if (error) {
        props.onErro?.(error);
      }
    };

    inicializarPagamento();
  }, [props.agendamentoId, props.valor]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-[var(--text-secondary)]">Preparando pagamento...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="bg-[var(--error-light)] border-[var(--error)]">
          <CardContent className="p-4">
            <div className="text-center">
              <h3 className="font-medium text-[var(--error)] mb-2">
                Erro ao Preparar Pagamento
              </h3>
              <p className="text-sm text-[var(--text-secondary)] mb-4">
                {error}
              </p>
              {props.onVoltar && (
                <Button variant="outline" onClick={props.onVoltar}>
                  Voltar
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!clientSecret) {
    return (
      <div className="flex items-center justify-center p-8">
        <span className="text-[var(--text-secondary)]">Carregando...</span>
      </div>
    );
  }

  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#3b82f6',
    },
  };

  const options = {
    clientSecret,
    appearance,
  };

  return (
    <Elements options={options} stripe={stripePromise}>
      <FormularioPagamento {...props} />
    </Elements>
  );
}
