import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { CenarioTeste } from '@/types/testesUsuario';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const { data: userData } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', user.id)
      .single();

    const userRole = userData?.raw_user_meta_data?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas administradores podem gerenciar testes.' },
        { status: 403 }
      );
    }

    // Buscar parâmetros de filtro
    const { searchParams } = new URL(request.url);
    const papel_usuario = searchParams.get('papel_usuario');
    const categoria = searchParams.get('categoria');
    const dificuldade = searchParams.get('dificuldade');
    const ativo = searchParams.get('ativo');

    // Construir query
    let query = supabase
      .from('cenarios_teste')
      .select('*')
      .order('created_at', { ascending: false });

    // Aplicar filtros
    if (papel_usuario) {
      query = query.eq('papel_usuario', papel_usuario);
    }
    if (categoria) {
      query = query.eq('categoria', categoria);
    }
    if (dificuldade) {
      query = query.eq('dificuldade', dificuldade);
    }
    if (ativo !== null) {
      query = query.eq('ativo', ativo === 'true');
    }

    const { data: cenarios, error } = await query;

    if (error) {
      console.error('Erro ao buscar cenários:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar cenários de teste' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: cenarios
    });

  } catch (error: any) {
    console.error('Erro na API de cenários:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const { data: userData } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', user.id)
      .single();

    const userRole = userData?.raw_user_meta_data?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas administradores podem criar cenários.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Validar dados obrigatórios
    const {
      nome_cenario,
      descricao,
      papel_usuario,
      categoria,
      dificuldade,
      tempo_estimado_minutos,
      passos,
      criterios_sucesso,
      metricas_alvo
    } = body;

    if (!nome_cenario || !descricao || !papel_usuario || !categoria || !dificuldade) {
      return NextResponse.json(
        { success: false, error: 'Campos obrigatórios não preenchidos' },
        { status: 400 }
      );
    }

    if (!passos || !Array.isArray(passos) || passos.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cenário deve ter pelo menos um passo' },
        { status: 400 }
      );
    }

    if (!criterios_sucesso || !Array.isArray(criterios_sucesso) || criterios_sucesso.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cenário deve ter pelo menos um critério de sucesso' },
        { status: 400 }
      );
    }

    // Validar papel de usuário
    const papeisValidos = ['Administrador', 'Proprietario', 'Colaborador', 'Cliente'];
    if (!papeisValidos.includes(papel_usuario)) {
      return NextResponse.json(
        { success: false, error: 'Papel de usuário inválido' },
        { status: 400 }
      );
    }

    // Validar categoria
    const categoriasValidas = ['Onboarding', 'Agendamento', 'Gestao', 'Pagamento', 'Navegacao'];
    if (!categoriasValidas.includes(categoria)) {
      return NextResponse.json(
        { success: false, error: 'Categoria inválida' },
        { status: 400 }
      );
    }

    // Validar dificuldade
    const dificuldadesValidas = ['Facil', 'Medio', 'Dificil'];
    if (!dificuldadesValidas.includes(dificuldade)) {
      return NextResponse.json(
        { success: false, error: 'Dificuldade inválida' },
        { status: 400 }
      );
    }

    // Validar e numerar passos
    const passosNumerados = passos.map((passo: any, index: number) => ({
      ...passo,
      passo_numero: index + 1
    }));

    // Criar cenário
    const { data: novoCenario, error } = await supabase
      .from('cenarios_teste')
      .insert({
        nome_cenario,
        descricao,
        papel_usuario,
        categoria,
        dificuldade,
        tempo_estimado_minutos: tempo_estimado_minutos || 30,
        passos: passosNumerados,
        criterios_sucesso,
        metricas_alvo: metricas_alvo || {
          tempo_maximo_minutos: tempo_estimado_minutos * 1.5 || 45,
          taxa_sucesso_minima: 80,
          pontuacao_satisfacao_minima: 7,
          taxa_erro_maxima: 20
        },
        ativo: body.ativo !== undefined ? body.ativo : true
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar cenário:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao criar cenário de teste' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: novoCenario,
      message: 'Cenário de teste criado com sucesso'
    });

  } catch (error: any) {
    console.error('Erro na API de criação de cenário:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
