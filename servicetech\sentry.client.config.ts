/**
 * Configuração do Sentry para o Cliente (Browser)
 */

import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  
  // Configurações de ambiente
  environment: process.env.NODE_ENV ?? 'development',
  release: process.env.NEXT_PUBLIC_APP_VERSION ?? '1.0.0',
  
  // Configurações de performance
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // Configurações de replay de sessão (apenas em produção)
  replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0.0,
  replaysOnErrorSampleRate: process.env.NODE_ENV === 'production' ? 1.0 : 0.0,
  
  // Configurações de debug
  debug: process.env.NODE_ENV === 'development',
  
  // Filtrar dados sensíveis
  beforeSend(event, hint) {
    // Lista de campos sensíveis para filtrar
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'cookie', 'session', 'credit_card', 'ssn', 'cpf', 'cnpj'
    ];

    // Função para filtrar dados sensíveis
    const filterSensitiveData = (obj: any): any => {
      if (!obj || typeof obj !== 'object') return obj;

      const filtered = Array.isArray(obj) ? [] : {};
      
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        const isSensitive = sensitiveFields.some(field => lowerKey.includes(field));
        
        if (isSensitive) {
          (filtered as any)[key] = '[FILTERED]';
        } else if (typeof value === 'object' && value !== null) {
          (filtered as any)[key] = filterSensitiveData(value);
        } else {
          (filtered as any)[key] = value;
        }
      }
      
      return filtered;
    };

    // Filtrar dados do evento
    if (event.request?.data) {
      event.request.data = filterSensitiveData(event.request.data);
    }
    
    if (event.extra) {
      event.extra = filterSensitiveData(event.extra);
    }

    // Filtrar breadcrumbs
    if (event.breadcrumbs) {
      event.breadcrumbs = event.breadcrumbs.map(breadcrumb => ({
        ...breadcrumb,
        data: breadcrumb.data ? filterSensitiveData(breadcrumb.data) : undefined
      }));
    }

    return event;
  },

  // Configurações de integração
  integrations: [
    Sentry.replayIntegration({
      // Configurações de replay
      maskAllText: true,
      blockAllMedia: true,
    }),
  ],

  // Configurações de transporte (padrão do browser)

  // Configurações de contexto inicial
  initialScope: {
    tags: {
      component: 'client',
      platform: 'browser'
    }
  },

  // Configurações de erro
  ignoreErrors: [
    // Erros comuns do browser que não são relevantes
    'Non-Error promise rejection captured',
    'ResizeObserver loop limit exceeded',
    'Script error.',
    'Network request failed',
    // Erros de extensões do browser
    /extension\//i,
    /^chrome:\/\//i,
    /^moz-extension:\/\//i,
  ],

  // URLs para ignorar
  denyUrls: [
    // Extensões do browser
    /extensions\//i,
    /^chrome:\/\//i,
    /^moz-extension:\/\//i,
    // Scripts de terceiros
    /google-analytics\.com/i,
    /googletagmanager\.com/i,
    /facebook\.net/i,
  ],

  // Configurações de breadcrumbs
  maxBreadcrumbs: 50,

  // Configurações de contexto
  attachStacktrace: true,
  sendDefaultPii: false, // Não enviar informações pessoais por padrão
});
