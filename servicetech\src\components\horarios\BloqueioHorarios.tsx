'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { 
  BloqueioRecorrente, 
  BloqueioEspecifico, 
  DiaSemana,
  FormularioBloqueio 
} from '@/types/horarios';

interface BloqueioHorariosProps {
  colaboradorId: string;
  bloqueiosRecorrentes: BloqueioRecorrente[];
  bloqueiosEspecificos: BloqueioEspecifico[];
  onAdicionarBloqueio: (bloqueio: BloqueioRecorrente | BloqueioEspecifico) => Promise<boolean>;
  onRemoverBloqueio: (bloqueioId: string, tipo: 'recorrente' | 'especifico') => Promise<boolean>;
  loading?: boolean;
}

const diasSemana: { key: DiaSemana; label: string }[] = [
  { key: 'segunda', label: 'Segunda-feira' },
  { key: 'terca', label: 'Terça-feira' },
  { key: 'quarta', label: 'Quarta-feira' },
  { key: 'quinta', label: 'Quinta-feira' },
  { key: 'sexta', label: 'Sexta-feira' },
  { key: 'sabado', label: 'Sábado' },
  { key: 'domingo', label: 'Domingo' },
];

const tiposBloqueio = [
  { key: 'folga', label: 'Folga' },
  { key: 'compromisso', label: 'Compromisso' },
  { key: 'manutencao', label: 'Manutenção' },
  { key: 'outro', label: 'Outro' },
];

export function BloqueioHorarios({
  colaboradorId,
  bloqueiosRecorrentes,
  bloqueiosEspecificos,
  onAdicionarBloqueio,
  onRemoverBloqueio,
  loading = false
}: BloqueioHorariosProps) {
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [formulario, setFormulario] = useState<FormularioBloqueio>({
    tipo: 'especifico',
    data: '',
    dia_semana: 'segunda',
    horario_inicio: '',
    horario_fim: '',
    descricao: '',
    bloquear_dia_todo: true
  });
  const [salvando, setSalvando] = useState(false);
  const [mensagem, setMensagem] = useState<{ tipo: 'success' | 'error'; texto: string } | null>(null);

  // Limpar mensagens após 5 segundos
  useEffect(() => {
    if (mensagem) {
      const timer = setTimeout(() => setMensagem(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [mensagem]);

  const resetFormulario = () => {
    setFormulario({
      tipo: 'especifico',
      data: '',
      dia_semana: 'segunda',
      horario_inicio: '',
      horario_fim: '',
      descricao: '',
      bloquear_dia_todo: true
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formulario.descricao.trim()) {
      setMensagem({ tipo: 'error', texto: 'Descrição é obrigatória' });
      return;
    }

    if (formulario.tipo === 'especifico' && !formulario.data) {
      setMensagem({ tipo: 'error', texto: 'Data é obrigatória para bloqueios específicos' });
      return;
    }

    if (!formulario.bloquear_dia_todo && (!formulario.horario_inicio || !formulario.horario_fim)) {
      setMensagem({ tipo: 'error', texto: 'Horários são obrigatórios quando não bloquear o dia todo' });
      return;
    }

    setSalvando(true);
    setMensagem(null);

    try {
      let bloqueio: BloqueioRecorrente | BloqueioEspecifico;

      if (formulario.tipo === 'recorrente') {
        bloqueio = {
          dia_semana: formulario.dia_semana!,
          descricao: formulario.descricao.trim(),
          ativo: true,
          ...(formulario.bloquear_dia_todo ? {} : {
            horario_inicio: formulario.horario_inicio,
            horario_fim: formulario.horario_fim
          })
        };
      } else {
        bloqueio = {
          data: formulario.data!,
          descricao: formulario.descricao.trim(),
          tipo: 'outro',
          ...(formulario.bloquear_dia_todo ? {} : {
            horario_inicio: formulario.horario_inicio,
            horario_fim: formulario.horario_fim
          })
        };
      }

      const sucesso = await onAdicionarBloqueio(bloqueio);

      if (sucesso) {
        setMensagem({ tipo: 'success', texto: 'Bloqueio adicionado com sucesso!' });
        resetFormulario();
        setMostrarFormulario(false);
      } else {
        setMensagem({ tipo: 'error', texto: 'Erro ao adicionar bloqueio. Tente novamente.' });
      }
    } catch (error) {
      setMensagem({ tipo: 'error', texto: 'Erro inesperado ao adicionar bloqueio.' });
    } finally {
      setSalvando(false);
    }
  };

  const handleRemover = async (bloqueioId: string, tipo: 'recorrente' | 'especifico') => {
    if (!confirm('Tem certeza que deseja remover este bloqueio?')) {
      return;
    }

    setSalvando(true);
    setMensagem(null);

    try {
      const sucesso = await onRemoverBloqueio(bloqueioId, tipo);

      if (sucesso) {
        setMensagem({ tipo: 'success', texto: 'Bloqueio removido com sucesso!' });
      } else {
        setMensagem({ tipo: 'error', texto: 'Erro ao remover bloqueio. Tente novamente.' });
      }
    } catch (error) {
      setMensagem({ tipo: 'error', texto: 'Erro inesperado ao remover bloqueio.' });
    } finally {
      setSalvando(false);
    }
  };

  const formatarData = (data: string) => {
    return new Date(data + 'T00:00:00').toLocaleDateString('pt-BR');
  };

  const formatarHorario = (inicio?: string, fim?: string) => {
    if (!inicio || !fim) return 'Dia todo';
    return `${inicio} às ${fim}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
        <span className="ml-2 text-[var(--text-secondary)]">Carregando bloqueios...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Mensagem de feedback */}
      {mensagem && (
        <div className={`p-4 rounded-md ${
          mensagem.tipo === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {mensagem.texto}
        </div>
      )}

      {/* Cabeçalho */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-[var(--text-primary)]">
            Bloqueios de Horários
          </h3>
          <p className="text-[var(--text-secondary)] text-sm">
            Configure bloqueios específicos ou recorrentes
          </p>
        </div>
        
        <Button
          onClick={() => setMostrarFormulario(!mostrarFormulario)}
          disabled={salvando}
        >
          {mostrarFormulario ? 'Cancelar' : '+ Novo Bloqueio'}
        </Button>
      </div>

      {/* Formulário de novo bloqueio */}
      {mostrarFormulario && (
        <Card className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Tipo de bloqueio */}
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                  Tipo de Bloqueio
                </label>
                <select
                  value={formulario.tipo}
                  onChange={(e) => setFormulario(prev => ({ 
                    ...prev, 
                    tipo: e.target.value as 'recorrente' | 'especifico' 
                  }))}
                  className="w-full px-3 py-2 border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)]"
                >
                  <option value="especifico">Data Específica</option>
                  <option value="recorrente">Recorrente (semanal)</option>
                </select>
              </div>

              {/* Data ou dia da semana */}
              <div>
                <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                  {formulario.tipo === 'especifico' ? 'Data' : 'Dia da Semana'}
                </label>
                {formulario.tipo === 'especifico' ? (
                  <Input
                    type="date"
                    value={formulario.data}
                    onChange={(e) => setFormulario(prev => ({ ...prev, data: e.target.value }))}
                    min={new Date().toISOString().split('T')[0]}
                  />
                ) : (
                  <select
                    value={formulario.dia_semana}
                    onChange={(e) => setFormulario(prev => ({ 
                      ...prev, 
                      dia_semana: e.target.value as DiaSemana 
                    }))}
                    className="w-full px-3 py-2 border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)]"
                  >
                    {diasSemana.map(dia => (
                      <option key={dia.key} value={dia.key}>{dia.label}</option>
                    ))}
                  </select>
                )}
              </div>
            </div>

            {/* Descrição */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                Descrição
              </label>
              <Input
                type="text"
                value={formulario.descricao}
                onChange={(e) => setFormulario(prev => ({ ...prev, descricao: e.target.value }))}
                placeholder="Ex: Consulta médica, Folga, Manutenção..."
                required
              />
            </div>

            {/* Opção de bloquear dia todo */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="bloquear-dia-todo"
                checked={formulario.bloquear_dia_todo}
                onChange={(e) => setFormulario(prev => ({ 
                  ...prev, 
                  bloquear_dia_todo: e.target.checked,
                  horario_inicio: e.target.checked ? '' : prev.horario_inicio,
                  horario_fim: e.target.checked ? '' : prev.horario_fim
                }))}
                className="h-4 w-4 text-[var(--primary)] focus:ring-[var(--primary)] border-gray-300 rounded"
              />
              <label htmlFor="bloquear-dia-todo" className="text-sm text-[var(--text-primary)]">
                Bloquear o dia todo
              </label>
            </div>

            {/* Horários específicos */}
            {!formulario.bloquear_dia_todo && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                    Horário de Início
                  </label>
                  <Input
                    type="time"
                    value={formulario.horario_inicio}
                    onChange={(e) => setFormulario(prev => ({ ...prev, horario_inicio: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                    Horário de Fim
                  </label>
                  <Input
                    type="time"
                    value={formulario.horario_fim}
                    onChange={(e) => setFormulario(prev => ({ ...prev, horario_fim: e.target.value }))}
                  />
                </div>
              </div>
            )}

            {/* Botões */}
            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setMostrarFormulario(false);
                  resetFormulario();
                }}
                disabled={salvando}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={salvando}
              >
                {salvando ? 'Salvando...' : 'Adicionar Bloqueio'}
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Lista de bloqueios recorrentes */}
      {bloqueiosRecorrentes.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-[var(--text-primary)] mb-3">
            Bloqueios Recorrentes
          </h4>
          <div className="space-y-2">
            {bloqueiosRecorrentes.map((bloqueio) => (
              <Card key={bloqueio.id} className="p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium text-[var(--text-primary)]">
                      {diasSemana.find(d => d.key === bloqueio.dia_semana)?.label}
                    </div>
                    <div className="text-sm text-[var(--text-secondary)]">
                      {bloqueio.descricao} • {formatarHorario(bloqueio.horario_inicio, bloqueio.horario_fim)}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => bloqueio.id && handleRemover(bloqueio.id, 'recorrente')}
                    disabled={salvando}
                    className="text-red-600 hover:text-red-700"
                  >
                    Remover
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Lista de bloqueios específicos */}
      {bloqueiosEspecificos.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-[var(--text-primary)] mb-3">
            Bloqueios Específicos
          </h4>
          <div className="space-y-2">
            {bloqueiosEspecificos.map((bloqueio) => (
              <Card key={bloqueio.id} className="p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium text-[var(--text-primary)]">
                      {formatarData(bloqueio.data)}
                    </div>
                    <div className="text-sm text-[var(--text-secondary)]">
                      {bloqueio.descricao} • {formatarHorario(bloqueio.horario_inicio, bloqueio.horario_fim)}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => bloqueio.id && handleRemover(bloqueio.id, 'especifico')}
                    disabled={salvando}
                    className="text-red-600 hover:text-red-700"
                  >
                    Remover
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Estado vazio */}
      {bloqueiosRecorrentes.length === 0 && bloqueiosEspecificos.length === 0 && !mostrarFormulario && (
        <Card className="p-8 text-center">
          <div className="max-w-sm mx-auto">
            <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-[var(--text-primary)]">
              Nenhum bloqueio configurado
            </h3>
            <p className="mt-1 text-sm text-[var(--text-secondary)]">
              Configure bloqueios para dias ou horários específicos quando não estiver disponível.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
}
