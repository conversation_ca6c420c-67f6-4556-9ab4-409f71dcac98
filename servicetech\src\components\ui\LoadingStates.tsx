'use client';

import React, { memo } from 'react';
import { Card, CardContent, CardHeader } from './Card';

// Tipos para diferentes estados de loading
export type LoadingVariant = 'spinner' | 'skeleton' | 'pulse' | 'dots' | 'bars';
export type LoadingSize = 'sm' | 'md' | 'lg' | 'xl';

interface LoadingSpinnerProps {
  size?: LoadingSize;
  className?: string;
  color?: string;
}

interface LoadingSkeletonProps {
  lines?: number;
  className?: string;
  avatar?: boolean;
  width?: string;
  height?: string;
}

interface LoadingStateProps {
  variant?: LoadingVariant;
  size?: LoadingSize;
  message?: string;
  className?: string;
}

// Componente de spinner otimizado
export const LoadingSpinner = memo(function LoadingSpinner({
  size = 'md',
  className = '',
  color = 'var(--primary)'
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  return (
    <div className={`inline-block ${className}`}>
      <svg
        className={`animate-spin ${sizeClasses[size]}`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        style={{ color }}
        aria-hidden="true"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  );
});

// Componente de skeleton otimizado
export const LoadingSkeleton = memo(function LoadingSkeleton({
  lines = 3,
  className = '',
  avatar = false,
  width = '100%',
  height = '1rem'
}: LoadingSkeletonProps) {
  return (
    <div className={`animate-pulse ${className}`}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="rounded-full bg-gray-300 h-10 w-10"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      )}
      
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className="bg-gray-300 rounded"
            style={{
              width: index === lines - 1 ? '75%' : width,
              height
            }}
          />
        ))}
      </div>
    </div>
  );
});

// Componente de dots loading
export const LoadingDots = memo(function LoadingDots({
  size = 'md',
  className = ''
}: { size?: LoadingSize; className?: string }) {
  const dotSizes = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4'
  };

  return (
    <div className={`flex space-x-1 ${className}`}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className={`${dotSizes[size]} bg-[var(--primary)] rounded-full animate-bounce`}
          style={{
            animationDelay: `${index * 0.1}s`,
            animationDuration: '0.6s'
          }}
        />
      ))}
    </div>
  );
});

// Componente de bars loading
export const LoadingBars = memo(function LoadingBars({
  size = 'md',
  className = ''
}: { size?: LoadingSize; className?: string }) {
  const barSizes = {
    sm: 'w-1 h-4',
    md: 'w-1 h-6',
    lg: 'w-2 h-8',
    xl: 'w-2 h-10'
  };

  return (
    <div className={`flex items-end space-x-1 ${className}`}>
      {[0, 1, 2, 3].map((index) => (
        <div
          key={index}
          className={`${barSizes[size]} bg-[var(--primary)] animate-pulse`}
          style={{
            animationDelay: `${index * 0.15}s`,
            animationDuration: '1.2s'
          }}
        />
      ))}
    </div>
  );
});

// Componente principal de loading state
export const LoadingState = memo(function LoadingState({
  variant = 'spinner',
  size = 'md',
  message,
  className = ''
}: LoadingStateProps) {
  const renderLoadingComponent = () => {
    switch (variant) {
      case 'skeleton':
        return <LoadingSkeleton />;
      case 'dots':
        return <LoadingDots size={size} />;
      case 'bars':
        return <LoadingBars size={size} />;
      case 'pulse':
        return (
          <div className="animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-300 rounded w-1/2"></div>
          </div>
        );
      default:
        return <LoadingSpinner size={size} />;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center p-4 ${className}`}>
      {renderLoadingComponent()}
      {message && (
        <p className="mt-2 text-sm text-[var(--text-secondary)] text-center">
          {message}
        </p>
      )}
    </div>
  );
});

// Componente para loading de página inteira
export const PageLoading = memo(function PageLoading({
  message = 'Carregando...'
}: { message?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
      <div className="text-center">
        <LoadingSpinner size="xl" />
        <h2 className="text-xl font-semibold text-[var(--text-primary)] mt-4 mb-2">
          {message}
        </h2>
        <p className="text-[var(--text-secondary)]">
          Aguarde um momento.
        </p>
      </div>
    </div>
  );
});

// Componente para loading de card
export const CardLoading = memo(function CardLoading({
  title = 'Carregando...',
  lines = 3
}: { title?: string; lines?: number }) {
  return (
    <Card>
      <CardHeader>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded w-1/2"></div>
        </div>
      </CardHeader>
      <CardContent>
        <LoadingSkeleton lines={lines} />
      </CardContent>
    </Card>
  );
});

// Componente para loading de lista
export const ListLoading = memo(function ListLoading({
  items = 5,
  avatar = false
}: { items?: number; avatar?: boolean }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: items }).map((_, index) => (
        <Card key={index}>
          <CardContent className="p-4">
            <LoadingSkeleton lines={2} avatar={avatar} />
          </CardContent>
        </Card>
      ))}
    </div>
  );
});

// Componente para loading de tabela
export const TableLoading = memo(function TableLoading({
  rows = 5,
  columns = 4
}: { rows?: number; columns?: number }) {
  return (
    <div className="animate-pulse">
      {/* Header */}
      <div className="grid gap-4 mb-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-300 rounded"></div>
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4 mb-3" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="h-3 bg-gray-200 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  );
});

// Hook para gerenciar estados de loading
export function useLoadingState(initialLoading = false) {
  const [loading, setLoading] = React.useState(initialLoading);
  const [error, setError] = React.useState<string | null>(null);

  const startLoading = React.useCallback(() => {
    setLoading(true);
    setError(null);
  }, []);

  const stopLoading = React.useCallback(() => {
    setLoading(false);
  }, []);

  const setLoadingError = React.useCallback((errorMessage: string) => {
    setLoading(false);
    setError(errorMessage);
  }, []);

  const reset = React.useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  return {
    loading,
    error,
    startLoading,
    stopLoading,
    setLoadingError,
    reset
  };
}
