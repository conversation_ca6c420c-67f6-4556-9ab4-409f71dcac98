'use client';

import { useState, useEffect } from 'react';
import { useTestesUsuario } from '@/hooks/useTestesUsuario';
import { CenarioTeste, FiltrosCenarios } from '@/types/testesUsuario';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { FormularioCenario } from './FormularioCenario';

export function GerenciamentoCenarios() {
  const {
    cenarios,
    loading,
    error,
    buscarCenarios,
    excluirCenario
  } = useTestesUsuario();

  const [filtros, setFiltros] = useState<FiltrosCenarios>({});
  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [cenarioEditando, setCenarioEditando] = useState<CenarioTeste | null>(null);
  const [confirmandoExclusao, setConfirmandoExclusao] = useState<number | null>(null);

  useEffect(() => {
    buscarCenarios(filtros);
  }, [buscarCenarios, filtros]);

  const handleFiltroChange = (campo: keyof FiltrosCenarios, valor: any) => {
    setFiltros(prev => ({
      ...prev,
      [campo]: valor === '' ? undefined : valor
    }));
  };

  const handleExcluir = async (cenarioId: number) => {
    try {
      await excluirCenario(cenarioId);
      setConfirmandoExclusao(null);
    } catch (error) {
      console.error('Erro ao excluir cenário:', error);
    }
  };

  const handleEditar = (cenario: CenarioTeste) => {
    setCenarioEditando(cenario);
    setMostrarFormulario(true);
  };

  const handleFecharFormulario = () => {
    setMostrarFormulario(false);
    setCenarioEditando(null);
  };

  const getDificuldadeColor = (dificuldade: string) => {
    switch (dificuldade) {
      case 'Facil': return 'text-green-600 bg-green-100';
      case 'Medio': return 'text-yellow-600 bg-yellow-100';
      case 'Dificil': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoriaColor = (categoria: string) => {
    switch (categoria) {
      case 'Onboarding': return 'text-blue-600 bg-blue-100';
      case 'Agendamento': return 'text-purple-600 bg-purple-100';
      case 'Gestao': return 'text-indigo-600 bg-indigo-100';
      case 'Pagamento': return 'text-green-600 bg-green-100';
      case 'Navegacao': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (mostrarFormulario) {
    return (
      <FormularioCenario
        cenario={cenarioEditando}
        onClose={handleFecharFormulario}
        onSuccess={() => {
          handleFecharFormulario();
          buscarCenarios(filtros);
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Cenários de Teste
          </h2>
          <p className="text-gray-600 mt-1">
            Gerencie os cenários de teste para validação de usabilidade
          </p>
        </div>
        <Button
          onClick={() => setMostrarFormulario(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          + Novo Cenário
        </Button>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Filtros</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Papel do Usuário
              </label>
              <select
                value={filtros.papel_usuario || ''}
                onChange={(e) => handleFiltroChange('papel_usuario', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos</option>
                <option value="Administrador">Administrador</option>
                <option value="Proprietario">Proprietário</option>
                <option value="Colaborador">Colaborador</option>
                <option value="Cliente">Cliente</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Categoria
              </label>
              <select
                value={filtros.categoria || ''}
                onChange={(e) => handleFiltroChange('categoria', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todas</option>
                <option value="Onboarding">Onboarding</option>
                <option value="Agendamento">Agendamento</option>
                <option value="Gestao">Gestão</option>
                <option value="Pagamento">Pagamento</option>
                <option value="Navegacao">Navegação</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dificuldade
              </label>
              <select
                value={filtros.dificuldade || ''}
                onChange={(e) => handleFiltroChange('dificuldade', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todas</option>
                <option value="Facil">Fácil</option>
                <option value="Medio">Médio</option>
                <option value="Dificil">Difícil</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filtros.ativo !== undefined ? filtros.ativo.toString() : ''}
                onChange={(e) => handleFiltroChange('ativo', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos</option>
                <option value="true">Ativo</option>
                <option value="false">Inativo</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Cenários */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Carregando cenários...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-600">Erro: {error}</p>
          <Button
            onClick={() => buscarCenarios(filtros)}
            className="mt-2"
          >
            Tentar Novamente
          </Button>
        </div>
      ) : cenarios.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-600">Nenhum cenário encontrado</p>
          <Button
            onClick={() => setMostrarFormulario(true)}
            className="mt-2 bg-blue-600 hover:bg-blue-700"
          >
            Criar Primeiro Cenário
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {cenarios.map((cenario) => (
            <Card key={cenario.cenario_id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {cenario.nome_cenario}
                    </h3>
                    <div className="flex flex-wrap gap-2 mb-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoriaColor(cenario.categoria)}`}>
                        {cenario.categoria}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDificuldadeColor(cenario.dificuldade)}`}>
                        {cenario.dificuldade}
                      </span>
                      <span className="px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                        {cenario.papel_usuario}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        cenario.ativo ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
                      }`}>
                        {cenario.ativo ? 'Ativo' : 'Inativo'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4 line-clamp-2">
                  {cenario.descricao}
                </p>
                
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Tempo estimado:</span>
                    <p className="text-gray-600">{cenario.tempo_estimado_minutos} min</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Passos:</span>
                    <p className="text-gray-600">{cenario.passos.length} passos</p>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    onClick={() => handleEditar(cenario)}
                    className="bg-blue-600 hover:bg-blue-700 text-sm"
                  >
                    Editar
                  </Button>
                  <Button
                    onClick={() => setConfirmandoExclusao(cenario.cenario_id)}
                    className="bg-red-600 hover:bg-red-700 text-sm"
                  >
                    Excluir
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Modal de Confirmação de Exclusão */}
      {confirmandoExclusao && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Confirmar Exclusão
            </h3>
            <p className="text-gray-600 mb-6">
              Tem certeza que deseja excluir este cenário? Esta ação não pode ser desfeita.
            </p>
            <div className="flex justify-end space-x-3">
              <Button
                onClick={() => setConfirmandoExclusao(null)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700"
              >
                Cancelar
              </Button>
              <Button
                onClick={() => handleExcluir(confirmandoExclusao)}
                className="bg-red-600 hover:bg-red-700"
                disabled={loading}
              >
                {loading ? 'Excluindo...' : 'Excluir'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
