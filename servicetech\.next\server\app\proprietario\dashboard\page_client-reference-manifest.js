globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/proprietario/dashboard/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/accessibility/SkipLinks.tsx":{"*":{"id":"(ssr)/./src/components/accessibility/SkipLinks.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/onboarding/OnboardingTour.tsx":{"*":{"id":"(ssr)/./src/components/onboarding/OnboardingTour.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/PWAPrompt.tsx":{"*":{"id":"(ssr)/./src/components/ui/PWAPrompt.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/BrandingContext.tsx":{"*":{"id":"(ssr)/./src/contexts/BrandingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/OnboardingContext.tsx":{"*":{"id":"(ssr)/./src/contexts/OnboardingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/ThemeContext.tsx":{"*":{"id":"(ssr)/./src/contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientButtons.tsx":{"*":{"id":"(ssr)/./src/components/ClientButtons.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CTABottomButtons.tsx":{"*":{"id":"(ssr)/./src/components/CTABottomButtons.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Layout.tsx":{"*":{"id":"(ssr)/./src/components/layout/Layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/Card.tsx":{"*":{"id":"(ssr)/./src/components/ui/Card.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/buscar/page.tsx":{"*":{"id":"(ssr)/./src/app/buscar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/Breadcrumbs.tsx":{"*":{"id":"(ssr)/./src/components/ui/Breadcrumbs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/Button.tsx":{"*":{"id":"(ssr)/./src/components/ui/Button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/cadastro/page.tsx":{"*":{"id":"(ssr)/./src/app/cadastro/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/estabelecimento/[slug]/page.tsx":{"*":{"id":"(ssr)/./src/app/estabelecimento/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/proprietario/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/proprietario/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/proprietario/servicos/page.tsx":{"*":{"id":"(ssr)/./src/app/proprietario/servicos/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\accessibility\\SkipLinks.tsx":{"id":"(app-pages-browser)/./src/components/accessibility/SkipLinks.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\onboarding\\OnboardingTour.tsx":{"id":"(app-pages-browser)/./src/components/onboarding/OnboardingTour.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ui\\PWAPrompt.tsx":{"id":"(app-pages-browser)/./src/components/ui/PWAPrompt.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\BrandingContext.tsx":{"id":"(app-pages-browser)/./src/contexts/BrandingContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\OnboardingContext.tsx":{"id":"(app-pages-browser)/./src/contexts/OnboardingContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\ThemeContext.tsx":{"id":"(app-pages-browser)/./src/contexts/ThemeContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ClientButtons.tsx":{"id":"(app-pages-browser)/./src/components/ClientButtons.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\CTABottomButtons.tsx":{"id":"(app-pages-browser)/./src/components/CTABottomButtons.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\layout\\Layout.tsx":{"id":"(app-pages-browser)/./src/components/layout/Layout.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ui\\Card.tsx":{"id":"(app-pages-browser)/./src/components/ui/Card.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\buscar\\page.tsx":{"id":"(app-pages-browser)/./src/app/buscar/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ui\\Breadcrumbs.tsx":{"id":"(app-pages-browser)/./src/components/ui/Breadcrumbs.tsx","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ui\\Button.tsx":{"id":"(app-pages-browser)/./src/components/ui/Button.tsx","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\cadastro\\page.tsx":{"id":"(app-pages-browser)/./src/app/cadastro/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\estabelecimento\\[slug]\\page.tsx":{"id":"(app-pages-browser)/./src/app/estabelecimento/[slug]/page.tsx","name":"*","chunks":[],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\proprietario\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/proprietario/dashboard/page.tsx","name":"*","chunks":["app/proprietario/dashboard/page","static/chunks/app/proprietario/dashboard/page.js"],"async":false},"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\proprietario\\servicos\\page.tsx":{"id":"(app-pages-browser)/./src/app/proprietario/servicos/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Projetos\\1\\geremias\\servicetech\\src\\":[],"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\page":[],"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\proprietario\\dashboard\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/accessibility/SkipLinks.tsx":{"*":{"id":"(rsc)/./src/components/accessibility/SkipLinks.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/onboarding/OnboardingTour.tsx":{"*":{"id":"(rsc)/./src/components/onboarding/OnboardingTour.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/PWAPrompt.tsx":{"*":{"id":"(rsc)/./src/components/ui/PWAPrompt.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/BrandingContext.tsx":{"*":{"id":"(rsc)/./src/contexts/BrandingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/OnboardingContext.tsx":{"*":{"id":"(rsc)/./src/contexts/OnboardingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/ThemeContext.tsx":{"*":{"id":"(rsc)/./src/contexts/ThemeContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ClientButtons.tsx":{"*":{"id":"(rsc)/./src/components/ClientButtons.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/CTABottomButtons.tsx":{"*":{"id":"(rsc)/./src/components/CTABottomButtons.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Layout.tsx":{"*":{"id":"(rsc)/./src/components/layout/Layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/Card.tsx":{"*":{"id":"(rsc)/./src/components/ui/Card.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/buscar/page.tsx":{"*":{"id":"(rsc)/./src/app/buscar/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/Breadcrumbs.tsx":{"*":{"id":"(rsc)/./src/components/ui/Breadcrumbs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/Button.tsx":{"*":{"id":"(rsc)/./src/components/ui/Button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/cadastro/page.tsx":{"*":{"id":"(rsc)/./src/app/cadastro/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/estabelecimento/[slug]/page.tsx":{"*":{"id":"(rsc)/./src/app/estabelecimento/[slug]/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/proprietario/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/proprietario/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/proprietario/servicos/page.tsx":{"*":{"id":"(rsc)/./src/app/proprietario/servicos/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}