'use client';

import { useState, useEffect } from 'react';
import { useTestesUsuario } from '@/hooks/useTestesUsuario';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

export function RelatoriosTestes() {
  const { sessoes, buscarSessoes, loading } = useTestesUsuario();
  const [sessoesConcluidas, setSessoesConcluidas] = useState<any[]>([]);

  useEffect(() => {
    buscarSessoes({ status: 'Concluida' });
  }, [buscarSessoes]);

  useEffect(() => {
    setSessoesConcluidas(sessoes.filter(s => s.status === 'Concluida'));
  }, [sessoes]);

  const formatarData = (dataString: string) => {
    return new Date(dataString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Relatórios de Testes
          </h2>
          <p className="text-gray-600 mt-1">
            Análise e métricas das sessões de teste realizadas
          </p>
        </div>
      </div>

      {/* Estatísticas Gerais */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-lg">📊</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Sessões Concluídas</p>
                <p className="text-2xl font-bold text-gray-900">{sessoesConcluidas.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 text-lg">👥</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Participantes</p>
                <p className="text-2xl font-bold text-gray-900">
                  {sessoesConcluidas.reduce((acc, s) => acc + s.participantes_confirmados, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600 text-lg">✅</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Taxa de Sucesso</p>
                <p className="text-2xl font-bold text-gray-900">--%</p>
                <p className="text-xs text-gray-500">Em desenvolvimento</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-600 text-lg">⭐</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Satisfação Média</p>
                <p className="text-2xl font-bold text-gray-900">-/10</p>
                <p className="text-xs text-gray-500">Em desenvolvimento</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Sessões para Relatório */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Sessões Disponíveis para Relatório</h3>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Carregando sessões...</p>
            </div>
          ) : sessoesConcluidas.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-gray-400 text-2xl">📊</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nenhuma sessão concluída
              </h3>
              <p className="text-gray-600 mb-4">
                Complete algumas sessões de teste para visualizar relatórios e métricas.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {sessoesConcluidas.map((sessao) => (
                <div
                  key={sessao.sessao_id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 mb-2">
                        {sessao.nome_sessao}
                      </h4>
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {sessao.descricao}
                      </p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Data:</span>
                          <p className="text-gray-600">{formatarData(sessao.data_inicio)}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Participantes:</span>
                          <p className="text-gray-600">{sessao.participantes_confirmados}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Cenários:</span>
                          <p className="text-gray-600">{sessao.cenarios_incluidos.length}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Status:</span>
                          <span className="px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                            Concluída
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="ml-4 flex flex-col space-y-2">
                      <Button
                        onClick={() => {/* TODO: Implementar visualização de relatório */}}
                        className="bg-blue-600 hover:bg-blue-700 text-sm"
                      >
                        Ver Relatório
                      </Button>
                      <Button
                        onClick={() => {/* TODO: Implementar download de relatório */}}
                        className="bg-green-600 hover:bg-green-700 text-sm"
                      >
                        Download PDF
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Métricas em Desenvolvimento */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">🚧 Funcionalidades em Desenvolvimento</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Métricas Quantitativas</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Taxa de sucesso por cenário
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Tempo médio de conclusão
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Número de erros por tarefa
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Taxa de abandono
                </li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Análise Qualitativa</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Pontuação de satisfação (NPS)
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Feedback qualitativo
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Problemas identificados
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Sugestões de melhoria
                </li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Sistema de Relatórios em Desenvolvimento
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    As funcionalidades de coleta de métricas, análise de feedback e geração 
                    de relatórios detalhados estão sendo desenvolvidas. Em breve você poderá:
                  </p>
                  <ul className="mt-2 list-disc list-inside space-y-1">
                    <li>Visualizar métricas detalhadas de cada sessão</li>
                    <li>Comparar performance entre diferentes cenários</li>
                    <li>Exportar relatórios em PDF e Excel</li>
                    <li>Acompanhar evolução das métricas ao longo do tempo</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
