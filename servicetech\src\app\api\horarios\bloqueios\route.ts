import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { BloqueioRecorrente, BloqueioEspecifico } from '@/types/horarios';

// POST - Adicionar bloqueio
export async function POST(request: Request) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { colaborador_user_id, bloqueio } = body;

    if (!colaborador_user_id || !bloqueio) {
      return NextResponse.json(
        { success: false, error: 'ID do colaborador e dados do bloqueio são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar permissões
    const userRole = user.user_metadata?.role;
    let podeAdicionar = false;

    if (userRole === 'Proprietario') {
      // Proprietário pode adicionar bloqueios para seus colaboradores
      const { data: empresa } = await supabase
        .from('empresas')
        .select('empresa_id')
        .eq('proprietario_user_id', user.id)
        .single();

      if (empresa) {
        const { data: colaborador } = await supabase
          .from('colaboradores_empresa')
          .select('colaborador_user_id')
          .eq('empresa_id', empresa.empresa_id)
          .eq('colaborador_user_id', colaborador_user_id)
          .eq('ativo', true)
          .single();

        podeAdicionar = !!colaborador;
      }
    } else if (userRole === 'Colaborador' && user.id === colaborador_user_id) {
      // Colaborador pode adicionar bloqueios para si mesmo
      podeAdicionar = true;
    }

    if (!podeAdicionar) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Validar bloqueio
    const validacao = validarBloqueio(bloqueio);
    if (!validacao.valido) {
      return NextResponse.json(
        { success: false, error: `Bloqueio inválido: ${validacao.erros.join(', ')}` },
        { status: 400 }
      );
    }

    // Buscar horários atuais do colaborador
    const { data: colaboradorData, error: colaboradorError } = await supabase
      .from('colaboradores_empresa')
      .select('horarios_trabalho_individual')
      .eq('colaborador_user_id', colaborador_user_id)
      .eq('ativo', true)
      .single();

    if (colaboradorError) {
      console.error('Erro ao buscar colaborador:', colaboradorError);
      return NextResponse.json(
        { success: false, error: 'Colaborador não encontrado' },
        { status: 404 }
      );
    }

    const horariosAtuais = colaboradorData.horarios_trabalho_individual || {
      colaborador_user_id,
      empresa_id: 0, // Será preenchido
      horarios: {},
      bloqueios_recorrentes: [],
      bloqueios_especificos: []
    };

    // Gerar ID único para o bloqueio
    const bloqueioId = `bloqueio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    bloqueio.id = bloqueioId;

    // Adicionar bloqueio baseado no tipo
    if (isBloqueioRecorrente(bloqueio)) {
      if (!horariosAtuais.bloqueios_recorrentes) {
        horariosAtuais.bloqueios_recorrentes = [];
      }
      horariosAtuais.bloqueios_recorrentes.push(bloqueio);
    } else {
      if (!horariosAtuais.bloqueios_especificos) {
        horariosAtuais.bloqueios_especificos = [];
      }
      horariosAtuais.bloqueios_especificos.push(bloqueio);
    }

    // Verificar conflitos com agendamentos existentes
    const conflitos = await verificarConflitosAgendamento(
      supabase,
      colaborador_user_id,
      bloqueio
    );

    if (conflitos.tem_conflitos) {
      return NextResponse.json({
        success: false,
        error: 'Conflito com agendamentos existentes',
        conflitos: conflitos.agendamentos,
        pode_prosseguir: false
      }, { status: 409 });
    }

    // Atualizar horários do colaborador
    const { error: updateError } = await supabase
      .from('colaboradores_empresa')
      .update({ 
        horarios_trabalho_individual: horariosAtuais,
        updated_at: new Date().toISOString()
      })
      .eq('colaborador_user_id', colaborador_user_id)
      .eq('ativo', true);

    if (updateError) {
      console.error('Erro ao adicionar bloqueio:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao adicionar bloqueio' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Bloqueio adicionado com sucesso',
      data: { bloqueio_id: bloqueioId, bloqueio }
    });

  } catch (error: any) {
    console.error('Erro geral ao adicionar bloqueio:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Remover bloqueio
export async function DELETE(request: Request) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { colaborador_user_id, bloqueio_id, tipo } = body;

    if (!colaborador_user_id || !bloqueio_id || !tipo) {
      return NextResponse.json(
        { success: false, error: 'ID do colaborador, ID do bloqueio e tipo são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar permissões (mesmo código do POST)
    const userRole = user.user_metadata?.role;
    let podeRemover = false;

    if (userRole === 'Proprietario') {
      const { data: empresa } = await supabase
        .from('empresas')
        .select('empresa_id')
        .eq('proprietario_user_id', user.id)
        .single();

      if (empresa) {
        const { data: colaborador } = await supabase
          .from('colaboradores_empresa')
          .select('colaborador_user_id')
          .eq('empresa_id', empresa.empresa_id)
          .eq('colaborador_user_id', colaborador_user_id)
          .eq('ativo', true)
          .single();

        podeRemover = !!colaborador;
      }
    } else if (userRole === 'Colaborador' && user.id === colaborador_user_id) {
      podeRemover = true;
    }

    if (!podeRemover) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Buscar horários atuais do colaborador
    const { data: colaboradorData, error: colaboradorError } = await supabase
      .from('colaboradores_empresa')
      .select('horarios_trabalho_individual')
      .eq('colaborador_user_id', colaborador_user_id)
      .eq('ativo', true)
      .single();

    if (colaboradorError) {
      console.error('Erro ao buscar colaborador:', colaboradorError);
      return NextResponse.json(
        { success: false, error: 'Colaborador não encontrado' },
        { status: 404 }
      );
    }

    const horariosAtuais = colaboradorData.horarios_trabalho_individual;
    if (!horariosAtuais) {
      return NextResponse.json(
        { success: false, error: 'Horários do colaborador não encontrados' },
        { status: 404 }
      );
    }

    // Remover bloqueio baseado no tipo
    let bloqueioRemovido = false;

    if (tipo === 'recorrente' && horariosAtuais.bloqueios_recorrentes) {
      const indiceOriginal = horariosAtuais.bloqueios_recorrentes.length;
      horariosAtuais.bloqueios_recorrentes = horariosAtuais.bloqueios_recorrentes.filter(
        (b: BloqueioRecorrente) => b.id !== bloqueio_id
      );
      bloqueioRemovido = horariosAtuais.bloqueios_recorrentes.length < indiceOriginal;
    } else if (tipo === 'especifico' && horariosAtuais.bloqueios_especificos) {
      const indiceOriginal = horariosAtuais.bloqueios_especificos.length;
      horariosAtuais.bloqueios_especificos = horariosAtuais.bloqueios_especificos.filter(
        (b: BloqueioEspecifico) => b.id !== bloqueio_id
      );
      bloqueioRemovido = horariosAtuais.bloqueios_especificos.length < indiceOriginal;
    }

    if (!bloqueioRemovido) {
      return NextResponse.json(
        { success: false, error: 'Bloqueio não encontrado' },
        { status: 404 }
      );
    }

    // Atualizar horários do colaborador
    const { error: updateError } = await supabase
      .from('colaboradores_empresa')
      .update({ 
        horarios_trabalho_individual: horariosAtuais,
        updated_at: new Date().toISOString()
      })
      .eq('colaborador_user_id', colaborador_user_id)
      .eq('ativo', true);

    if (updateError) {
      console.error('Erro ao remover bloqueio:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao remover bloqueio' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Bloqueio removido com sucesso'
    });

  } catch (error: any) {
    console.error('Erro geral ao remover bloqueio:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Função para validar bloqueio
function validarBloqueio(bloqueio: any): { valido: boolean; erros: string[] } {
  const erros: string[] = [];

  if (!bloqueio.descricao || bloqueio.descricao.trim().length === 0) {
    erros.push('Descrição é obrigatória');
  }

  if (isBloqueioRecorrente(bloqueio)) {
    const dias = ['segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado', 'domingo'];
    if (!bloqueio.dia_semana || !dias.includes(bloqueio.dia_semana)) {
      erros.push('Dia da semana inválido');
    }
  } else {
    if (!bloqueio.data || !/^\d{4}-\d{2}-\d{2}$/.test(bloqueio.data)) {
      erros.push('Data inválida (use YYYY-MM-DD)');
    }

    if (!bloqueio.tipo || !['folga', 'compromisso', 'manutencao', 'outro'].includes(bloqueio.tipo)) {
      erros.push('Tipo de bloqueio inválido');
    }
  }

  // Validar horários se especificados
  if (bloqueio.horario_inicio && bloqueio.horario_fim) {
    const regexHorario = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!regexHorario.test(bloqueio.horario_inicio) || !regexHorario.test(bloqueio.horario_fim)) {
      erros.push('Formato de horário inválido (use HH:mm)');
    } else {
      const inicio = new Date(`2000-01-01T${bloqueio.horario_inicio}:00`);
      const fim = new Date(`2000-01-01T${bloqueio.horario_fim}:00`);
      if (inicio >= fim) {
        erros.push('Horário de fim deve ser posterior ao de início');
      }
    }
  }

  return {
    valido: erros.length === 0,
    erros
  };
}

// Função para verificar se é bloqueio recorrente
function isBloqueioRecorrente(bloqueio: any): bloqueio is BloqueioRecorrente {
  return 'dia_semana' in bloqueio;
}

// Função para verificar conflitos com agendamentos
async function verificarConflitosAgendamento(
  supabase: any,
  colaboradorId: string,
  bloqueio: BloqueioRecorrente | BloqueioEspecifico
): Promise<{ tem_conflitos: boolean; agendamentos: any[] }> {
  try {
    // Por enquanto, retorna sem conflitos
    // TODO: Implementar verificação real quando a tabela de agendamentos estiver pronta
    return { tem_conflitos: false, agendamentos: [] };
  } catch (error) {
    console.error('Erro ao verificar conflitos:', error);
    return { tem_conflitos: false, agendamentos: [] };
  }
}
