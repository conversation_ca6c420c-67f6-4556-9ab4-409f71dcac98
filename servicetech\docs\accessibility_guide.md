# Guia de Acessibilidade - ServiceTech

## 📋 Visão Geral

Este documento descreve as implementações de acessibilidade no projeto ServiceTech, seguindo as diretrizes WCAG 2.1 AA e melhores práticas de UX.

## 🎯 Objetivos de Acessibilidade

- **WCAG 2.1 AA Compliance**: Atender aos critérios de acessibilidade nível AA
- **Navegação por Teclado**: Suporte completo para navegação sem mouse
- **Screen Reader Support**: Compatibilidade com leitores de tela
- **Alto Contraste**: Suporte para preferências de contraste elevado
- **Movimento Reduzido**: Respeitar preferências de movimento reduzido

## 🛠️ Componentes Implementados

### 1. Skip Links
**Localização**: `src/components/accessibility/SkipLinks.tsx`

- Links de navegação rápida para conteúdo principal
- Visíveis apenas quando focados
- Suporte a navegação por teclado

```tsx
<SkipLinks />
```

### 2. Componentes de UI Acessíveis

#### Button Component
**Localização**: `src/components/ui/Button.tsx`

Melhorias implementadas:
- Estados de loading com indicadores visuais e textuais
- Suporte a aria-labels e aria-describedby
- Focus management aprimorado
- Estados disabled acessíveis

```tsx
<Button 
  loading={isLoading}
  loadingText="Salvando..."
  aria-label="Salvar formulário"
>
  Salvar
</Button>
```

#### Input Component
**Localização**: `src/components/ui/Input.tsx`

Melhorias implementadas:
- Labels associados automaticamente
- Mensagens de erro com role="alert"
- Texto de ajuda descritivo
- Indicadores de campos obrigatórios
- IDs únicos gerados automaticamente

```tsx
<Input
  label="Email"
  type="email"
  required
  error={errors.email}
  helperText="Digite seu email principal"
/>
```

### 3. Formulários Acessíveis
**Localização**: `src/components/forms/AccessibleForm.tsx`

Recursos:
- Validação em tempo real acessível
- Gerenciamento de foco automático
- Agrupamento de campos relacionados (fieldsets)
- Mensagens de erro contextuais

```tsx
<AccessibleForm
  title="Cadastro de Usuário"
  description="Preencha os dados para criar sua conta"
  onSubmit={handleSubmit}
>
  <FormField
    id="name"
    label="Nome Completo"
    required
    value={values.name}
    onChange={handleChange('name')}
    onBlur={handleBlur('name')}
    error={errors.name}
  />
</AccessibleForm>
```

### 4. Tabelas Acessíveis
**Localização**: `src/components/ui/AccessibleTable.tsx`

Recursos:
- Navegação por teclado (setas, Home, End)
- Cabeçalhos associados corretamente
- Ordenação acessível
- Seleção múltipla acessível
- Estados de loading e vazio

```tsx
<AccessibleTable
  data={agendamentos}
  columns={columns}
  caption="Lista de agendamentos"
  sortable
  selectable
  onSort={handleSort}
  onSelectionChange={handleSelection}
/>
```

### 5. Utilitários de Acessibilidade
**Localização**: `src/components/accessibility/AccessibilityUtils.tsx`

#### Screen Reader Announcements
```tsx
<ScreenReaderAnnouncement 
  message="Dados salvos com sucesso"
  priority="assertive"
/>
```

#### Focus Management Hook
```tsx
const { trapFocus, saveFocus, restoreFocus } = useFocusManagement();
```

#### Accessibility Preferences Hook
```tsx
const { reducedMotion, highContrast } = useAccessibilityPreferences();
```

## 🎨 Estilos de Acessibilidade

### CSS Personalizado
**Localização**: `src/styles/accessibility.css`

Recursos implementados:
- Classes sr-only para conteúdo apenas para screen readers
- Estilos de foco visível aprimorados
- Suporte a prefers-reduced-motion
- Suporte a prefers-contrast: high
- Estilos para indicadores de estado

### Media Queries de Acessibilidade

```css
/* Alto Contraste */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --background: #ffffff;
    --border-color: #000000;
  }
}

/* Movimento Reduzido */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

## 🔧 Implementações Específicas

### 1. Header Navigation
**Melhorias implementadas**:
- Landmarks semânticos (role="banner", role="navigation")
- Menu mobile com aria-expanded e aria-controls
- Skip links para navegação rápida
- Focus management em menus dropdown

### 2. Modal Components
**Melhorias implementadas**:
- Focus trap automático
- Fechamento com Escape
- Aria-modal e role="dialog"
- Restauração de foco ao fechar

### 3. Form Validation
**Melhorias implementadas**:
- Mensagens de erro com aria-live
- Validação em tempo real acessível
- Indicadores visuais e textuais de estado
- Agrupamento lógico de campos

## 📱 Responsividade e Mobile

### Touch Targets
- Tamanho mínimo de 44px para elementos interativos
- Espaçamento adequado entre elementos clicáveis
- Suporte a gestos de acessibilidade mobile

### Zoom e Redimensionamento
- Suporte a zoom até 200% sem perda de funcionalidade
- Layout responsivo que se adapta a diferentes tamanhos
- Texto legível em todas as resoluções

## 🧪 Testes de Acessibilidade

### Ferramentas Recomendadas
1. **axe-core**: Testes automatizados de acessibilidade
2. **WAVE**: Avaliação visual de acessibilidade
3. **Lighthouse**: Auditoria de acessibilidade do Chrome
4. **Screen Readers**: NVDA, JAWS, VoiceOver

### Checklist de Testes
- [ ] Navegação completa por teclado
- [ ] Leitura correta por screen readers
- [ ] Contraste de cores adequado (4.5:1 para texto normal)
- [ ] Elementos interativos com tamanho mínimo de 44px
- [ ] Formulários com labels e validação acessível
- [ ] Imagens com alt text apropriado
- [ ] Vídeos com legendas (quando aplicável)

## 🚀 Próximos Passos

### Melhorias Planejadas
1. **Testes Automatizados**: Integração de testes de acessibilidade no CI/CD
2. **Documentação Interativa**: Guias de uso para desenvolvedores
3. **Treinamento**: Sessões de capacitação em acessibilidade
4. **Auditoria Externa**: Avaliação por especialistas em acessibilidade

### Monitoramento Contínuo
- Revisões regulares de acessibilidade
- Feedback de usuários com deficiências
- Atualizações conforme novas diretrizes WCAG
- Testes com tecnologias assistivas

## 📚 Recursos e Referências

### Documentação Oficial
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [WAI-ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [MDN Accessibility](https://developer.mozilla.org/en-US/docs/Web/Accessibility)

### Ferramentas de Desenvolvimento
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [jest-axe](https://github.com/nickcolley/jest-axe)
- [eslint-plugin-jsx-a11y](https://github.com/jsx-eslint/eslint-plugin-jsx-a11y)

## 🤝 Contribuindo

### Diretrizes para Desenvolvedores
1. Sempre incluir aria-labels em elementos interativos
2. Testar navegação por teclado em novos componentes
3. Verificar contraste de cores antes do commit
4. Incluir testes de acessibilidade em PRs
5. Documentar recursos de acessibilidade implementados

### Code Review Checklist
- [ ] Elementos semânticos utilizados corretamente
- [ ] Aria attributes apropriados
- [ ] Navegação por teclado funcional
- [ ] Mensagens de erro acessíveis
- [ ] Focus management implementado
- [ ] Testes de acessibilidade incluídos
