/**
 * <PERSON>ript para atualizar o slug da empresa Barbearia Santos
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

// Cliente administrativo do Supabase
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

/**
 * Gera um slug a partir de um texto
 */
function generateSlug(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    .toLowerCase()
    .trim()
    // Remover acentos e caracteres especiais
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    // Substituir espaços e caracteres especiais por hífens
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    // Remover hífens do início e fim
    .replace(/^-+|-+$/g, '');
}

async function verificarEmpresa() {
  try {
    console.log('🔍 Verificando empresas Barbearia Santos...');

    const { data: empresas, error } = await supabase
      .from('empresas')
      .select('empresa_id, nome_empresa, slug, status, created_at')
      .eq('nome_empresa', 'Barbearia Santos')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Erro ao buscar empresas:', error);
      return null;
    }

    if (!empresas || empresas.length === 0) {
      console.log('❌ Nenhuma empresa encontrada com o nome "Barbearia Santos"');
      return null;
    }

    console.log(`✅ ${empresas.length} empresa(s) encontrada(s):`);
    empresas.forEach((emp, index) => {
      console.log(`   ${index + 1}. ID: ${emp.empresa_id} | Slug: ${emp.slug || 'NULL'} | Status: ${emp.status}`);
    });
    console.log('');

    // Retornar a mais recente
    const empresa = empresas[0];
    console.log('📌 Usando a empresa mais recente:');
    console.log(`   ID: ${empresa.empresa_id}`);
    console.log(`   Nome: ${empresa.nome_empresa}`);
    console.log(`   Slug atual: ${empresa.slug || 'NULL'}`);
    console.log(`   Status: ${empresa.status}`);
    console.log('');

    return empresa;
  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    return null;
  }
}

async function atualizarSlug(empresaId, nomeEmpresa) {
  try {
    console.log('🔧 Gerando e atualizando slug...');
    
    const novoSlug = generateSlug(nomeEmpresa);
    console.log(`   Novo slug: ${novoSlug}`);

    const { data, error } = await supabase
      .from('empresas')
      .update({ slug: novoSlug })
      .eq('empresa_id', empresaId)
      .select()
      .single();

    if (error) {
      console.error('❌ Erro ao atualizar slug:', error);
      return false;
    }

    console.log('✅ Slug atualizado com sucesso!');
    console.log(`   Empresa ID: ${data.empresa_id}`);
    console.log(`   Nome: ${data.nome_empresa}`);
    console.log(`   Novo slug: ${data.slug}`);
    console.log('');

    return true;
  } catch (error) {
    console.error('❌ Erro inesperado ao atualizar slug:', error);
    return false;
  }
}

async function testarAPI(slug) {
  try {
    console.log('🧪 Testando API com o novo slug...');
    
    // Simular chamada da API (não podemos usar fetch aqui)
    const { data: empresa, error } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        slug,
        status,
        telefone,
        endereco,
        numero,
        bairro,
        cidade,
        estado
      `)
      .eq('slug', slug)
      .eq('status', 'ativo')
      .single();

    if (error) {
      console.error('❌ Erro ao testar busca por slug:', error);
      return false;
    }

    console.log('✅ Busca por slug funcionando!');
    console.log(`   Empresa encontrada: ${empresa.nome_empresa}`);
    console.log(`   ID: ${empresa.empresa_id}`);
    console.log(`   Slug: ${empresa.slug}`);
    console.log(`   Endereço: ${empresa.endereco}, ${empresa.numero} - ${empresa.bairro}`);
    console.log('');

    return true;
  } catch (error) {
    console.error('❌ Erro inesperado no teste:', error);
    return false;
  }
}

async function main() {
  try {
    console.log('🚀 Iniciando atualização de slug...\n');

    // 1. Verificar empresa
    const empresa = await verificarEmpresa();
    if (!empresa) {
      console.log('❌ Empresa não encontrada. Abortando...');
      return;
    }

    // 2. Atualizar slug se necessário
    if (!empresa.slug || empresa.slug.trim() === '') {
      const sucesso = await atualizarSlug(empresa.empresa_id, empresa.nome_empresa);
      if (!sucesso) {
        console.log('❌ Falha ao atualizar slug. Abortando...');
        return;
      }
    } else {
      console.log('ℹ️ Empresa já possui slug definido.');
    }

    // 3. Testar API
    const slugFinal = empresa.slug || generateSlug(empresa.nome_empresa);
    const testeOK = await testarAPI(slugFinal);

    console.log('=' .repeat(60));
    if (testeOK) {
      console.log('🎉 SLUG CONFIGURADO COM SUCESSO!');
      console.log('=' .repeat(60));
      console.log(`🔗 URL da empresa: http://localhost:3000/estabelecimento/${slugFinal}`);
      console.log(`📡 API endpoint: http://localhost:3000/api/empresas/${slugFinal}`);
      console.log('');
      console.log('📝 PRÓXIMOS PASSOS:');
      console.log('1. Teste a URL no navegador');
      console.log('2. Verifique se todos os dados aparecem corretamente');
      console.log('3. Teste também com o ID numérico para compatibilidade');
    } else {
      console.log('❌ PROBLEMA NA CONFIGURAÇÃO DO SLUG');
      console.log('=' .repeat(60));
      console.log('Verifique os logs acima para mais detalhes.');
    }

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar script
main();
