'use client';

import React, { useState, useRef, useEffect } from 'react';

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  trigger?: 'hover' | 'click' | 'focus';
  delay?: number;
  className?: string;
  disabled?: boolean;
  maxWidth?: string;
}

export function ContextualTooltip({
  content,
  children,
  position = 'top',
  trigger = 'hover',
  delay = 300,
  className = '',
  disabled = false,
  maxWidth = '200px',
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showTooltip = () => {
    if (disabled) return;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      updatePosition();
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const updatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    let top = 0;
    let left = 0;

    switch (position) {
      case 'top':
        top = triggerRect.top + scrollTop - tooltipRect.height - 8;
        left = triggerRect.left + scrollLeft + (triggerRect.width / 2) - (tooltipRect.width / 2);
        break;
      case 'bottom':
        top = triggerRect.bottom + scrollTop + 8;
        left = triggerRect.left + scrollLeft + (triggerRect.width / 2) - (tooltipRect.width / 2);
        break;
      case 'left':
        top = triggerRect.top + scrollTop + (triggerRect.height / 2) - (tooltipRect.height / 2);
        left = triggerRect.left + scrollLeft - tooltipRect.width - 8;
        break;
      case 'right':
        top = triggerRect.top + scrollTop + (triggerRect.height / 2) - (tooltipRect.height / 2);
        left = triggerRect.right + scrollLeft + 8;
        break;
    }

    // Ajustar para não sair da tela
    const padding = 8;
    const maxLeft = window.innerWidth - tooltipRect.width - padding;
    const maxTop = window.innerHeight - tooltipRect.height - padding + scrollTop;

    left = Math.max(padding, Math.min(left, maxLeft));
    top = Math.max(padding + scrollTop, Math.min(top, maxTop));

    setTooltipPosition({ top, left });
  };

  useEffect(() => {
    if (isVisible) {
      updatePosition();
      window.addEventListener('scroll', updatePosition);
      window.addEventListener('resize', updatePosition);
      
      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isVisible, position]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleTriggerEvents = () => {
    const events: any = {};

    if (trigger === 'hover') {
      events.onMouseEnter = showTooltip;
      events.onMouseLeave = hideTooltip;
    } else if (trigger === 'click') {
      events.onClick = () => {
        if (isVisible) {
          hideTooltip();
        } else {
          showTooltip();
        }
      };
    } else if (trigger === 'focus') {
      events.onFocus = showTooltip;
      events.onBlur = hideTooltip;
    }

    return events;
  };

  const getArrowClasses = () => {
    const baseClasses = 'absolute w-2 h-2 bg-[var(--surface)] border border-[var(--border-color)] transform rotate-45';
    
    switch (position) {
      case 'top':
        return `${baseClasses} bottom-[-5px] left-1/2 -translate-x-1/2 border-t-0 border-l-0`;
      case 'bottom':
        return `${baseClasses} top-[-5px] left-1/2 -translate-x-1/2 border-b-0 border-r-0`;
      case 'left':
        return `${baseClasses} right-[-5px] top-1/2 -translate-y-1/2 border-l-0 border-b-0`;
      case 'right':
        return `${baseClasses} left-[-5px] top-1/2 -translate-y-1/2 border-r-0 border-t-0`;
      default:
        return baseClasses;
    }
  };

  return (
    <>
      <div
        ref={triggerRef}
        className={`inline-block ${className}`}
        {...handleTriggerEvents()}
      >
        {children}
      </div>

      {isVisible && (
        <>
          {/* Overlay para fechar no click (apenas para trigger click) */}
          {trigger === 'click' && (
            <div
              className="fixed inset-0 z-40"
              onClick={hideTooltip}
            />
          )}

          {/* Tooltip */}
          <div
            ref={tooltipRef}
            className="fixed z-50 bg-[var(--surface)] border border-[var(--border-color)] rounded-lg shadow-lg p-3 text-sm"
            style={{
              top: tooltipPosition.top,
              left: tooltipPosition.left,
              maxWidth,
            }}
            onMouseEnter={trigger === 'hover' ? showTooltip : undefined}
            onMouseLeave={trigger === 'hover' ? hideTooltip : undefined}
          >
            {/* Conteúdo */}
            <div className="text-[var(--text-primary)]">
              {content}
            </div>

            {/* Seta */}
            <div className={getArrowClasses()} />
          </div>
        </>
      )}
    </>
  );
}

// Componente específico para ajuda contextual
interface HelpTooltipProps {
  content: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md';
}

export function HelpTooltip({ content, className = '', size = 'sm' }: HelpTooltipProps) {
  const iconSize = size === 'sm' ? 'w-4 h-4' : 'w-5 h-5';

  return (
    <ContextualTooltip
      content={content}
      position="top"
      trigger="hover"
      maxWidth="250px"
      className={className}
    >
      <button
        type="button"
        className={`${iconSize} text-[var(--text-secondary)] hover:text-[var(--primary)] transition-colors duration-200 focus:outline-none focus:text-[var(--primary)]`}
        aria-label="Ajuda"
      >
        <svg
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          className="w-full h-full"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </button>
    </ContextualTooltip>
  );
}

// Componente para dicas de funcionalidades
interface FeatureTipProps {
  title: string;
  description: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  showOnce?: boolean;
  tipId?: string;
}

export function FeatureTip({
  title,
  description,
  children,
  position = 'top',
  showOnce = false,
  tipId,
}: FeatureTipProps) {
  const [hasBeenShown, setHasBeenShown] = useState(false);

  useEffect(() => {
    if (showOnce && tipId) {
      const shown = localStorage.getItem(`feature-tip-${tipId}`);
      setHasBeenShown(!!shown);
    }
  }, [showOnce, tipId]);

  const handleTooltipShow = () => {
    if (showOnce && tipId && !hasBeenShown) {
      localStorage.setItem(`feature-tip-${tipId}`, 'true');
      setHasBeenShown(true);
    }
  };

  const content = (
    <div className="max-w-xs">
      <div className="font-medium text-[var(--text-primary)] mb-1">
        {title}
      </div>
      <div className="text-[var(--text-secondary)] text-xs leading-relaxed">
        {description}
      </div>
    </div>
  );

  if (showOnce && hasBeenShown) {
    return <>{children}</>;
  }

  return (
    <ContextualTooltip
      content={content}
      position={position}
      trigger="hover"
      delay={500}
      maxWidth="300px"
    >
      <div onMouseEnter={handleTooltipShow}>
        {children}
      </div>
    </ContextualTooltip>
  );
}

// Componente para status e informações
interface StatusTooltipProps {
  status: 'success' | 'warning' | 'error' | 'info';
  title: string;
  description?: string;
  children: React.ReactNode;
}

export function StatusTooltip({ status, title, description, children }: StatusTooltipProps) {
  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'var(--success)';
      case 'warning':
        return 'var(--warning)';
      case 'error':
        return 'var(--error)';
      case 'info':
        return 'var(--primary)';
      default:
        return 'var(--text-secondary)';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'success':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  const content = (
    <div className="max-w-xs">
      <div className="flex items-center space-x-2 mb-1">
        <span style={{ color: getStatusColor() }}>
          {getStatusIcon()}
        </span>
        <span className="font-medium text-[var(--text-primary)]">
          {title}
        </span>
      </div>
      {description && (
        <div className="text-[var(--text-secondary)] text-xs leading-relaxed">
          {description}
        </div>
      )}
    </div>
  );

  return (
    <ContextualTooltip
      content={content}
      position="top"
      trigger="hover"
      maxWidth="300px"
    >
      {children}
    </ContextualTooltip>
  );
}
