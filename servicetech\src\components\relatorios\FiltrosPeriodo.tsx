/**
 * Componente de Filtros de Período para Relatórios
 * Implementação da Tarefa #20 - Desenvolver <PERSON><PERSON><PERSON><PERSON> de Relatórios
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FiltrosRelatorio, PeriodoRelatorio } from '@/types/relatorios';
import { format, startOfDay, endOfDay } from 'date-fns';

interface FiltrosPeriodoProps {
  filtros: FiltrosRelatorio;
  onFiltrosChange: (filtros: Partial<FiltrosRelatorio>) => void;
  onAplicarFiltros: () => void;
  onLimparFiltros: () => void;
  loading?: boolean;
  isPlanoPremium?: boolean;
}

export function FiltrosPeriodo({
  filtros,
  onFiltrosChange,
  onAplicarFiltros,
  onLimparFiltros,
  loading = false,
  isPlanoPremium = false
}: FiltrosPeriodoProps) {
  const [mostrarFiltrosAvancados, setMostrarFiltrosAvancados] = useState(false);

  const periodos: Array<{ value: PeriodoRelatorio; label: string }> = [
    { value: 'dia', label: 'Hoje' },
    { value: 'semana', label: 'Esta Semana' },
    { value: 'mes', label: 'Este Mês' },
    { value: 'trimestre', label: 'Este Trimestre' },
    { value: 'ano', label: 'Este Ano' },
    { value: 'personalizado', label: 'Período Personalizado' }
  ];

  const statusAgendamento = [
    { value: '', label: 'Todos os Status' },
    { value: 'Pendente', label: 'Pendentes' },
    { value: 'Confirmado', label: 'Confirmados' },
    { value: 'Cancelado', label: 'Cancelados' },
    { value: 'Concluido', label: 'Concluídos' }
  ];

  const formasPagamento = [
    { value: '', label: 'Todas as Formas' },
    { value: 'Online', label: 'Online' },
    { value: 'Local', label: 'Local' }
  ];

  const handlePeriodoChange = (periodo: PeriodoRelatorio) => {
    const novosFiltros: Partial<FiltrosRelatorio> = { periodo };
    
    // Limpar datas personalizadas se não for período personalizado
    if (periodo !== 'personalizado') {
      novosFiltros.data_inicio = undefined;
      novosFiltros.data_fim = undefined;
    }
    
    onFiltrosChange(novosFiltros);
  };

  const handleDataChange = (campo: 'data_inicio' | 'data_fim', valor: string) => {
    onFiltrosChange({ [campo]: valor });
  };

  const formatarDataParaInput = (data?: string) => {
    if (!data) return '';
    return format(new Date(data), 'yyyy-MM-dd');
  };

  const temFiltrosAtivos = () => {
    return filtros.colaborador_id || 
           filtros.servico_id || 
           filtros.forma_pagamento || 
           filtros.status_agendamento ||
           (filtros.periodo === 'personalizado' && filtros.data_inicio && filtros.data_fim);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center">
            <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
            </svg>
            Filtros
          </CardTitle>
          {isPlanoPremium && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setMostrarFiltrosAvancados(!mostrarFiltrosAvancados)}
            >
              {mostrarFiltrosAvancados ? 'Ocultar' : 'Mostrar'} Filtros Avançados
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Seletor de Período */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Período
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {periodos.map((periodo) => (
              <button
                key={periodo.value}
                onClick={() => handlePeriodoChange(periodo.value)}
                className={`px-3 py-2 text-sm rounded-md border transition-colors ${
                  filtros.periodo === periodo.value
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {periodo.label}
              </button>
            ))}
          </div>
        </div>

        {/* Datas Personalizadas */}
        {filtros.periodo === 'personalizado' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Início
              </label>
              <Input
                type="date"
                value={formatarDataParaInput(filtros.data_inicio)}
                onChange={(e) => handleDataChange('data_inicio', e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Fim
              </label>
              <Input
                type="date"
                value={formatarDataParaInput(filtros.data_fim)}
                onChange={(e) => handleDataChange('data_fim', e.target.value)}
                className="w-full"
              />
            </div>
          </div>
        )}

        {/* Filtros Básicos */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Status do Agendamento */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filtros.status_agendamento || ''}
              onChange={(e) => onFiltrosChange({ status_agendamento: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {statusAgendamento.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>

          {/* Forma de Pagamento */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Forma de Pagamento
            </label>
            <select
              value={filtros.forma_pagamento || ''}
              onChange={(e) => onFiltrosChange({ forma_pagamento: e.target.value as any })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {formasPagamento.map((forma) => (
                <option key={forma.value} value={forma.value}>
                  {forma.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Filtros Avançados (Premium) */}
        {isPlanoPremium && mostrarFiltrosAvancados && (
          <div className="border-t pt-4 space-y-4">
            <h4 className="font-medium text-gray-900">Filtros Avançados</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Colaborador */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Colaborador
                </label>
                <Input
                  type="text"
                  placeholder="ID do colaborador"
                  value={filtros.colaborador_id || ''}
                  onChange={(e) => onFiltrosChange({ colaborador_id: e.target.value })}
                  className="w-full"
                />
              </div>

              {/* Serviço */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Serviço
                </label>
                <Input
                  type="number"
                  placeholder="ID do serviço"
                  value={filtros.servico_id || ''}
                  onChange={(e) => onFiltrosChange({ servico_id: e.target.value ? parseInt(e.target.value) : undefined })}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        )}

        {/* Botões de Ação */}
        <div className="flex flex-col sm:flex-row gap-2 pt-4 border-t">
          <Button
            onClick={onAplicarFiltros}
            disabled={loading}
            className="flex-1"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Aplicando...
              </>
            ) : (
              'Aplicar Filtros'
            )}
          </Button>
          
          {temFiltrosAtivos() && (
            <Button
              variant="outline"
              onClick={onLimparFiltros}
              disabled={loading}
              className="flex-1 sm:flex-none"
            >
              Limpar Filtros
            </Button>
          )}
        </div>

        {/* Indicador de Filtros Ativos */}
        {temFiltrosAtivos() && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-center">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm text-blue-800">
                Filtros ativos aplicados aos relatórios
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
