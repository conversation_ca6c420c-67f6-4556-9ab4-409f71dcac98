'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Breadcrumbs } from '@/components/ui/Breadcrumbs';
import { FormularioAgendamento } from '@/components/agendamento/FormularioAgendamento';
import { createClient } from '@/utils/supabase/client';

interface DadosEmpresa {
  empresa_id: number;
  nome_empresa: string;
  endereco: string;
  numero: string;
  bairro: string;
  cidade: string;
  estado: string;
  servicos: Array<{
    servico_id: number;
    nome_servico: string;
    descricao: string;
    duracao_minutos: number;
    preco: number;
    categoria: string;
  }>;
  servicos_por_categoria: Record<string, any[]>;
  colaboradores: Array<{
    colaborador_user_id: string;
    name: string;
    ativo_como_prestador: boolean;
  }>;
}

export default function AgendamentoPage() {
  const params = useParams();
  const router = useRouter();
  const [dadosEmpresa, setDadosEmpresa] = useState<DadosEmpresa | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [usuarioAutenticado, setUsuarioAutenticado] = useState<boolean | null>(null);

  useEffect(() => {
    const verificarAutenticacao = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      setUsuarioAutenticado(!!user);
      
      if (!user) {
        // Redirecionar para login com redirect de volta
        const redirectUrl = encodeURIComponent(window.location.pathname);
        router.push(`/auth/login?redirect=${redirectUrl}`);
        return;
      }
    };

    verificarAutenticacao();
  }, [router]);

  useEffect(() => {
    const buscarDadosEmpresa = async () => {
      if (usuarioAutenticado === false) return; // Não buscar se não autenticado

      try {
        setLoading(true);
        setError(null);

        const empresaId = params.empresaId as string;

        if (!empresaId || isNaN(Number(empresaId))) {
          throw new Error('ID da empresa inválido');
        }

        const response = await fetch(`/api/empresas/${empresaId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error ?? 'Erro ao buscar dados da empresa');
        }

        setDadosEmpresa(result.data);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (usuarioAutenticado && params.empresaId) {
      buscarDadosEmpresa();
    }
  }, [params.empresaId, usuarioAutenticado]);

  // Aguardar verificação de autenticação
  if (usuarioAutenticado === null) {
    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="text-[var(--text-secondary)] mt-4">Verificando autenticação...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  // Se não autenticado, não renderizar nada (redirecionamento já foi feito)
  if (usuarioAutenticado === false) {
    return null;
  }

  if (loading) {
    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
              <p className="text-[var(--text-secondary)] mt-4">Carregando dados do estabelecimento...</p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !dadosEmpresa) {
    return (
      <Layout>
        <div className="bg-[var(--background)] py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <p className="text-[var(--error)] text-lg mb-4">
                {error ?? 'Estabelecimento não encontrado'}
              </p>
              <div className="space-x-4">
                <Button 
                  variant="outline" 
                  onClick={() => router.push('/buscar')}
                >
                  Buscar Estabelecimentos
                </Button>
                <Button 
                  onClick={() => window.location.reload()}
                >
                  Tentar Novamente
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="bg-[var(--background)] py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <div className="mb-8">
            <Breadcrumbs
              items={[
                { label: 'Início', href: '/' },
                { label: 'Buscar', href: '/buscar' },
                { 
                  label: dadosEmpresa.nome_empresa, 
                  href: `/estabelecimento/${dadosEmpresa.empresa_id}` 
                },
                { label: 'Agendamento' }
              ]}
            />
          </div>

          {/* Título da página */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-[var(--text-primary)] mb-2">
              Fazer Agendamento
            </h1>
            <p className="text-[var(--text-secondary)] text-lg">
              Agende seu horário em {dadosEmpresa.nome_empresa}
            </p>
          </div>

          {/* Formulário de agendamento */}
          <FormularioAgendamento dadosEmpresa={dadosEmpresa} />

          {/* Informações importantes */}
          <Card className="mt-8 bg-[var(--info-light)] border-[var(--info)]">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-[var(--info)] rounded-full flex items-center justify-center mt-1">
                  <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-[var(--text-primary)] mb-2">
                    Informações Importantes
                  </h3>
                  <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                    <li>• Seu agendamento ficará pendente de confirmação por até 24 horas</li>
                    <li>• Você receberá uma notificação quando o agendamento for confirmado</li>
                    <li>• É possível cancelar ou reagendar até 2 horas antes do horário marcado</li>
                    <li>• Em caso de dúvidas, entre em contato diretamente com o estabelecimento</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Botão de voltar */}
          <div className="text-center mt-8">
            <Button
              variant="outline"
              onClick={() => router.push(`/estabelecimento/${dadosEmpresa.empresa_id}`)}
            >
              ← Voltar para o Estabelecimento
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
}
