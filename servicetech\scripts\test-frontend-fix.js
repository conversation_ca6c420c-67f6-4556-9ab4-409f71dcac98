/**
 * Script para testar se a correção do frontend está funcionando
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

// Cliente administrativo do Supabase
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testarFrontend() {
  try {
    console.log('🧪 TESTE DA CORREÇÃO DO FRONTEND');
    console.log('=' .repeat(60));
    console.log('');

    const empresaId = 6;
    const slug = 'barbearia-santos-3';

    // 1. Testar API diretamente
    console.log('1️⃣ Testando API diretamente...');
    
    try {
      const response = await fetch(`http://localhost:3000/api/empresas/${slug}`);
      const result = await response.json();
      
      if (response.ok && result.success) {
        console.log('✅ API funcionando corretamente');
        console.log(`   Empresa: ${result.data.empresa.nome_empresa}`);
        console.log(`   Serviços: ${result.data.estatisticas.total_servicos}`);
        console.log(`   Estrutura de dados: ${Object.keys(result.data).join(', ')}`);
        
        // Verificar se a estrutura está correta
        const { empresa, servicos, servicos_por_categoria, colaboradores, estatisticas } = result.data;
        
        console.log('');
        console.log('📊 Verificando estrutura de dados:');
        console.log(`   ✅ empresa: ${empresa ? 'OK' : 'FALTANDO'}`);
        console.log(`   ✅ servicos: ${servicos ? 'OK' : 'FALTANDO'} (${servicos?.length || 0} itens)`);
        console.log(`   ✅ servicos_por_categoria: ${servicos_por_categoria ? 'OK' : 'FALTANDO'} (${Object.keys(servicos_por_categoria || {}).length} categorias)`);
        console.log(`   ✅ colaboradores: ${colaboradores ? 'OK' : 'FALTANDO'} (${colaboradores?.length || 0} itens)`);
        console.log(`   ✅ estatisticas: ${estatisticas ? 'OK' : 'FALTANDO'}`);
        
        // Verificar campos específicos da empresa
        console.log('');
        console.log('🏢 Verificando campos da empresa:');
        console.log(`   ✅ empresa_id: ${empresa.empresa_id}`);
        console.log(`   ✅ nome_empresa: ${empresa.nome_empresa}`);
        console.log(`   ✅ slug: ${empresa.slug}`);
        console.log(`   ✅ endereco: ${empresa.endereco}`);
        console.log(`   ✅ telefone: ${empresa.telefone || 'N/A'}`);
        console.log(`   ✅ horario_funcionamento: ${empresa.horario_funcionamento ? 'OK' : 'N/A'}`);
        
      } else {
        console.log('❌ API retornou erro:', result.error);
        return false;
      }
    } catch (error) {
      console.log('❌ Erro ao testar API:', error.message);
      return false;
    }

    console.log('');
    console.log('2️⃣ Simulando lógica do frontend...');
    
    // Simular o que o frontend faz
    try {
      const response = await fetch(`http://localhost:3000/api/empresas/${slug}`);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error ?? 'Erro ao buscar dados da empresa');
      }

      const dadosEmpresa = result.data;
      
      // Verificar se dadosEmpresa existe e tem a estrutura correta
      if (!dadosEmpresa) {
        console.log('❌ dadosEmpresa é null/undefined');
        return false;
      }

      if (!dadosEmpresa.empresa) {
        console.log('❌ dadosEmpresa.empresa é null/undefined');
        return false;
      }

      console.log('✅ Lógica do frontend funcionando');
      console.log(`   Empresa carregada: ${dadosEmpresa.empresa.nome_empresa}`);
      console.log(`   Dados completos: ${JSON.stringify(dadosEmpresa, null, 2).length} caracteres`);
      
    } catch (error) {
      console.log('❌ Erro na lógica do frontend:', error.message);
      return false;
    }

    console.log('');
    console.log('3️⃣ Testando com ID numérico...');
    
    try {
      const response = await fetch(`http://localhost:3000/api/empresas/${empresaId}`);
      const result = await response.json();
      
      if (response.ok && result.success) {
        console.log('✅ API com ID numérico funcionando');
        console.log(`   Empresa: ${result.data.empresa.nome_empresa}`);
      } else {
        console.log('❌ API com ID numérico falhou:', result.error);
        return false;
      }
    } catch (error) {
      console.log('❌ Erro ao testar API com ID:', error.message);
      return false;
    }

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 TODOS OS TESTES PASSARAM!');
    console.log('=' .repeat(60));
    console.log('');
    console.log('📝 PRÓXIMOS PASSOS:');
    console.log('1. Abra o navegador e acesse as URLs:');
    console.log(`   🔗 http://localhost:3000/estabelecimento/${slug}`);
    console.log(`   🔗 http://localhost:3000/estabelecimento/${empresaId}`);
    console.log('');
    console.log('2. Verifique se a página carrega corretamente');
    console.log('3. Confirme se os dados da empresa aparecem');
    console.log('4. Teste os botões de agendamento');
    console.log('');
    console.log('🔧 Se ainda houver problemas:');
    console.log('- Abra o console do navegador (F12)');
    console.log('- Verifique se há erros JavaScript');
    console.log('- Confirme se a API está sendo chamada corretamente');

    return true;

  } catch (error) {
    console.error('❌ Erro geral no teste:', error);
    return false;
  }
}

// Executar teste
testarFrontend();
