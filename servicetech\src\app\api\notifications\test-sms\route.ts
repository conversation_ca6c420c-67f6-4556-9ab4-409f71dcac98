import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { SMSService } from '@/services/SMSService';

/**
 * POST - Enviar SMS de teste
 * Apenas para administradores
 */
export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    // Verificar se é administrador
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json({
        success: false,
        error: 'Acesso negado. Apenas administradores podem testar SMS.'
      }, { status: 403 });
    }

    const body = await request.json();
    const { message, to } = body;

    // Validações
    if (!message || typeof message !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Mensagem é obrigatória'
      }, { status: 400 });
    }

    if (!to || typeof to !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Número de destino é obrigatório'
      }, { status: 400 });
    }

    // Verificar se o serviço SMS está disponível
    let smsService: SMSService;
    try {
      smsService = new SMSService();
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Serviço de SMS não está configurado',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      }, { status: 503 });
    }

    // Validar número
    if (!smsService.validarNumero(to)) {
      return NextResponse.json({
        success: false,
        error: 'Número de telefone inválido. Use formato brasileiro: +5511999999999'
      }, { status: 400 });
    }

    // Enviar SMS
    console.log(`📱 Enviando SMS de teste para: ${to}`);
    
    const resultado = await smsService.enviarSMS({
      to,
      message: `[TESTE] ${message}`
    });

    if (resultado.success) {
      // Registrar teste no log de auditoria
      await supabase
        .from('audit_logs')
        .insert({
          user_id: user.id,
          action: 'test_sms',
          details: {
            to,
            message_id: resultado.messageId,
            message_preview: message.substring(0, 50)
          }
        });

      return NextResponse.json({
        success: true,
        message: 'SMS de teste enviado com sucesso',
        messageId: resultado.messageId,
        to: to
      });
    } else {
      return NextResponse.json({
        success: false,
        error: resultado.error || 'Erro ao enviar SMS'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Erro na API de teste SMS:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}

/**
 * GET - Verificar status do serviço SMS
 */
export async function GET() {
  try {
    const supabase = await createClient();
    
    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não autenticado'
      }, { status: 401 });
    }

    // Verificar se é administrador
    const userRole = user.user_metadata?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json({
        success: false,
        error: 'Acesso negado'
      }, { status: 403 });
    }

    // Verificar configuração do Twilio
    const twilioConfigured = !!(
      process.env.TWILIO_ACCOUNT_SID &&
      process.env.TWILIO_AUTH_TOKEN &&
      process.env.TWILIO_PHONE_NUMBER
    );

    let serviceStatus = 'not_configured';
    let serviceError = null;

    if (twilioConfigured) {
      try {
        const smsService = new SMSService();
        serviceStatus = 'configured';
      } catch (error) {
        serviceStatus = 'error';
        serviceError = error instanceof Error ? error.message : 'Erro desconhecido';
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        twilio_configured: twilioConfigured,
        service_status: serviceStatus,
        service_error: serviceError,
        environment_variables: {
          TWILIO_ACCOUNT_SID: !!process.env.TWILIO_ACCOUNT_SID,
          TWILIO_AUTH_TOKEN: !!process.env.TWILIO_AUTH_TOKEN,
          TWILIO_PHONE_NUMBER: !!process.env.TWILIO_PHONE_NUMBER
        }
      }
    });

  } catch (error) {
    console.error('❌ Erro ao verificar status SMS:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 });
  }
}
