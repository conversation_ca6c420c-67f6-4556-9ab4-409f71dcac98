import { NextResponse } from 'next/server';
import Stripe from 'stripe';

// Inicializar o cliente Stripe com a chave secreta
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil', // Usar a versão mais recente da API
});

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { plano, dadosEmpresa, user_id } = body;

    // Verificar se os dados necessários foram fornecidos
    if (!plano) {
      return NextResponse.json(
        { error: 'Plano é obrigatório para criar o pagamento' },
        { status: 400 }
      );
    }

    // Determinar o valor com base no plano selecionado
    const amount = plano === 'essencial' ? 9900 : 19900; // Em centavos (R$ 99,00 ou R$ 199,00)

    // Criar um cliente no Stripe (apenas se temos dados da empresa)
    let customer;
    if (dadosEmpresa) {
      customer = await stripe.customers.create({
        name: dadosEmpresa.nomeEstabelecimento,
        email: dadosEmpresa.email ?? '<EMAIL>',
        metadata: {
          cnpj: dadosEmpresa.cnpj,
          telefone: dadosEmpresa.telefone,
          endereco: `${dadosEmpresa.endereco}, ${dadosEmpresa.numero}, ${dadosEmpresa.bairro}, ${dadosEmpresa.cidade} - ${dadosEmpresa.estado}`,
        },
      });
    }

    // Criar um Payment Intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'brl',
      customer: customer?.id,
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        plano,
        estabelecimento: dadosEmpresa?.nomeEstabelecimento ?? 'Estabelecimento',
        cnpj: dadosEmpresa?.cnpj ?? '',
        user_id: user_id ?? '',
      },
    });

    // Retornar o client_secret para o frontend
    return NextResponse.json({ clientSecret: paymentIntent.client_secret });
  } catch (error) {
    console.error('Erro ao criar payment intent:', error);
    return NextResponse.json(
      { error: 'Erro ao processar o pagamento' },
      { status: 500 }
    );
  }
}