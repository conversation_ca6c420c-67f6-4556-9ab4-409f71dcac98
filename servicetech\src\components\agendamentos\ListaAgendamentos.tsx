'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { CardAgendamento } from './CardAgendamento';
import { AgendamentoCompleto, StatusAgendamento } from '@/types/agendamentos';

interface ListaAgendamentosProps {
  agendamentos: AgendamentoCompleto[];
  onConfirmar?: (id: number) => void;
  onRecusar?: (id: number) => void;
  onCancelar?: (id: number) => void;
  onConcluir?: (id: number) => void;
  onMarcarPago?: (id: number) => void;
  onVerDetalhes?: (agendamento: AgendamentoCompleto) => void;
  loading?: boolean;
  userRole?: string;
  titulo?: string;
  mostrarFiltros?: boolean;
}

export function ListaAgendamentos({
  agendamentos,
  onConfirmar,
  onRecusar,
  onCancelar,
  onConcluir,
  onMarcarPago,
  onVerDetal<PERSON>,
  loading = false,
  userRole,
  titulo = 'Agendamentos',
  mostrarFiltros = true
}: ListaAgendamentosProps) {
  
  const [filtroStatus, setFiltroStatus] = useState<StatusAgendamento | 'todos'>('todos');
  const [ordenacao, setOrdenacao] = useState<'data_asc' | 'data_desc' | 'status'>('data_asc');

  // Filtrar agendamentos
  const agendamentosFiltrados = agendamentos.filter(agendamento => {
    if (filtroStatus === 'todos') return true;
    return agendamento.status_agendamento === filtroStatus;
  });

  // Ordenar agendamentos
  const agendamentosOrdenados = [...agendamentosFiltrados].sort((a, b) => {
    switch (ordenacao) {
      case 'data_asc':
        return new Date(a.data_hora_inicio).getTime() - new Date(b.data_hora_inicio).getTime();
      case 'data_desc':
        return new Date(b.data_hora_inicio).getTime() - new Date(a.data_hora_inicio).getTime();
      case 'status':
        const statusOrder = { 'Pendente': 1, 'Confirmado': 2, 'Concluido': 3, 'Cancelado': 4, 'Recusado': 5 };
        return (statusOrder[a.status_agendamento] || 6) - (statusOrder[b.status_agendamento] || 6);
      default:
        return 0;
    }
  });

  // Contar por status
  const contadores = agendamentos.reduce((acc, agendamento) => {
    acc[agendamento.status_agendamento] = (acc[agendamento.status_agendamento] || 0) + 1;
    return acc;
  }, {} as Record<StatusAgendamento, number>);

  const statusOptions: { value: StatusAgendamento | 'todos'; label: string; count?: number }[] = [
    { value: 'todos', label: 'Todos', count: agendamentos.length },
    { value: 'Pendente', label: 'Pendentes', count: contadores.Pendente || 0 },
    { value: 'Confirmado', label: 'Confirmados', count: contadores.Confirmado || 0 },
    { value: 'Concluido', label: 'Concluídos', count: contadores.Concluido || 0 },
    { value: 'Cancelado', label: 'Cancelados', count: contadores.Cancelado || 0 },
    { value: 'Recusado', label: 'Recusados', count: contadores.Recusado || 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Header com filtros */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="text-xl font-bold text-[var(--text-primary)]">
              {titulo}
              <span className="ml-2 text-sm font-normal text-[var(--text-secondary)]">
                ({agendamentosOrdenados.length} {agendamentosOrdenados.length === 1 ? 'agendamento' : 'agendamentos'})
              </span>
            </CardTitle>
            
            {mostrarFiltros && (
              <div className="flex flex-col sm:flex-row gap-3">
                {/* Filtro por Status */}
                <div className="flex flex-wrap gap-1">
                  {statusOptions.map(option => (
                    <Button
                      key={option.value}
                      size="sm"
                      variant={filtroStatus === option.value ? 'primary' : 'outline'}
                      onClick={() => setFiltroStatus(option.value)}
                      className="text-xs"
                    >
                      {option.label}
                      {option.count !== undefined && (
                        <span className={`ml-1 px-1.5 py-0.5 rounded-full text-xs ${
                          filtroStatus === option.value 
                            ? 'bg-white/20 text-white' 
                            : 'bg-gray-100 text-gray-600'
                        }`}>
                          {option.count}
                        </span>
                      )}
                    </Button>
                  ))}
                </div>
                
                {/* Ordenação */}
                <select
                  value={ordenacao}
                  onChange={(e) => setOrdenacao(e.target.value as any)}
                  className="px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
                >
                  <option value="data_asc">Data (mais antigo)</option>
                  <option value="data_desc">Data (mais recente)</option>
                  <option value="status">Status</option>
                </select>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Lista de agendamentos */}
      {loading ? (
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="flex gap-2">
                    <div className="h-8 bg-gray-200 rounded w-20"></div>
                    <div className="h-8 bg-gray-200 rounded w-20"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : agendamentosOrdenados.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-6xl mb-4">📅</div>
            <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
              {filtroStatus === 'todos' 
                ? 'Nenhum agendamento encontrado'
                : `Nenhum agendamento ${statusOptions.find(s => s.value === filtroStatus)?.label.toLowerCase()}`
              }
            </h3>
            <p className="text-[var(--text-secondary)] mb-4">
              {filtroStatus === 'todos'
                ? 'Quando houver agendamentos, eles aparecerão aqui.'
                : 'Tente alterar os filtros para ver outros agendamentos.'
              }
            </p>
            {filtroStatus !== 'todos' && (
              <Button
                variant="outline"
                onClick={() => setFiltroStatus('todos')}
              >
                Ver todos os agendamentos
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {agendamentosOrdenados.map(agendamento => (
            <CardAgendamento
              key={agendamento.agendamento_id}
              agendamento={agendamento}
              onConfirmar={onConfirmar}
              onRecusar={onRecusar}
              onCancelar={onCancelar}
              onConcluir={onConcluir}
              onMarcarPago={onMarcarPago}
              onVerDetalhes={onVerDetalhes}
              loading={loading}
              userRole={userRole}
            />
          ))}
        </div>
      )}

      {/* Resumo no final */}
      {agendamentosOrdenados.length > 0 && (
        <Card className="bg-gray-50">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4 text-sm text-[var(--text-secondary)]">
              <span>
                <strong>Total:</strong> {agendamentos.length} agendamentos
              </span>
              {contadores.Pendente > 0 && (
                <span>
                  <strong>Pendentes:</strong> {contadores.Pendente}
                </span>
              )}
              {contadores.Confirmado > 0 && (
                <span>
                  <strong>Confirmados:</strong> {contadores.Confirmado}
                </span>
              )}
              {contadores.Concluido > 0 && (
                <span>
                  <strong>Concluídos:</strong> {contadores.Concluido}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
