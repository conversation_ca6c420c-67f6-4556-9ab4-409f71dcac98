# Schema do Banco de Dados - ServiceTech

Banco PostgreSQL no Supabase com arquitetura multi-tenant e Row Level Security (RLS).

## 🏗️ Estrutura Geral

- **24 tabelas** com **46 políticas RLS**
- **Isolamento por empresa** garantindo privacidade
- **Triggers automáticos** para timestamps e validações
- **Índices estratégicos** para performance

## 📊 Tabelas Principais

### Core System

#### `papeis`
Gerencia papéis de usuário (Administrador, Proprietario, Colaborador, Cliente).

#### `planos_saas`
Planos da plataforma:
- **Essencial**: R$ 99/mês, 6 serviços, 2 colaboradores
- **Premium**: R$ 199/mês, 12 serviços, 6 colaboradores

#### `empresas`
Estabelecimentos cadastrados com:
- Dados básicos (nome, CNPJ, endereço)
- `logo_url`, `imagem_capa_url` - Imagens da empresa
- `horario_funcionamento` (JSONB) - Hor<PERSON><PERSON>s por dia
- `stripe_account_id` - Conta Stripe Connect
- `politica_cancelamento` (JSONB) - Políticas configuráveis
- `slug` - URL amigável gerada automaticamente

### Business Logic

#### `servicos`
Serviços oferecidos:
- Nome, descrição, duração, preço, categoria
- Limites por plano (6-12 serviços)
- Status ativo/inativo

#### `colaboradores_empresa`
Associação usuário-empresa:
- Sistema de convites com tokens
- `horarios_trabalho_individual` (JSONB)
- Configurações financeiras (comissão, custos)

#### `agendamentos`
Sistema de agendamentos:
- Cliente, empresa, colaborador, serviço
- Status: Pendente, Confirmado, Recusado, Cancelado, Concluido
- Forma de pagamento: Online, Local
- `codigo_confirmacao` - Gerado automaticamente
- `stripe_payment_intent_id` - Integração Stripe

#### `pagamentos`
Transações financeiras:
- Agendamentos, assinaturas SaaS, assinaturas cliente
- Status: Pendente, Pago, Falhou, Reembolsado
- Integração completa com Stripe

### Premium Features

#### `planos_servico_cliente`
Assinaturas mensais de serviços (Premium):
- Planos criados por proprietários
- Limites de uso mensal
- Integração Stripe para cobrança recorrente
- Status: ativa, pausada, cancelada, expirada

#### `combos_servicos` + `combo_itens`
Sistema de combos promocionais:
- Desconto por valor fixo ou percentual
- Período de validade e limite de usos
- Detecção automática durante agendamento

#### `cupons`
Sistema de cupons de desconto (Premium):
- Códigos únicos com validação
- Limites por cliente e total
- Aplicável a serviços específicos

#### `campanhas_marketing` + `campanhas_destinatarios`
Campanhas de email/SMS (Premium):
- Segmentação avançada de clientes
- Tracking de abertura e cliques
- Log detalhado de envios

### Notifications & Communication

#### `notificacoes`
Sistema multicanal (email, SMS, push):
- 7 tipos de notificação
- Contexto JSONB para dados específicos
- Controle de tentativas e erros

#### `preferencias_notificacao`
Preferências granulares por usuário:
- Controle por canal (email, SMS, push)
- Tipos de notificação habilitados

#### `device_tokens`
Tokens FCM para push notifications:
- Suporte web, Android, iOS
- Soft delete para histórico

### Testing & Quality

#### `cenarios_teste` + `sessoes_teste` + `execucoes_teste` + `feedback_teste`
Sistema completo de testes de usuário:
- Cenários com passos detalhados
- Sessões com múltiplos participantes
- Métricas quantitativas e qualitativas
- Templates predefinidos

## 🔐 Políticas RLS

### Padrões de Acesso
- **Proprietários**: Acesso total às suas empresas
- **Colaboradores**: Leitura das empresas onde trabalham
- **Clientes**: Dados próprios + empresas/serviços ativos
- **Administradores**: Acesso total (bypass RLS)
- **Público**: Empresas e serviços ativos

### Exemplos de Implementação

```sql
-- Empresas: Proprietários gerenciam suas empresas
CREATE POLICY "proprietarios_empresas" ON empresas
  FOR ALL USING (proprietario_user_id = auth.uid());

-- Serviços: Público vê serviços ativos
CREATE POLICY "publico_servicos_ativos" ON servicos
  FOR SELECT USING (
    ativo = true AND 
    empresa_id IN (SELECT empresa_id FROM empresas WHERE status = 'ativo')
  );

-- Agendamentos: Clientes veem seus agendamentos
CREATE POLICY "clientes_agendamentos" ON agendamentos
  FOR SELECT USING (cliente_user_id = auth.uid());
```

## 🔧 Funcionalidades Automáticas

### Triggers
- `update_updated_at` - Atualiza timestamps automaticamente
- `set_empresa_slug` - Gera slugs únicos
- `set_agendamento_codigo` - Códigos de confirmação
- `reset_cycle_usage` - Reset de usos mensais

### Funções Auxiliares
- `generate_unique_slug()` - Slugs únicos para empresas
- `generate_confirmation_code()` - Códigos de 6 caracteres
- `cliente_pode_usar_servico()` - Validação de assinaturas
- `user_has_company_access()` - Verificação de permissões

## 🛡️ Sistema de Segurança

### Headers de Segurança
- CSP, HSTS, X-Frame-Options configurados
- Rate limiting por endpoint e usuário
- Validação anti-XSS e SQL injection

### Auditoria
- Log de eventos por categoria (AUTH, DATA, ADMIN, PAYMENT)
- Classificação por risco (LOW, MEDIUM, HIGH, CRITICAL)
- Alertas automáticos para atividades suspeitas

### Exemplo de Evento de Auditoria
```json
{
  "action": "LOGIN_SUCCESS",
  "userId": "user_123",
  "riskLevel": "LOW",
  "category": "AUTH",
  "ipAddress": "*************",
  "timestamp": "2025-01-27T10:30:00Z"
}
```

## 📈 Estruturas JSONB

### Políticas de Cancelamento
```json
{
  "cliente": {
    "antecedencia_24h_nao_confirmado": {"percentual_reembolso": 100},
    "antecedencia_24h_confirmado": {"percentual_reembolso": 50},
    "mesmo_dia": {"percentual_reembolso": 0}
  },
  "empresa": {"percentual_reembolso": 100},
  "configuracoes": {
    "prazo_cancelamento_cliente": 2,
    "permitir_cancelamento_cliente": true
  }
}
```

### Horários de Funcionamento
```json
{
  "segunda": {"aberto": true, "inicio": "08:00", "fim": "18:00"},
  "terca": {"aberto": true, "inicio": "08:00", "fim": "18:00"},
  "intervalo": {"inicio": "12:00", "fim": "13:00"}
}
```

### Segmentação de Campanhas
```json
{
  "clientes_ativos": true,
  "clientes_inativos_dias": 30,
  "min_agendamentos": 5,
  "servicos_utilizados": [1, 2, 3],
  "valor_gasto_min": 100.00
}
```

## 🚀 Implementações Recentes

### Janeiro 2025
- **Sistema de Testes de Usuário**: 4 tabelas para UX testing
- **Notificações Multicanal**: Email, SMS e Push completos
- **Stripe Connect**: Contas conectadas para proprietários
- **Marketing Premium**: Cupons e campanhas segmentadas
- **Segurança Avançada**: Rate limiting e auditoria

### Funcionalidades Core
- **Agendamentos**: Sistema completo com Round-Robin
- **Pagamentos**: Stripe + reembolsos automáticos
- **Relatórios**: Básicos (Essencial) e avançados (Premium)
- **Combos**: Promoções automáticas
- **Assinaturas**: Planos mensais para clientes

## 📋 Próximas Implementações

- Sistema de avaliações e comentários
- Integração com calendários externos
- Marketplace de estabelecimentos
- Analytics avançados de performance

---

Para detalhes completos de implementação, consulte:
- `docs/sql/enhanced-rls-policies.sql` - Políticas RLS completas
- `docs/database_migrations/` - Scripts de migração
- `docs/planos-servico-cliente-schema.sql` - Schema de assinaturas
