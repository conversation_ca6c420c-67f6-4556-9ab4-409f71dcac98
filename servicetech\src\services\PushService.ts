import * as admin from 'firebase-admin';
import { EnviarPushData, PushResponse } from '@/types/notifications';

export class PushService {
  private app: admin.app.App;

  constructor() {
    // Inicializar Firebase Admin apenas se não estiver inicializado
    if (!admin.apps.length) {
      const projectId = process.env.FIREBASE_PROJECT_ID;
      const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n');
      const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;

      if (!projectId || !privateKey || !clientEmail) {
        throw new Error('Credenciais do Firebase não configuradas');
      }

      this.app = admin.initializeApp({
        credential: admin.credential.cert({
          projectId,
          privateKey,
          clientEmail
        }),
        projectId
      });
    } else {
      this.app = admin.apps[0] as admin.app.App;
    }
  }

  /**
   * Envia notificação push usando Firebase Cloud Messaging
   */
  async enviarPush(data: EnviarPushData): Promise<PushResponse> {
    try {
      console.log(`🔔 Enviando push notification para token: ${data.token.substring(0, 20)}...`);

      const message: admin.messaging.Message = {
        token: data.token,
        notification: {
          title: data.title,
          body: data.body,
          imageUrl: data.imageUrl
        },
        data: data.data || {},
        webpush: {
          notification: {
            title: data.title,
            body: data.body,
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            image: data.imageUrl,
            requireInteraction: true,
            actions: [
              {
                action: 'view',
                title: 'Ver Detalhes'
              },
              {
                action: 'dismiss',
                title: 'Dispensar'
              }
            ]
          },
          fcmOptions: {
            link: data.data?.url || '/'
          }
        }
      };

      const response = await admin.messaging().send(message);

      console.log(`✅ Push notification enviada com sucesso: ${response}`);

      return {
        success: true,
        messageId: response
      };

    } catch (error) {
      console.error('❌ Erro ao enviar push notification:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro ao enviar push notification'
      };
    }
  }

  /**
   * Envia notificação push para múltiplos tokens
   */
  async enviarPushMultiplo(tokens: string[], data: Omit<EnviarPushData, 'token'>): Promise<PushResponse[]> {
    const promises = tokens.map(token => 
      this.enviarPush({ ...data, token })
    );

    return Promise.all(promises);
  }

  /**
   * Gerar dados de push baseado no tipo de notificação
   */
  gerarDadosPush(tipo: string, contexto: any): Omit<EnviarPushData, 'token'> {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

    switch (tipo) {
      case 'novo_agendamento':
        return {
          title: '📅 Novo Agendamento',
          body: `Agendamento de ${contexto.servico_nome} solicitado para ${this.formatarDataPush(contexto.data_hora_inicio)}`,
          data: {
            type: 'agendamento',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/cliente/agendamentos`
          }
        };

      case 'agendamento_confirmado':
        return {
          title: '✅ Agendamento Confirmado',
          body: `${contexto.servico_nome} confirmado para ${this.formatarDataPush(contexto.data_hora_inicio)}`,
          data: {
            type: 'confirmacao',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/cliente/agendamentos`
          }
        };

      case 'agendamento_recusado':
        return {
          title: '❌ Agendamento Recusado',
          body: `Seu agendamento de ${contexto.servico_nome} foi recusado`,
          data: {
            type: 'recusa',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/buscar`
          }
        };

      case 'agendamento_cancelado':
        return {
          title: '🚫 Agendamento Cancelado',
          body: `Agendamento de ${contexto.servico_nome} foi cancelado`,
          data: {
            type: 'cancelamento',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/cliente/agendamentos`
          }
        };

      case 'lembrete_confirmacao':
        return {
          title: '⏰ Confirmação Pendente',
          body: `Agendamento de ${contexto.cliente_nome} expira em breve`,
          data: {
            type: 'lembrete_confirmacao',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/proprietario/agendamentos`
          }
        };

      case 'lembrete_agendamento':
        return {
          title: '🔔 Lembrete de Agendamento',
          body: `${contexto.servico_nome} amanhã às ${this.formatarHoraPush(contexto.data_hora_inicio)}`,
          data: {
            type: 'lembrete_agendamento',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/cliente/agendamentos`
          }
        };

      case 'pagamento_confirmado':
        return {
          title: '💳 Pagamento Confirmado',
          body: `Pagamento de ${contexto.servico_nome} processado com sucesso`,
          data: {
            type: 'pagamento',
            agendamento_id: contexto.agendamento_id?.toString(),
            url: `${baseUrl}/cliente/agendamentos`
          }
        };

      default:
        return {
          title: '🔔 ServiceTech',
          body: 'Você tem uma nova notificação',
          data: {
            type: 'geral',
            url: baseUrl
          }
        };
    }
  }

  /**
   * Formatar data para push notification
   */
  private formatarDataPush(dataISO: string): string {
    const data = new Date(dataISO);
    return data.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formatar hora para push notification
   */
  private formatarHoraPush(dataISO: string): string {
    const data = new Date(dataISO);
    return data.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Validar token de dispositivo
   */
  validarToken(token: string): boolean {
    // Token FCM tem formato específico e comprimento mínimo
    return !!(token && token.length > 100 && /^[A-Za-z0-9_-]+$/.test(token));
  }
}
