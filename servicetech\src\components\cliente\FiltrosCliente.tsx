'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FiltrosAgendamentos, StatusAgendamento, FormaPagamento } from '@/types/agendamentos';

interface FiltrosClienteEstendidos extends FiltrosAgendamentos {
  periodo?: string;
}

interface FiltrosClienteProps {
  filtros: FiltrosClienteEstendidos;
  onAplicarFiltros: (filtros: FiltrosAgendamentos) => void;
  onLimparFiltros: () => void;
  loading?: boolean;
}

export function FiltrosCliente({
  filtros,
  onAplicarFiltros,
  onLimparFiltros,
  loading = false
}: FiltrosClienteProps) {
  const [filtrosLocais, setFiltrosLocais] = useState<FiltrosClienteEstendidos>(filtros);

  const handleAplicar = () => {
    onAplicarFiltros(filtrosLocais);
  };

  const handleLimpar = () => {
    const filtrosVazios: FiltrosClienteEstendidos = {};
    setFiltrosLocais(filtrosVazios);
    onLimparFiltros();
  };

  const statusOptions: { value: StatusAgendamento | 'todos'; label: string }[] = [
    { value: 'todos', label: 'Todos os Status' },
    { value: 'Pendente', label: 'Pendente' },
    { value: 'Confirmado', label: 'Confirmado' },
    { value: 'Concluido', label: 'Concluído' },
    { value: 'Cancelado', label: 'Cancelado' },
    { value: 'Recusado', label: 'Recusado' }
  ];

  const periodoOptions = [
    { value: 'todos', label: 'Todos os Períodos' },
    { value: 'hoje', label: 'Hoje' },
    { value: 'semana', label: 'Esta Semana' },
    { value: 'mes', label: 'Este Mês' },
    { value: 'personalizado', label: 'Período Personalizado' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Filtros</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
            Status
          </label>
          <select
            value={filtrosLocais.status_agendamento || 'todos'}
            onChange={(e) => setFiltrosLocais(prev => ({
              ...prev,
              status_agendamento: e.target.value === 'todos' ? undefined : e.target.value as StatusAgendamento
            }))}
            className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
            disabled={loading}
          >
            {statusOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Período */}
        <div>
          <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
            Período
          </label>
          <select
            value={filtrosLocais.periodo || 'todos'}
            onChange={(e) => {
              const periodo = e.target.value;
              setFiltrosLocais(prev => ({
                ...prev,
                periodo: periodo === 'todos' ? undefined : periodo
              }));
            }}
            className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
            disabled={loading}
          >
            {periodoOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Datas personalizadas */}
        {filtrosLocais.periodo === 'personalizado' && (
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-1">
                Data Início
              </label>
              <Input
                type="date"
                value={filtrosLocais.data_inicio || ''}
                onChange={(e) => setFiltrosLocais(prev => ({
                  ...prev,
                  data_inicio: e.target.value || undefined
                }))}
                disabled={loading}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-1">
                Data Fim
              </label>
              <Input
                type="date"
                value={filtrosLocais.data_fim || ''}
                onChange={(e) => setFiltrosLocais(prev => ({
                  ...prev,
                  data_fim: e.target.value || undefined
                }))}
                disabled={loading}
              />
            </div>
          </div>
        )}

        {/* Forma de Pagamento */}
        <div>
          <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
            Forma de Pagamento
          </label>
          <select
            value={filtrosLocais.forma_pagamento || 'todos'}
            onChange={(e) => setFiltrosLocais(prev => ({
              ...prev,
              forma_pagamento: e.target.value === 'todos' ? undefined : e.target.value as FormaPagamento
            }))}
            className="w-full px-3 py-2 border border-[var(--border-color)] rounded-lg focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
            disabled={loading}
          >
            <option value="todos">Todas as Formas</option>
            <option value="Online">Online</option>
            <option value="Local">Local</option>
          </select>
        </div>

        {/* Botões */}
        <div className="flex gap-2 pt-4">
          <Button
            onClick={handleAplicar}
            disabled={loading}
            className="flex-1"
          >
            {loading ? 'Aplicando...' : 'Aplicar'}
          </Button>
          <Button
            variant="outline"
            onClick={handleLimpar}
            disabled={loading}
            className="flex-1"
          >
            Limpar
          </Button>
        </div>

        {/* Resumo dos filtros ativos */}
        {(filtrosLocais.status_agendamento || filtrosLocais.periodo || filtrosLocais.forma_pagamento) && (
          <div className="pt-4 border-t border-[var(--border-color)]">
            <p className="text-sm font-medium text-[var(--text-secondary)] mb-2">
              Filtros Ativos:
            </p>
            <div className="space-y-1 text-xs text-[var(--text-secondary)]">
              {filtrosLocais.status_agendamento && (
                <div>• Status: {statusOptions.find(s => s.value === filtrosLocais.status_agendamento)?.label}</div>
              )}
              {filtrosLocais.periodo && (
                <div>• Período: {periodoOptions.find(p => p.value === filtrosLocais.periodo)?.label}</div>
              )}
              {filtrosLocais.forma_pagamento && (
                <div>• Pagamento: {filtrosLocais.forma_pagamento}</div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
