'use client';
import React, { useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export default function TestWebhookPage() {
  const { user, refreshUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testWebhook = async () => {
    if (!user) {
      alert('Você precisa estar logado para testar');
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // Simular um webhook do Stripe
      const response = await fetch('/api/webhook/stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'stripe-signature': 'test-signature'
        },
        body: JSON.stringify({
          type: 'payment_intent.succeeded',
          data: {
            object: {
              id: 'pi_test_123',
              amount: 9900,
              metadata: {
                plano: 'essencial',
                estabelecimento: 'Teste Estabelecimento',
                cnpj: '12345678000199',
                user_id: user.id
              }
            }
          }
        })
      });

      const data = await response.text();
      setResult({
        status: response.status,
        data: data
      });

      // Forçar atualização do usuário
      if (refreshUser) {
        await refreshUser();
      }

    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const checkUserData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const supabase = createClient();
      const { data: { user: authUser } } = await supabase.auth.getUser();
      
      setResult({
        user_metadata: authUser?.user_metadata,
        user_id: authUser?.id,
        email: authUser?.email
      });
    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const supabase = createClient();

      const { error } = await supabase.auth.updateUser({
        data: {
          role: 'Proprietario',
          pagamento_confirmado: true,
          onboarding_pendente: true,
          plano_selecionado: 'essencial'
        }
      });

      if (error) {
        setResult({ error: error.message });
      } else {
        setResult({ success: 'Usuário atualizado com sucesso' });

        // Forçar atualização
        if (refreshUser) {
          await refreshUser();
        }
      }
    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const testWebhookConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/webhook/stripe/test');
      const data = await response.json();
      setResult({
        test: 'Configuração Webhook',
        ...data
      });
    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const testStripeConnection = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-stripe');
      const data = await response.json();
      setResult({
        test: 'Conexão Stripe',
        ...data
      });
    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const simulatePayment = async () => {
    if (!user) {
      alert('Você precisa estar logado para simular pagamento');
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/webhook/stripe/simulate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          plano: 'essencial',
          cnpj: '12345678000199',
          estabelecimento: 'Empresa Teste LTDA'
        })
      });

      const data = await response.json();
      setResult({
        test: 'Simulação de Pagamento',
        status: response.status,
        ...data
      });

      // Forçar atualização do usuário após simulação
      if (response.ok && refreshUser) {
        setTimeout(() => refreshUser(), 1000);
      }

    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const testDatabase = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/test-database');
      const data = await response.json();

      setResult({
        test: 'Teste do Banco de Dados',
        status: response.status,
        ...data
      });

    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const testAdmin = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/test-admin');
      const data = await response.json();

      setResult({
        test: 'Teste Administrativo (Bypass RLS)',
        status: response.status,
        ...data
      });

    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  const resetUserToNormal = async () => {
    if (!user) {
      alert('Você precisa estar logado para resetar o usuário');
      return;
    }

    const confirmReset = confirm(
      'Tem certeza que deseja resetar seu usuário para o papel normal?\n\n' +
      'Isso irá:\n' +
      '• Definir role como "Usuario"\n' +
      '• Remover pagamento_confirmado\n' +
      '• Remover onboarding_pendente\n' +
      '• Limpar dados de empresa'
    );

    if (!confirmReset) return;

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/user/reset', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id
        })
      });

      const data = await response.json();

      setResult({
        test: 'Reset do Usuário',
        status: response.status,
        ...data
      });

      // Forçar atualização do usuário após reset
      if (response.ok && refreshUser) {
        setTimeout(() => refreshUser(), 1000);
      }

    } catch (error: unknown) {
      setResult({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto bg-white rounded-xl shadow-xl p-8 border border-gray-200">
          <h1 className="text-4xl font-bold text-center mb-8" style={{ color: '#111827' }}>
            🧪 Teste de Webhook e Autenticação
          </h1>

          {user ? (
            <div className="space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
                <h2 className="text-xl font-bold mb-6 flex items-center" style={{ color: '#111827' }}>
                  👤 Usuário Atual
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <p style={{ color: '#374151' }}>
                      <strong style={{ color: '#111827' }}>Email:</strong>
                      <span style={{ color: '#1d4ed8', marginLeft: '8px' }}>{user.email}</span>
                    </p>
                    <p style={{ color: '#374151' }}>
                      <strong style={{ color: '#111827' }}>Nome:</strong>
                      <span style={{ color: '#4b5563', marginLeft: '8px' }}>{user.name ?? 'Não informado'}</span>
                    </p>
                    <p style={{ color: '#374151' }}>
                      <strong style={{ color: '#111827' }}>ID:</strong>
                      <span className="text-xs font-mono bg-gray-100 px-2 py-1 rounded ml-2" style={{ color: '#4b5563' }}>{user.id}</span>
                    </p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <span style={{ color: '#111827', fontWeight: 'bold' }}>Papel:</span>
                      <span
                        className="ml-3 px-3 py-1 rounded-full text-sm font-bold"
                        style={{
                          backgroundColor: user.role === 'Proprietario' ? '#10b981' :
                                         user.role === 'Administrador' ? '#8b5cf6' :
                                         user.role === 'Colaborador' ? '#3b82f6' : '#6b7280',
                          color: '#ffffff'
                        }}
                      >
                        {user.role}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span style={{ color: '#111827', fontWeight: 'bold' }}>Pagamento:</span>
                      <span
                        className="ml-3 px-3 py-1 rounded-full text-sm font-bold"
                        style={{
                          backgroundColor: user.pagamento_confirmado ? '#10b981' : '#ef4444',
                          color: '#ffffff'
                        }}
                      >
                        {user.pagamento_confirmado ? 'Confirmado' : 'Pendente'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span style={{ color: '#111827', fontWeight: 'bold' }}>Onboarding:</span>
                      <span
                        className="ml-3 px-3 py-1 rounded-full text-sm font-bold"
                        style={{
                          backgroundColor: user.onboarding_pendente ? '#f59e0b' : '#6b7280',
                          color: '#ffffff'
                        }}
                      >
                        {user.onboarding_pendente ? 'Pendente' : 'Concluído'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <h2 className="text-2xl font-bold mb-4" style={{ color: '#111827' }}>🧪 Testes Disponíveis</h2>

                {/* Testes Principais */}
                <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>🚀 Testes Principais</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <button
                      onClick={simulatePayment}
                      disabled={loading}
                      className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50 font-bold shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      {loading ? 'Processando...' : '🚀 Simular Pagamento Completo'}
                    </button>

                    <button
                      onClick={testAdmin}
                      disabled={loading}
                      className="bg-pink-600 text-white px-6 py-3 rounded-lg hover:bg-pink-700 disabled:opacity-50 font-bold shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      👑 Testar Admin (Bypass RLS)
                    </button>

                    <button
                      onClick={resetUserToNormal}
                      disabled={loading}
                      className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 disabled:opacity-50 font-bold shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      🔄 Resetar para Usuário Normal
                    </button>
                  </div>
                </div>

                {/* Testes de Conexão */}
                <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>🔗 Testes de Conexão</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <button
                      onClick={testWebhookConfig}
                      disabled={loading}
                      className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 disabled:opacity-50 shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      {loading ? 'Testando...' : 'Verificar Configuração'}
                    </button>

                    <button
                      onClick={testStripeConnection}
                      disabled={loading}
                      className="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 disabled:opacity-50 shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      {loading ? 'Testando...' : 'Testar Conexão Stripe'}
                    </button>

                    <button
                      onClick={testDatabase}
                      disabled={loading}
                      className="bg-yellow-600 text-white px-6 py-3 rounded-lg hover:bg-yellow-700 disabled:opacity-50 font-bold shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      🗄️ Testar Banco de Dados
                    </button>
                  </div>
                </div>

                {/* Testes de Interface */}
                <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>🎨 Testes de Interface</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

                    <button
                      onClick={() => window.open('/pagamento-sucesso?payment_intent=pi_test_123&payment_intent_client_secret=pi_test_123_secret&redirect_status=succeeded', '_blank')}
                      disabled={loading}
                      className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50 font-bold shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      🧪 Testar Tela de Sucesso
                    </button>
                  </div>
                </div>

                {/* Testes de Usuário */}
                <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>👤 Testes de Usuário</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <button
                      onClick={checkUserData}
                      disabled={loading}
                      className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      {loading ? 'Verificando...' : 'Verificar Dados do Usuário'}
                    </button>

                    <button
                      onClick={updateUserRole}
                      disabled={loading}
                      className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 disabled:opacity-50 shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      {loading ? 'Atualizando...' : 'Atualizar para Proprietário'}
                    </button>

                    <button
                      onClick={() => refreshUser?.()}
                      disabled={loading}
                      className="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 disabled:opacity-50 shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      Forçar Atualização
                    </button>
                  </div>
                </div>

                {/* Testes de Debug */}
                <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
                  <h3 className="text-lg font-semibold mb-4" style={{ color: '#111827' }}>🐛 Testes de Debug</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <button
                      onClick={testWebhook}
                      disabled={loading}
                      className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 shadow-lg transition-all duration-200 hover:shadow-xl"
                    >
                      {loading ? 'Testando...' : 'Simular Webhook (com erro)'}
                    </button>
                  </div>
                </div>
              </div>

              {result && (
                <div className="bg-white p-6 rounded-xl border border-gray-300 shadow-lg">
                  <h3 className="text-xl font-bold mb-4 flex items-center" style={{ color: '#111827' }}>
                    📊 Resultado do Teste
                    {result.success === true && <span className="ml-2" style={{ color: '#059669' }}>✅</span>}
                    {result.success === false && <span className="ml-2" style={{ color: '#dc2626' }}>❌</span>}
                  </h3>
                  <div className="bg-gray-900 p-4 rounded-lg overflow-auto">
                    <pre className="text-sm text-green-400 font-mono">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="bg-yellow-50 p-8 rounded-xl border border-yellow-200 max-w-md mx-auto">
                <div className="text-6xl mb-4">🔒</div>
                <h2 className="text-2xl font-bold mb-4" style={{ color: '#111827' }}>Acesso Restrito</h2>
                <p className="mb-6" style={{ color: '#374151' }}>Você precisa estar logado para acessar os testes do sistema</p>
                <a
                  href="/login"
                  className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 font-bold shadow-lg transition-all duration-200 hover:shadow-xl inline-block"
                >
                  🚀 Fazer Login
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
