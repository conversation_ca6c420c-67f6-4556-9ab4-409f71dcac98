'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { createClient } from '@/utils/supabase/client';

// Carregando o Stripe com a chave publicável
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string);

// Componente de formulário de pagamento
function CheckoutForm() {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [planoSelecionado, setPlanoSelecionado] = useState<string | null>(null);
  const [dadosEstabelecimento, setDadosEstabelecimento] = useState<any>(null);

  useEffect(() => {
    // Recuperar dados do localStorage
    const plano = localStorage.getItem('planoSelecionado');
    const dadosEmpresa = localStorage.getItem('dadosEstabelecimento');

    if (!plano || !dadosEmpresa) {
      // Se não houver dados necessários, redirecionar para a etapa apropriada
      if (!plano) {
        router.push('/onboarding/selecao-plano');
      } else {
        router.push('/onboarding/registro-empresa');
      }
    } else {
      setPlanoSelecionado(plano);
      setDadosEstabelecimento(JSON.parse(dadosEmpresa));
    }
  }, [router]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js ainda não carregou
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    // Confirmar o pagamento com o Stripe.js
    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/onboarding/inicio`,
      },
    });

    if (error) {
      setErrorMessage(error.message || 'Ocorreu um erro ao processar o pagamento.');
      setLoading(false);
    }
    // O redirecionamento será tratado pelo Stripe após o pagamento bem-sucedido
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {planoSelecionado && dadosEstabelecimento && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Resumo da assinatura</h3>
          <p className="text-gray-700">
            <span className="font-medium">Plano:</span> {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'}
          </p>
          <p className="text-gray-700">
            <span className="font-medium">Valor mensal:</span> {planoSelecionado === 'essencial' ? 'R$ 99,00' : 'R$ 199,00'}
          </p>
          <p className="text-gray-700">
            <span className="font-medium">Estabelecimento:</span> {dadosEstabelecimento.nomeEstabelecimento}
          </p>
        </div>
      )}

      <div className="p-4 border border-gray-200 rounded-lg">
        <PaymentElement />
      </div>

      {errorMessage && (
        <div className="p-4 bg-red-50 text-red-700 rounded-lg">
          {errorMessage}
        </div>
      )}

      <div className="flex justify-between items-center mt-8">
        <Link href="/onboarding/registro-empresa" className="text-gray-600 hover:text-gray-800 transition duration-300">
          Voltar para registro da empresa
        </Link>
        <button
          type="submit"
          disabled={!stripe || loading}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition duration-300 disabled:bg-gray-400"
        >
          {loading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processando pagamento...
            </span>
          ) : 'Finalizar pagamento'}
        </button>
      </div>
    </form>
  );
}

// Componente principal da página de pagamento
export default function PagamentoAssinaturaPage() {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Recuperar dados do localStorage
    const plano = localStorage.getItem('planoSelecionado');
    const dadosEmpresa = localStorage.getItem('dadosEstabelecimento');

    if (plano) {
      // Criar intent de pagamento no servidor
      const createPaymentIntent = async () => {
        try {
          // Obter o usuário atual
          const supabase = createClient();
          const { data: { user } } = await supabase.auth.getUser();

          const response = await fetch('/api/create-payment-intent', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              plano,
              dadosEmpresa: dadosEmpresa ? JSON.parse(dadosEmpresa) : null,
              user_id: user?.id,
            }),
          });

          if (!response.ok) {
            throw new Error('Falha ao criar intent de pagamento');
          }

          const data = await response.json();
          setClientSecret(data.clientSecret);
        } catch (err) {
          setError('Ocorreu um erro ao preparar o pagamento. Por favor, tente novamente.');
          console.error('Erro ao criar payment intent:', err);
        } finally {
          setLoading(false);
        }
      };

      createPaymentIntent();
    }
  }, []);

  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#3b82f6',
    },
  };

  const options = {
    clientSecret: clientSecret || undefined,
    appearance,
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
            Pagamento da Assinatura
          </h1>
          <p className="text-gray-600 text-center mb-8">
            Complete o pagamento para ativar sua assinatura
          </p>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span className="ml-3 text-gray-700">Preparando pagamento...</span>
            </div>
          ) : error ? (
            <div className="p-6 bg-red-50 text-red-700 rounded-lg text-center">
              <p className="font-medium mb-4">{error}</p>
              <Link href="/onboarding/selecao-plano" className="text-blue-600 hover:text-blue-800 underline">
                Voltar para o início do processo
              </Link>
            </div>
          ) : clientSecret ? (
            <Elements options={options} stripe={stripePromise}>
              <CheckoutForm />
            </Elements>
          ) : null}
        </div>
      </div>
    </div>
  );
}