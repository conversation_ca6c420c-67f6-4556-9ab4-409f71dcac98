'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Edit, Trash2, Users, TrendingUp, DollarSign } from 'lucide-react';
import { usePlanosAssinatura } from '@/hooks/usePlanosAssinatura';
import { TemplatePlanoAssinatura } from '@/types/planosAssinatura';
import { formatCurrency } from '@/utils/formatters';

export default function PlanosAssinaturaPage() {
  const { 
    planos, 
    assinantes, 
    estatisticas, 
    loading, 
    error, 
    criarPlano, 
    atualizarPlano, 
    excluirPlano,
    refresh 
  } = usePlanosAssinatura();

  const [planoSelecionado, setPlanoSelecionado] = useState<TemplatePlanoAssinatura | null>(null);
  const [mostrarFormulario, setMostrarFormulario] = useState(false);

  const handleCriarPlano = () => {
    setPlanoSelecionado(null);
    setMostrarFormulario(true);
  };

  const handleEditarPlano = (plano: TemplatePlanoAssinatura) => {
    setPlanoSelecionado(plano);
    setMostrarFormulario(true);
  };

  const handleExcluirPlano = async (plano: TemplatePlanoAssinatura) => {
    if (confirm(`Tem certeza que deseja excluir o plano "${plano.nome_plano}"?`)) {
      const sucesso = await excluirPlano(plano.template_id!);
      if (sucesso) {
        alert('Plano excluído com sucesso!');
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p>Carregando planos de assinatura...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p className="font-medium">Erro ao carregar planos</p>
              <p className="text-sm mt-1">{error}</p>
              <Button 
                onClick={refresh} 
                variant="outline" 
                className="mt-4"
              >
                Tentar novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Planos de Assinatura</h1>
          <p className="text-gray-600 mt-1">
            Gerencie planos mensais para seus serviços (Funcionalidade Premium)
          </p>
        </div>
        <Button onClick={handleCriarPlano} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Criar Plano
        </Button>
      </div>

      {/* Estatísticas */}
      {estatisticas && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Total Assinantes</p>
                  <p className="text-2xl font-bold">{estatisticas.total_assinantes}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Assinantes Ativos</p>
                  <p className="text-2xl font-bold">{estatisticas.assinantes_ativos}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600">Receita Mensal</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(estatisticas.receita_mensal_recorrente)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600">Taxa Cancelamento</p>
                  <p className="text-2xl font-bold">
                    {estatisticas.taxa_cancelamento.toFixed(1)}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs */}
      <Tabs defaultValue="planos" className="space-y-4">
        <TabsList>
          <TabsTrigger value="planos">Meus Planos</TabsTrigger>
          <TabsTrigger value="assinantes">Assinantes</TabsTrigger>
        </TabsList>

        {/* Lista de Planos */}
        <TabsContent value="planos" className="space-y-4">
          {planos.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Nenhum plano criado</h3>
                  <p className="mb-4">
                    Crie seu primeiro plano de assinatura para oferecer aos clientes
                  </p>
                  <Button onClick={handleCriarPlano}>
                    Criar Primeiro Plano
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {planos.map((plano) => (
                <Card key={plano.template_id} className="relative">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{plano.nome_plano}</CardTitle>
                        <CardDescription>
                          {plano.descricao}
                        </CardDescription>
                      </div>
                      <Badge variant={plano.ativo ? "default" : "secondary"}>
                        {plano.ativo ? "Ativo" : "Inativo"}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-2xl font-bold text-green-600">
                        {formatCurrency(plano.preco_mensal)}/mês
                      </p>
                      <p className="text-sm text-gray-600">
                        {plano.limite_usos_mes 
                          ? `Até ${plano.limite_usos_mes} usos/mês`
                          : 'Usos ilimitados'
                        }
                      </p>
                    </div>

                    {plano.descricao && (
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {plano.descricao}
                      </p>
                    )}

                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Benefícios:</h4>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {plano.beneficios?.map((beneficio, index) => (
                          <li key={index} className="flex items-center gap-1">
                            <span className="w-1 h-1 bg-green-500 rounded-full"></span>
                            {beneficio}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex gap-2 pt-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditarPlano(plano)}
                        className="flex-1"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Editar
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExcluirPlano(plano)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Lista de Assinantes */}
        <TabsContent value="assinantes" className="space-y-4">
          {assinantes.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Nenhum assinante ainda</h3>
                  <p>
                    Quando clientes assinarem seus planos, eles aparecerão aqui
                  </p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {assinantes.map((assinatura) => (
                <Card key={assinatura.plano_cliente_id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div>
                          <h3 className="font-medium">
                            {assinatura.cliente?.nome || assinatura.cliente?.email}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {assinatura.nome_plano_empresa} - {assinatura.servico?.nome_servico}
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>
                            {formatCurrency(assinatura.preco_mensal_assinatura)}/mês
                          </span>
                          <span>
                            Usos: {assinatura.usos_consumidos_ciclo_atual}
                            {assinatura.limite_usos_mes && `/${assinatura.limite_usos_mes}`}
                          </span>
                          <span>
                            Renovação: {new Date(assinatura.data_proxima_cobranca!).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      <Badge 
                        variant={
                          assinatura.status === 'ativa' ? 'default' :
                          assinatura.status === 'pausada' ? 'secondary' :
                          assinatura.status === 'cancelada' ? 'destructive' :
                          'outline'
                        }
                      >
                        {assinatura.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Modal/Formulário seria implementado aqui */}
      {mostrarFormulario && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>
                {planoSelecionado ? 'Editar Plano' : 'Criar Novo Plano'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-gray-600 py-8">
                Formulário de criação/edição será implementado aqui
              </p>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => setMostrarFormulario(false)}
                  className="flex-1"
                >
                  Cancelar
                </Button>
                <Button className="flex-1">
                  {planoSelecionado ? 'Salvar' : 'Criar'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
