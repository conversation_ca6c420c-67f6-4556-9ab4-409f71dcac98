// Tipos para o módulo de colaboradores

// Interface principal do colaborador (baseada no schema do banco)
export interface Colaborador {
  associacao_id: number;
  empresa_id: number;
  colaborador_user_id: string;
  ativo_como_prestador: boolean;
  horarios_trabalho_individual: HorarioTrabalhoIndividual | null;
  convite_aceito: boolean;
  convite_token: string | null;
  convite_expira_em: string | null;
  custos_operacionais: number | null;
  percentual_comissao: number | null;
  ativo: boolean;
  created_at: string;
  updated_at: string;
  // Dados do usuário (join)
  user_profile?: {
    id: string;
    email: string;
    name?: string;
    phone?: string;
  };
  // Serviços associados
  servicos_associados?: ServicoAssociado[];
}

// Horários de trabalho individual do colaborador
export interface HorarioTrabalhoIndividual {
  [diaSemana: string]: {
    ativo: boolean;
    horario_inicio?: string;
    horario_fim?: string;
    intervalo_inicio?: string;
    intervalo_fim?: string;
    bloqueios?: BloqueioHorario[];
  };
}

// Bloqueio de horário específico
export interface BloqueioHorario {
  id: string;
  data: string;
  horario_inicio: string;
  horario_fim: string;
  motivo?: string;
  recorrente?: boolean;
}

// Serviço associado ao colaborador
export interface ServicoAssociado {
  col_serv_id: number;
  colaborador_user_id: string;
  servico_id: number;
  ativo: boolean;
  // Dados do serviço (join)
  servico?: {
    servico_id: number;
    nome_servico: string;
    categoria: string;
    duracao_minutos: number;
    preco: number;
  };
}

// Dados para criar convite de colaborador
export interface CriarConviteData {
  email_colaborador: string;
  servicos_ids: number[];
  ativo_como_prestador?: boolean;
  custos_operacionais?: number;
  percentual_comissao?: number;
}

// Dados para atualizar colaborador
export interface AtualizarColaboradorData {
  ativo_como_prestador?: boolean;
  horarios_trabalho_individual?: HorarioTrabalhoIndividual;
  custos_operacionais?: number;
  percentual_comissao?: number;
  ativo?: boolean;
}

// Dados para aceitar convite
export interface AceitarConviteData {
  token: string;
  user_id: string;
}

// Resposta da API para operações de colaboradores
export interface ColaboradorApiResponse {
  success: boolean;
  data?: Colaborador | Colaborador[] | any;
  error?: string;
  message?: string;
}

// Filtros para busca de colaboradores
export interface FiltrosColaboradores {
  ativo?: boolean;
  ativo_como_prestador?: boolean;
  convite_aceito?: boolean;
  busca?: string;
}

// Estatísticas de colaboradores
export interface EstatisticasColaboradores {
  total: number;
  ativos: number;
  inativos: number;
  prestadores_ativos: number;
  convites_pendentes: number;
  convites_expirados: number;
}

// Status do convite
export type StatusConvite = 'pendente' | 'aceito' | 'expirado' | 'cancelado';

// Dados do convite
export interface DadosConvite {
  token: string;
  email_colaborador: string;
  empresa_nome: string;
  proprietario_nome: string;
  servicos_nomes: string[];
  expira_em: string;
  status: StatusConvite;
}

// Validações
export interface ValidacaoColaborador {
  email_colaborador: string[];
  servicos_ids: string[];
  custos_operacionais: string[];
  percentual_comissao: string[];
}

// Limites por plano
export const LIMITES_COLABORADORES = {
  essencial: 2,
  premium: 6
} as const;

// Dias da semana
export const DIAS_SEMANA = [
  { key: 'segunda', label: 'Segunda-feira' },
  { key: 'terca', label: 'Terça-feira' },
  { key: 'quarta', label: 'Quarta-feira' },
  { key: 'quinta', label: 'Quinta-feira' },
  { key: 'sexta', label: 'Sexta-feira' },
  { key: 'sabado', label: 'Sábado' },
  { key: 'domingo', label: 'Domingo' }
] as const;

// Horários padrão de trabalho
export const HORARIOS_PADRAO: HorarioTrabalhoIndividual = {
  segunda: { ativo: true, horario_inicio: '08:00', horario_fim: '18:00', intervalo_inicio: '12:00', intervalo_fim: '13:00' },
  terca: { ativo: true, horario_inicio: '08:00', horario_fim: '18:00', intervalo_inicio: '12:00', intervalo_fim: '13:00' },
  quarta: { ativo: true, horario_inicio: '08:00', horario_fim: '18:00', intervalo_inicio: '12:00', intervalo_fim: '13:00' },
  quinta: { ativo: true, horario_inicio: '08:00', horario_fim: '18:00', intervalo_inicio: '12:00', intervalo_fim: '13:00' },
  sexta: { ativo: true, horario_inicio: '08:00', horario_fim: '18:00', intervalo_inicio: '12:00', intervalo_fim: '13:00' },
  sabado: { ativo: true, horario_inicio: '08:00', horario_fim: '16:00' },
  domingo: { ativo: false }
};

// Utilitários
export const gerarTokenConvite = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

export const calcularDataExpiracao = (): Date => {
  const agora = new Date();
  agora.setHours(agora.getHours() + 24); // 24 horas a partir de agora
  return agora;
};

export const verificarConviteExpirado = (dataExpiracao: string): boolean => {
  return new Date() > new Date(dataExpiracao);
};
