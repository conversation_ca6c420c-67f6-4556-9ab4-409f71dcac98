'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';

interface AuditEvent {
  id: string;
  timestamp: string;
  userId?: string;
  userRole?: string;
  action: string;
  resource: string;
  success: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category: 'AUTH' | 'DATA' | 'ADMIN' | 'PAYMENT' | 'SECURITY';
  ipAddress?: string;
}

interface SecurityAlert {
  id: string;
  timestamp: string;
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  resolved: boolean;
}

interface SecurityStats {
  totalEvents: number;
  eventsByCategory: Record<string, number>;
  eventsByRiskLevel: Record<string, number>;
  recentFailures: number;
  activeAlerts: number;
}

export default function SecurityDashboard() {
  const [auditEvents, setAuditEvents] = useState<AuditEvent[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [stats, setStats] = useState<SecurityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'alerts'>('overview');

  useEffect(() => {
    loadSecurityData();
    
    // Atualizar a cada 30 segundos
    const interval = setInterval(loadSecurityData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      
      // Carregar eventos de auditoria
      const auditResponse = await fetch('/api/security/audit?limit=50');
      if (auditResponse.ok) {
        const auditData = await auditResponse.json();
        setAuditEvents(auditData.data.events);
        setStats(auditData.data.stats);
      }
      
      // Carregar alertas de segurança
      const alertsResponse = await fetch('/api/security/alerts?limit=20');
      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setSecurityAlerts(alertsData.data.alerts);
      }
      
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/security/alerts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ alertId, action: 'resolve' })
      });
      
      if (response.ok) {
        setSecurityAlerts(prev => 
          prev.map(alert => 
            alert.id === alertId ? { ...alert, resolved: true } : alert
          )
        );
      }
    } catch (err: any) {
      console.error('Erro ao resolver alerta:', err);
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'CRITICAL': return 'text-red-600 bg-red-100';
      case 'HIGH': return 'text-orange-600 bg-orange-100';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
      case 'LOW': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'text-red-600 bg-red-100';
      case 'HIGH': return 'text-orange-600 bg-orange-100';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
      case 'LOW': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Carregando dados de segurança...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
        Erro ao carregar dados de segurança: {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard de Segurança</h1>
        <button
          onClick={loadSecurityData}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Atualizar
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Visão Geral' },
            { id: 'events', label: 'Eventos de Auditoria' },
            { id: 'alerts', label: 'Alertas de Segurança' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900">Total de Eventos</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.totalEvents}</p>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900">Falhas Recentes</h3>
            <p className="text-3xl font-bold text-red-600">{stats.recentFailures}</p>
            <p className="text-sm text-gray-500">Últimas 24h</p>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900">Alertas Ativos</h3>
            <p className="text-3xl font-bold text-orange-600">{stats.activeAlerts}</p>
          </Card>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900">Eventos Críticos</h3>
            <p className="text-3xl font-bold text-red-600">
              {stats.eventsByRiskLevel.CRITICAL || 0}
            </p>
          </Card>

          {/* Gráfico de Eventos por Categoria */}
          <Card className="p-6 md:col-span-2">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Eventos por Categoria</h3>
            <div className="space-y-2">
              {Object.entries(stats.eventsByCategory).map(([category, count]) => (
                <div key={category} className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-700">{category}</span>
                  <span className="text-sm text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </Card>

          {/* Gráfico de Eventos por Nível de Risco */}
          <Card className="p-6 md:col-span-2">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Eventos por Nível de Risco</h3>
            <div className="space-y-2">
              {Object.entries(stats.eventsByRiskLevel).map(([level, count]) => (
                <div key={level} className="flex justify-between items-center">
                  <span className={`text-sm font-medium px-2 py-1 rounded ${getRiskLevelColor(level)}`}>
                    {level}
                  </span>
                  <span className="text-sm text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {/* Events Tab */}
      {activeTab === 'events' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Eventos de Auditoria Recentes</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ação
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Usuário
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Risco
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {auditEvents.map((event) => (
                  <tr key={event.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {new Date(event.timestamp).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {event.action}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {event.userId ? `${event.userRole} (${event.userId.substring(0, 8)}...)` : 'Anônimo'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getRiskLevelColor(event.riskLevel)}`}>
                        {event.riskLevel}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${
                        event.success ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
                      }`}>
                        {event.success ? 'Sucesso' : 'Falha'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {/* Alerts Tab */}
      {activeTab === 'alerts' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alertas de Segurança</h3>
          <div className="space-y-4">
            {securityAlerts.map((alert) => (
              <div
                key={alert.id}
                className={`p-4 border rounded-lg ${
                  alert.resolved ? 'bg-gray-50 border-gray-200' : 'bg-white border-red-200'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getSeverityColor(alert.severity)}`}>
                        {alert.severity}
                      </span>
                      <span className="text-sm font-medium text-gray-900">{alert.type}</span>
                      {alert.resolved && (
                        <span className="px-2 py-1 text-xs font-medium rounded text-green-600 bg-green-100">
                          Resolvido
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{alert.description}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(alert.timestamp).toLocaleString()}
                    </p>
                  </div>
                  {!alert.resolved && (
                    <button
                      onClick={() => resolveAlert(alert.id)}
                      className="ml-4 px-3 py-1 text-xs font-medium text-white bg-green-600 rounded hover:bg-green-700"
                    >
                      Resolver
                    </button>
                  )}
                </div>
              </div>
            ))}
            {securityAlerts.length === 0 && (
              <p className="text-center text-gray-500 py-8">Nenhum alerta de segurança encontrado.</p>
            )}
          </div>
        </Card>
      )}
    </div>
  );
}
