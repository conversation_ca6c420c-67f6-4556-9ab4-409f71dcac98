/**
 * Tipos TypeScript para o Módulo de Relatórios
 * Implementação da Tarefa #20 - Desenvolver <PERSON>ódu<PERSON> de Relatórios
 */

// Tipos de período para filtros
export type PeriodoRelatorio = 'dia' | 'semana' | 'mes' | 'trimestre' | 'ano' | 'personalizado';

// Filtros para relatórios
export interface FiltrosRelatorio {
  periodo: PeriodoRelatorio;
  data_inicio?: string;
  data_fim?: string;
  colaborador_id?: string;
  servico_id?: number;
  forma_pagamento?: 'Online' | 'Local';
  status_agendamento?: 'Pendente' | 'Confirmado' | 'Cancelado' | 'Concluido';
}

// Dados básicos de agendamentos (Plano Essencial)
export interface RelatorioAgendamentosBasico {
  periodo: {
    inicio: string;
    fim: string;
    tipo: PeriodoRelatorio;
  };
  total_agendamentos: number;
  agendamentos_por_status: {
    pendentes: number;
    confirmados: number;
    cancelados: number;
    concluidos: number;
  };
  faturamento_bruto: number;
  faturamento_por_periodo: Array<{
    data: string;
    agendamentos: number;
    faturamento: number;
  }>;
}

// Dados completos de agendamentos (Plano Premium)
export interface RelatorioAgendamentosCompleto extends RelatorioAgendamentosBasico {
  faturamento_por_colaborador: Array<{
    colaborador_id: string;
    nome_colaborador: string;
    total_agendamentos: number;
    faturamento_bruto: number;
    faturamento_liquido: number;
    comissao_total: number;
    custos_operacionais: number;
  }>;
  faturamento_por_servico: Array<{
    servico_id: number;
    nome_servico: string;
    total_agendamentos: number;
    faturamento_total: number;
    ticket_medio: number;
  }>;
  faturamento_por_forma_pagamento: {
    online: number;
    local: number;
    pix: number;
    cartao_credito: number;
    cartao_debito: number;
  };
  metricas_avancadas: {
    ticket_medio_geral: number;
    taxa_conversao: number;
    taxa_cancelamento: number;
    tempo_medio_confirmacao: number;
    horarios_mais_procurados: Array<{
      hora: string;
      total_agendamentos: number;
    }>;
    dias_mais_movimentados: Array<{
      dia_semana: string;
      total_agendamentos: number;
    }>;
  };
}

// Relatório financeiro detalhado (Premium)
export interface RelatorioFinanceiro {
  periodo: {
    inicio: string;
    fim: string;
  };
  resumo_financeiro: {
    receita_total: number;
    receita_online: number;
    receita_local: number;
    comissoes_pagas: number;
    custos_operacionais: number;
    lucro_liquido: number;
  };
  evolucao_mensal: Array<{
    mes: string;
    receita: number;
    custos: number;
    lucro: number;
  }>;
  comparativo_periodo_anterior: {
    receita_variacao: number;
    agendamentos_variacao: number;
    ticket_medio_variacao: number;
  };
}

// Relatório de performance de colaboradores (Premium)
export interface RelatorioColaboradores {
  periodo: {
    inicio: string;
    fim: string;
  };
  colaboradores: Array<{
    colaborador_id: string;
    nome: string;
    email: string;
    total_agendamentos: number;
    agendamentos_confirmados: number;
    agendamentos_cancelados: number;
    taxa_confirmacao: number;
    faturamento_gerado: number;
    comissao_recebida: number;
    custos_operacionais: number;
    resultado_liquido: number;
    avaliacao_media: number;
    servicos_realizados: Array<{
      servico_id: number;
      nome_servico: string;
      quantidade: number;
      faturamento: number;
    }>;
    horarios_preferidos: Array<{
      hora: string;
      quantidade: number;
    }>;
  }>;
  ranking_performance: Array<{
    posicao: number;
    colaborador_id: string;
    nome: string;
    score_performance: number;
    criterios: {
      faturamento: number;
      taxa_confirmacao: number;
      pontualidade: number;
      avaliacao_clientes: number;
    };
  }>;
}

// Relatório de serviços (Premium)
export interface RelatorioServicos {
  periodo: {
    inicio: string;
    fim: string;
  };
  servicos: Array<{
    servico_id: number;
    nome_servico: string;
    categoria: string;
    preco: number;
    duracao_minutos: number;
    total_agendamentos: number;
    faturamento_total: number;
    ticket_medio: number;
    taxa_conversao: number;
    colaboradores_habilitados: number;
    horarios_mais_procurados: Array<{
      hora: string;
      quantidade: number;
    }>;
  }>;
  categorias_performance: Array<{
    categoria: string;
    total_servicos: number;
    total_agendamentos: number;
    faturamento_total: number;
    participacao_percentual: number;
  }>;
  servicos_mais_rentaveis: Array<{
    servico_id: number;
    nome_servico: string;
    faturamento_total: number;
    margem_lucro: number;
  }>;
}

// Resposta da API de relatórios
export interface RelatorioApiResponse<T> {
  success: boolean;
  data: T;
  plano_empresa: 'essencial' | 'premium';
  gerado_em: string;
  cache_valido_ate?: string;
  error?: string;
}

// Estado do hook de relatórios
export interface EstadoRelatorios {
  relatorioBasico: RelatorioAgendamentosBasico | null;
  relatorioCompleto: RelatorioAgendamentosCompleto | null;
  relatorioFinanceiro: RelatorioFinanceiro | null;
  relatorioColaboradores: RelatorioColaboradores | null;
  relatorioServicos: RelatorioServicos | null;
  loading: boolean;
  error: string | null;
  ultimaAtualizacao: string | null;
}

// Configurações de exportação
export interface ConfiguracaoExportacao {
  formato: 'pdf' | 'excel' | 'csv';
  incluir_graficos: boolean;
  incluir_detalhes: boolean;
  periodo_personalizado?: {
    inicio: string;
    fim: string;
  };
}

// Métricas de comparação
export interface MetricasComparacao {
  periodo_atual: {
    inicio: string;
    fim: string;
    valor: number;
  };
  periodo_anterior: {
    inicio: string;
    fim: string;
    valor: number;
  };
  variacao_absoluta: number;
  variacao_percentual: number;
  tendencia: 'crescimento' | 'declinio' | 'estavel';
}
