/**
 * Página de Relatórios do Proprietário
 * Implementação da Tarefa #20 - Desenvolver <PERSON><PERSON><PERSON><PERSON> de Relatórios
 */

'use client';

import React, { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { useRelatorios } from '@/hooks/useRelatorios';
import { RelatoriosBasicos } from '@/components/relatorios/RelatoriosBasicos';
import { FiltrosPeriodo } from '@/components/relatorios/FiltrosPeriodo';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

export default function RelatoriosPage() {
  return (
    <ProtectedRoute requiredRole="Proprietario">
      <RelatoriosContent />
    </ProtectedRoute>
  );
}

function RelatoriosContent() {
  const { user } = useAuth();
  const [tipoRelatorioAtivo, setTipoRelatorioAtivo] = useState<'agendamentos' | 'financeiro' | 'colaboradores' | 'servicos'>('agendamentos');

  const {
    relatorioBasico,
    relatorioCompleto,
    relatorioFinanceiro,
    relatorioColaboradores,
    relatorioServicos,
    loading,
    error,
    filtros,
    buscarRelatorioAgendamentos,
    buscarRelatorioFinanceiro,
    buscarRelatorioColaboradores,
    buscarRelatorioServicos,
    aplicarFiltros,
    limparFiltros,
    limparErro,
    exportarRelatorio,
    temDados,
    isPlanoEssencial,
    isPlanoPremium,
    ultimaAtualizacao
  } = useRelatorios();

  // Carregar relatório inicial
  useEffect(() => {
    buscarRelatorioAgendamentos();
  }, []);

  const handleAplicarFiltros = async () => {
    try {
      switch (tipoRelatorioAtivo) {
        case 'agendamentos':
          await buscarRelatorioAgendamentos();
          break;
        case 'financeiro':
          if (isPlanoPremium) await buscarRelatorioFinanceiro();
          break;
        case 'colaboradores':
          if (isPlanoPremium) await buscarRelatorioColaboradores();
          break;
        case 'servicos':
          if (isPlanoPremium) await buscarRelatorioServicos();
          break;
      }
    } catch (error) {
      console.error('Erro ao aplicar filtros:', error);
    }
  };

  const handleLimparFiltros = () => {
    limparFiltros();
    // Recarregar relatório com filtros limpos
    setTimeout(() => {
      handleAplicarFiltros();
    }, 100);
  };

  const handleExportar = async (formato: 'pdf' | 'excel' | 'csv') => {
    try {
      await exportarRelatorio(formato, tipoRelatorioAtivo);
    } catch (error) {
      console.error('Erro ao exportar relatório:', error);
    }
  };

  const tiposRelatorio = [
    { 
      id: 'agendamentos', 
      label: 'Agendamentos', 
      icon: '📅',
      disponivel: true,
      descricao: 'Relatórios de agendamentos e faturamento'
    },
    { 
      id: 'financeiro', 
      label: 'Financeiro', 
      icon: '💰',
      disponivel: isPlanoPremium,
      descricao: 'Análise financeira detalhada'
    },
    { 
      id: 'colaboradores', 
      label: 'Colaboradores', 
      icon: '👥',
      disponivel: isPlanoPremium,
      descricao: 'Performance dos colaboradores'
    },
    { 
      id: 'servicos', 
      label: 'Serviços', 
      icon: '🛠️',
      disponivel: isPlanoPremium,
      descricao: 'Análise de serviços oferecidos'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Relatórios e Análises
              </h1>
              <p className="text-gray-600">
                Acompanhe o desempenho do seu negócio
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Botões de Exportação */}
              {temDados && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportar('pdf')}
                    disabled={loading}
                  >
                    📄 PDF
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportar('excel')}
                    disabled={loading}
                  >
                    📊 Excel
                  </Button>
                </div>
              )}
              
              {/* Voltar ao Dashboard */}
              <Link href="/proprietario/dashboard">
                <Button variant="outline">
                  ← Voltar ao Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Filtros e Navegação */}
          <div className="lg:col-span-1 space-y-6">
            {/* Navegação entre Tipos de Relatório */}
            <Card>
              <CardHeader>
                <CardTitle>Tipos de Relatório</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {tiposRelatorio.map((tipo) => (
                  <button
                    key={tipo.id}
                    onClick={() => tipo.disponivel && setTipoRelatorioAtivo(tipo.id as any)}
                    disabled={!tipo.disponivel}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      tipoRelatorioAtivo === tipo.id
                        ? 'bg-blue-50 border-blue-200 text-blue-900'
                        : tipo.disponivel
                        ? 'bg-white border-gray-200 hover:bg-gray-50 text-gray-700'
                        : 'bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="text-lg mr-3">{tipo.icon}</span>
                        <div>
                          <div className="font-medium">{tipo.label}</div>
                          <div className="text-xs text-gray-500">{tipo.descricao}</div>
                        </div>
                      </div>
                      {!tipo.disponivel && (
                        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                          Premium
                        </span>
                      )}
                    </div>
                  </button>
                ))}
              </CardContent>
            </Card>

            {/* Filtros */}
            <FiltrosPeriodo
              filtros={filtros}
              onFiltrosChange={aplicarFiltros}
              onAplicarFiltros={handleAplicarFiltros}
              onLimparFiltros={handleLimparFiltros}
              loading={loading}
              isPlanoPremium={isPlanoPremium}
            />
          </div>

          {/* Main Content - Relatórios */}
          <div className="lg:col-span-3">
            {/* Mensagem de Erro */}
            {error && (
              <Card className="mb-6 border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-red-800">{error}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={limparErro}
                      className="text-red-600 border-red-300 hover:bg-red-100"
                    >
                      Fechar
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Conteúdo dos Relatórios */}
            {tipoRelatorioAtivo === 'agendamentos' && (
              <>
                {relatorioBasico && (
                  <RelatoriosBasicos 
                    relatorio={relatorioBasico} 
                    loading={loading} 
                  />
                )}
                {relatorioCompleto && (
                  <div>
                    {/* Aqui seria o componente RelatoriosCompletos quando implementado */}
                    <RelatoriosBasicos 
                      relatorio={relatorioCompleto} 
                      loading={loading} 
                    />
                  </div>
                )}
              </>
            )}

            {/* Placeholder para outros tipos de relatório */}
            {tipoRelatorioAtivo !== 'agendamentos' && !isPlanoPremium && (
              <Card className="border-2 border-dashed border-blue-300 bg-blue-50">
                <CardContent className="p-12 text-center">
                  <div className="text-blue-600 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-blue-900 mb-2">
                    Recurso Premium
                  </h3>
                  <p className="text-blue-700 mb-6">
                    Este tipo de relatório está disponível apenas no Plano Premium.
                    Faça upgrade para acessar análises avançadas!
                  </p>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Fazer Upgrade para Premium
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Loading State */}
            {loading && !temDados && (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Carregando relatórios...</p>
                </CardContent>
              </Card>
            )}

            {/* Estado Vazio */}
            {!loading && !temDados && !error && (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="text-gray-400 mb-4">
                    <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Nenhum dado encontrado
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Não há dados disponíveis para o período selecionado.
                  </p>
                  <Button onClick={handleAplicarFiltros}>
                    Tentar Novamente
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
