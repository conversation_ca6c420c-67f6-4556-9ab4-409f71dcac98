'use client';

import Link from "next/link";
import { buttonVariants } from "@/components/ui/Button";
import React from "react";

export function CTABottomButtons() {
  return (
    <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-6">
      <Link 
        href="/planos" 
        className={buttonVariants({ 
          variant: 'outline', 
          size: 'lg', 
        className: 'border-primary text-primary hover:bg-primary hover:text-on-primary shadow-lg transition-[background-color,color] duration-300' 
        })}
      >
        Ver Planos de Preços
      </Link>
      <Link 
        href="/planos" 
        className={buttonVariants({ 
          variant: 'primary', 
          size: 'lg', 
        className: 'bg-primary text-on-primary hover:bg-primary-hover shadow-lg transition-[background-color] duration-300' 
        })}
      >
        Comece Agora
      </Link>
    </div>
  );
}
