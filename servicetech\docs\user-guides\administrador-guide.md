# 🛡️ Guia do Administrador - ServiceTech

## 📋 Índice

- [Vis<PERSON> Geral](#visão-geral)
- [Acesso a<PERSON>](#acesso-ao-sistema)
- [Dashboard Administrativo](#dashboard-administrativo)
- [Gerenciamento de Usuários e Empresas](#gerenciamento-de-usuários-e-empresas)
- [Monitoramento de Segurança](#monitoramento-de-segurança)
- [Gerenciamento do Sistema de Notificações](#gerenciamento-do-sistema-de-notificações)
- [Relatórios e Analytics da Plataforma](#relatórios-e-analytics-da-plataforma)
- [Configurações Globais do Sistema](#configurações-globais-do-sistema)
- [Procedimentos de Emergência](#procedimentos-de-emergência)
- [Suporte](#suporte)

## 🎯 Visão Geral

Este guia é destinado aos Administradores da plataforma ServiceTech, detalhando suas responsabilidades e como utilizar as ferramentas administrativas para gerenciamento global, monitoramento e configuração do sistema. Para uma visão completa de todas as funcionalidades da plataforma, incluindo a arquitetura e os diferentes papéis de usuário, consulte o [README principal do projeto](../../readme.md).

## 🔐 Acesso ao Sistema

1.  **Login Administrativo**:
    *   Acesse a página de login designada para administradores (ex: `/admin/login` ou o login padrão `/login` se o sistema redirecionar baseado no papel).
    *   Utilize suas credenciais de administrador.
    *   Você será redirecionado para o Dashboard Administrativo (`/admin/dashboard`).
2.  **Permissões**:
    *   Seu papel de usuário deve ser "Administrador".
    *   Este papel concede acesso a todas as rotas e funcionalidades prefixadas com `/admin/*`.
    *   Em muitos casos, operações realizadas por você podem bypassar políticas RLS para permitir a gestão completa dos dados da plataforma.

## 📊 Dashboard Administrativo

Local: `/admin/dashboard`

O dashboard é sua central de comando, exibindo:
*   **Estatísticas Globais**: Número total de empresas, usuários (por papel), agendamentos totais, receita da plataforma, etc.
*   **Alertas do Sistema**: Notificações sobre empresas com pagamentos de assinatura pendentes, atividades de usuário suspeitas, erros críticos no sistema ou métricas de performance fora do normal.
*   **Atalhos Rápidos**: Links para as principais seções de gerenciamento.

## 👥 Gerenciamento de Usuários e Empresas

### Gestão de Usuários
*   **Visualizar Usuários**: Acesse uma lista completa de todos os usuários da plataforma.
*   **Filtros**: Filtre por papel (Proprietario, Colaborador, Usuario), status da conta (ativo, suspenso), data de cadastro.
*   **Busca**: Procure usuários por email, nome ou ID.
*   **Ações em Usuários**:
    *   **Alterar Papel**: Modifique o papel de um usuário (com cautela).
    *   **Suspender/Reativar Conta**: Desative ou reative o acesso de um usuário.
    *   **Forçar Reset de Senha**: Envie um link de redefinição de senha.
    *   **Visualizar Atividade**: Acesse o histórico de ações do usuário (se o sistema de auditoria estiver implementado para este nível).

### Gestão de Empresas
*   **Visualizar Empresas**: Acesse uma lista de todas as empresas cadastradas.
*   **Aprovar/Rejeitar Novos Cadastros**: Se houver um fluxo de aprovação manual para novas empresas.
*   **Suspender/Reativar Empresas**: Desative ou reative empresas que violem os termos ou tenham problemas de pagamento de assinatura.
*   **Gerenciar Assinaturas SaaS**: Visualize o plano de cada empresa, status de pagamento da assinatura, e realize ações administrativas se necessário (ex: aplicar um desconto manualmente, estender um trial).
*   **Configurações da Empresa (Visão Admin)**: Acesse as configurações de uma empresa para fins de suporte ou auditoria.

## 🛡️ Monitoramento de Segurança

Acesse em: `/admin/security`

*   **Alertas de Segurança**: Monitore tentativas de login suspeitas, violações de rate limiting, possíveis tentativas de injeção de código, acessos não autorizados.
*   **Logs de Auditoria**: Analise logs detalhados de ações administrativas, alterações de dados sensíveis e outros eventos de segurança.
*   **Configurações de Segurança**:
    *   **Rate Limiting**: Revise e ajuste os limites de requisição por endpoint ou tipo de usuário.
    *   **Políticas RLS**: (Geralmente gerenciadas via migrações SQL) Entenda e, se necessário, planeje atualizações nas políticas de segurança no nível do banco de dados.
    *   **Headers de Segurança**: Verifique e gerencie configurações como CSP (Content Security Policy).

## 📧 Gerenciamento do Sistema de Notificações

Acesse em: `/admin/notifications` e `/admin/notifications-sms-push`

*   **Templates de Email**: Gerencie os templates de email usados para notificações transacionais (novo agendamento, confirmação, etc.).
*   **Configurações de Envio**:
    *   **Resend (Email)**: Monitore o status da integração.
    *   **Twilio (SMS)**: Configure e teste a integração para notificações SMS (Plano Premium).
    *   **Firebase (Push)**: Gerencie a integração para notificações Push (Plano Premium).
*   **Estatísticas de Envio**: Acompanhe taxas de entrega, abertura (se disponível pelo provedor), e erros.
*   **Testes**: Envie notificações de teste para verificar a funcionalidade dos diferentes canais.
*   **Logs de Notificações**: Consulte o status de envio de notificações individuais e em lote.

## 📈 Relatórios e Analytics da Plataforma

*   **Relatórios Globais**:
    *   Crescimento da base de usuários e empresas.
    *   Volume total de agendamentos e tendências.
    *   Performance financeira da plataforma (receita de assinaturas, etc.).
    *   Funcionalidades mais utilizadas.
*   **Métricas de Performance do Sistema**:
    *   Tempo de resposta médio das APIs e páginas críticas.
    *   Taxa de erro por endpoint.
    *   Uptime da plataforma.
    *   Uso de recursos do servidor (CPU, memória, banco de dados).
*   **Exportação de Dados**: Gere relatórios em formatos como CSV, Excel, ou PDF para análise externa ou auditoria.

## ⚙️ Configurações Globais do Sistema

*   **Planos SaaS**: Defina e ajuste os detalhes dos planos de assinatura (Essencial, Premium), incluindo preços, limites de serviços/colaboradores, e funcionalidades incluídas.
*   **Comissões da Plataforma**: Configure o percentual de comissão que a ServiceTech retém sobre pagamentos online processados via Stripe Connect.
*   **Termos e Políticas**: Atualize os Termos de Uso e a Política de Privacidade da plataforma.
*   **Modo de Manutenção**: Habilite/desabilite o modo de manutenção da plataforma, que restringe o acesso de usuários não-administradores durante atualizações críticas.
*   **Integrações de Terceiros**: Gerencie as chaves de API e configurações para serviços externos (Stripe, Resend, Twilio, Firebase).

## 🚨 Procedimentos de Emergência

Tenha planos de ação para:
*   **Incidentes de Segurança**: Passos para identificar, isolar, investigar, remediar e documentar violações de segurança.
*   **Problemas de Performance**: Como monitorar, diagnosticar gargalos, escalar recursos e otimizar.
*   **Falhas do Sistema**: Procedimentos para detecção, avaliação de impacto, ativação de planos de contingência, restauração de serviços e análise de causa raiz.

## 📞 Suporte

*   **Canais Internos**: Documentação técnica, logs e ferramentas de diagnóstico.
*   **Suporte ao Desenvolvedor**: Se necessário, contate a equipe de desenvolvimento ou consultores externos.

---

**ServiceTech Admin** - Controle total da plataforma 🛡️
