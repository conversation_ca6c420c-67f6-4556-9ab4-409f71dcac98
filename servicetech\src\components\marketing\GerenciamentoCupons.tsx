'use client';

import { useState } from 'react';
import { useCupons } from '@/hooks/useCupons';
import { FiltrosCupons, Cupom } from '@/types/marketing';
import { FormularioCupom } from './FormularioCupom';

export function GerenciamentoCupons() {
  const {
    cupons,
    carregando,
    erro,
    total,
    paginaAtual,
    totalPaginas,
    buscarCupons,
    excluirCupom,
    limparErro
  } = useCupons();

  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [cupomEditando, setCupomEditando] = useState<Cupom | null>(null);
  const [filtros, setFiltros] = useState<FiltrosCupons>({
    busca: '',
    status: 'todos',
    tipo_desconto: 'todos'
  });

  // Aplicar filtros
  const aplicarFiltros = () => {
    buscarCupons(filtros, 1);
  };

  // Limpar filtros
  const limparFiltros = () => {
    const filtrosLimpos: FiltrosCupons = {
      busca: '',
      status: 'todos',
      tipo_desconto: 'todos'
    };
    setFiltros(filtrosLimpos);
    buscarCupons(filtrosLimpos, 1);
  };

  // Editar cupom
  const editarCupom = (cupom: Cupom) => {
    setCupomEditando(cupom);
    setMostrarFormulario(true);
  };

  // Excluir cupom
  const handleExcluirCupom = async (id: number) => {
    if (window.confirm('Tem certeza que deseja excluir este cupom?')) {
      await excluirCupom(id);
    }
  };

  // Fechar formulário
  const fecharFormulario = () => {
    setMostrarFormulario(false);
    setCupomEditando(null);
  };

  // Verificar status do cupom
  const getStatusCupom = (cupom: Cupom) => {
    const agora = new Date();
    const dataFim = new Date(cupom.data_fim);
    
    if (!cupom.ativo) return 'Inativo';
    if (agora > dataFim) return 'Expirado';
    if (cupom.limite_usos_total && cupom.usos_realizados >= cupom.limite_usos_total) return 'Esgotado';
    return 'Ativo';
  };

  // Cor do status
  const getCorStatus = (status: string) => {
    switch (status) {
      case 'Ativo': return 'text-green-600 bg-green-100';
      case 'Inativo': return 'text-gray-600 bg-gray-100';
      case 'Expirado': return 'text-red-600 bg-red-100';
      case 'Esgotado': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (mostrarFormulario) {
    return (
      <FormularioCupom
        cupom={cupomEditando}
        onSalvar={fecharFormulario}
        onCancelar={fecharFormulario}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Cupons de Desconto</h2>
          <p className="text-gray-600">Gerencie cupons promocionais para seus clientes</p>
        </div>
        <button
          onClick={() => setMostrarFormulario(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Novo Cupom
        </button>
      </div>

      {/* Filtros */}
      <div className="bg-white p-4 rounded-lg shadow border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <input
              type="text"
              value={filtros.busca}
              onChange={(e) => setFiltros(prev => ({ ...prev, busca: e.target.value }))}
              placeholder="Nome ou código do cupom"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filtros.status}
              onChange={(e) => setFiltros(prev => ({ ...prev, status: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="todos">Todos</option>
              <option value="ativos">Ativos</option>
              <option value="inativos">Inativos</option>
              <option value="expirados">Expirados</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tipo de Desconto
            </label>
            <select
              value={filtros.tipo_desconto}
              onChange={(e) => setFiltros(prev => ({ ...prev, tipo_desconto: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="todos">Todos</option>
              <option value="valor_fixo">Valor Fixo</option>
              <option value="percentual">Percentual</option>
            </select>
          </div>

          <div className="flex items-end space-x-2">
            <button
              onClick={aplicarFiltros}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Filtrar
            </button>
            <button
              onClick={limparFiltros}
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 transition-colors"
            >
              Limpar
            </button>
          </div>
        </div>
      </div>

      {/* Mensagem de erro */}
      {erro && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <p className="text-red-800">{erro}</p>
            <button
              onClick={limparErro}
              className="text-red-600 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Lista de cupons */}
      <div className="bg-white rounded-lg shadow border">
        {carregando ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Carregando cupons...</p>
          </div>
        ) : cupons.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-500">Nenhum cupom encontrado</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cupom
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Desconto
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Validade
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Usos
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {cupons.map((cupom) => {
                  const status = getStatusCupom(cupom);
                  return (
                    <tr key={cupom.cupom_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {cupom.codigo_cupom}
                          </div>
                          <div className="text-sm text-gray-500">
                            {cupom.nome_cupom}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {cupom.tipo_desconto === 'valor_fixo' 
                            ? `R$ ${cupom.valor_desconto.toFixed(2)}`
                            : `${cupom.valor_desconto}%`
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {new Date(cupom.data_inicio).toLocaleDateString()} - {new Date(cupom.data_fim).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {cupom.usos_realizados}
                          {cupom.limite_usos_total && ` / ${cupom.limite_usos_total}`}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getCorStatus(status)}`}>
                          {status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => editarCupom(cupom)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Editar
                        </button>
                        <button
                          onClick={() => handleExcluirCupom(cupom.cupom_id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Excluir
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}

        {/* Paginação */}
        {totalPaginas > 1 && (
          <div className="px-6 py-3 border-t border-gray-200 flex justify-between items-center">
            <div className="text-sm text-gray-700">
              Mostrando {cupons.length} de {total} cupons
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => buscarCupons(filtros, paginaAtual - 1)}
                disabled={paginaAtual === 1}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Anterior
              </button>
              <span className="px-3 py-1 text-sm text-gray-700">
                Página {paginaAtual} de {totalPaginas}
              </span>
              <button
                onClick={() => buscarCupons(filtros, paginaAtual + 1)}
                disabled={paginaAtual === totalPaginas}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Próxima
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
