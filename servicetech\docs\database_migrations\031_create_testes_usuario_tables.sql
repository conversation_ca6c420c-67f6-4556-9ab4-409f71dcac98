-- Migração 031: Criação das tabelas para sistema de testes de usuário
-- Data: 2024-01-XX
-- Descrição: Implementa sistema completo de testes de usuário com cenários, sessões, participantes e feedback

-- Tabela de cenários de teste
CREATE TABLE IF NOT EXISTS cenarios_teste (
    cenario_id SERIAL PRIMARY KEY,
    nome_cenario VARCHAR(255) NOT NULL,
    descricao TEXT NOT NULL,
    papel_usuario VARCHAR(50) NOT NULL CHECK (papel_usuario IN ('Administrador', 'Proprietario', 'Colaborador', 'Cliente')),
    categoria VARCHAR(50) NOT NULL CHECK (categoria IN ('Onboarding', 'Agendamento', 'Gestao', 'Pagamento', 'Navegacao')),
    dificuldade VARCHAR(20) NOT NULL CHECK (dificuldade IN ('Facil', 'Medio', 'Dificil')),
    tempo_estimado_minutos INTEGER NOT NULL DEFAULT 30,
    passos JSONB NOT NULL DEFAULT '[]',
    criterios_sucesso JSONB NOT NULL DEFAULT '[]',
    metricas_alvo JSONB NOT NULL DEFAULT '{}',
    ativo BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de sessões de teste
CREATE TABLE IF NOT EXISTS sessoes_teste (
    sessao_id SERIAL PRIMARY KEY,
    nome_sessao VARCHAR(255) NOT NULL,
    descricao TEXT NOT NULL,
    data_inicio TIMESTAMP WITH TIME ZONE NOT NULL,
    data_fim TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'Planejada' CHECK (status IN ('Planejada', 'Em_Andamento', 'Concluida', 'Cancelada')),
    cenarios_incluidos JSONB NOT NULL DEFAULT '[]',
    participantes_alvo INTEGER NOT NULL DEFAULT 5,
    participantes_confirmados INTEGER NOT NULL DEFAULT 0,
    moderador_user_id UUID NOT NULL REFERENCES auth.users(id),
    observacoes_gerais TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de participantes de teste
CREATE TABLE IF NOT EXISTS participantes_teste (
    participante_id SERIAL PRIMARY KEY,
    sessao_id INTEGER NOT NULL REFERENCES sessoes_teste(sessao_id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id),
    nome_participante VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    telefone VARCHAR(20),
    papel_usuario VARCHAR(50) NOT NULL CHECK (papel_usuario IN ('Administrador', 'Proprietario', 'Colaborador', 'Cliente')),
    experiencia_tecnologia VARCHAR(20) NOT NULL CHECK (experiencia_tecnologia IN ('Baixa', 'Media', 'Alta')),
    experiencia_agendamento VARCHAR(20) NOT NULL CHECK (experiencia_agendamento IN ('Nenhuma', 'Pouca', 'Moderada', 'Muita')),
    status_participacao VARCHAR(20) NOT NULL DEFAULT 'Convidado' CHECK (status_participacao IN ('Convidado', 'Confirmado', 'Participou', 'Ausente')),
    data_convite TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    data_confirmacao TIMESTAMP WITH TIME ZONE,
    observacoes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de execuções de teste
CREATE TABLE IF NOT EXISTS execucoes_teste (
    execucao_id SERIAL PRIMARY KEY,
    sessao_id INTEGER NOT NULL REFERENCES sessoes_teste(sessao_id) ON DELETE CASCADE,
    participante_id INTEGER NOT NULL REFERENCES participantes_teste(participante_id) ON DELETE CASCADE,
    cenario_id INTEGER NOT NULL REFERENCES cenarios_teste(cenario_id) ON DELETE CASCADE,
    data_inicio TIMESTAMP WITH TIME ZONE NOT NULL,
    data_fim TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) NOT NULL DEFAULT 'Iniciado' CHECK (status IN ('Iniciado', 'Em_Progresso', 'Concluido', 'Abandonado', 'Erro_Tecnico')),
    tempo_total_segundos INTEGER,
    passos_completados INTEGER NOT NULL DEFAULT 0,
    passos_total INTEGER NOT NULL DEFAULT 0,
    erros_encontrados INTEGER NOT NULL DEFAULT 0,
    ajuda_solicitada INTEGER NOT NULL DEFAULT 0,
    sucesso_completo BOOLEAN NOT NULL DEFAULT false,
    observacoes_participante TEXT,
    observacoes_moderador TEXT,
    gravacao_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de feedback de teste
CREATE TABLE IF NOT EXISTS feedback_teste (
    feedback_id SERIAL PRIMARY KEY,
    execucao_id INTEGER NOT NULL REFERENCES execucoes_teste(execucao_id) ON DELETE CASCADE,
    participante_id INTEGER NOT NULL REFERENCES participantes_teste(participante_id) ON DELETE CASCADE,
    cenario_id INTEGER NOT NULL REFERENCES cenarios_teste(cenario_id) ON DELETE CASCADE,
    
    -- Avaliações quantitativas (1-10)
    facilidade_uso INTEGER CHECK (facilidade_uso >= 1 AND facilidade_uso <= 10),
    clareza_interface INTEGER CHECK (clareza_interface >= 1 AND clareza_interface <= 10),
    velocidade_sistema INTEGER CHECK (velocidade_sistema >= 1 AND velocidade_sistema <= 10),
    satisfacao_geral INTEGER CHECK (satisfacao_geral >= 1 AND satisfacao_geral <= 10),
    probabilidade_recomendacao INTEGER CHECK (probabilidade_recomendacao >= 1 AND probabilidade_recomendacao <= 10),
    
    -- Feedback qualitativo
    aspectos_positivos TEXT,
    aspectos_negativos TEXT,
    sugestoes_melhoria TEXT,
    funcionalidades_faltantes TEXT,
    comentarios_gerais TEXT,
    
    -- Problemas específicos (JSON array)
    problemas_encontrados JSONB NOT NULL DEFAULT '[]',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de templates de cenários
CREATE TABLE IF NOT EXISTS templates_cenarios (
    template_id SERIAL PRIMARY KEY,
    nome_template VARCHAR(255) NOT NULL,
    descricao TEXT NOT NULL,
    papel_usuario VARCHAR(50) NOT NULL CHECK (papel_usuario IN ('Administrador', 'Proprietario', 'Colaborador', 'Cliente')),
    categoria VARCHAR(50) NOT NULL CHECK (categoria IN ('Onboarding', 'Agendamento', 'Gestao', 'Pagamento', 'Navegacao')),
    dificuldade VARCHAR(20) NOT NULL CHECK (dificuldade IN ('Facil', 'Medio', 'Dificil')),
    tempo_estimado_minutos INTEGER NOT NULL DEFAULT 30,
    passos_template JSONB NOT NULL DEFAULT '[]',
    criterios_sucesso_template JSONB NOT NULL DEFAULT '[]',
    metricas_alvo_template JSONB NOT NULL DEFAULT '{}',
    ativo BOOLEAN NOT NULL DEFAULT true,
    uso_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_cenarios_teste_papel_usuario ON cenarios_teste(papel_usuario);
CREATE INDEX IF NOT EXISTS idx_cenarios_teste_categoria ON cenarios_teste(categoria);
CREATE INDEX IF NOT EXISTS idx_cenarios_teste_ativo ON cenarios_teste(ativo);

CREATE INDEX IF NOT EXISTS idx_sessoes_teste_status ON sessoes_teste(status);
CREATE INDEX IF NOT EXISTS idx_sessoes_teste_moderador ON sessoes_teste(moderador_user_id);
CREATE INDEX IF NOT EXISTS idx_sessoes_teste_data_inicio ON sessoes_teste(data_inicio);

CREATE INDEX IF NOT EXISTS idx_participantes_teste_sessao ON participantes_teste(sessao_id);
CREATE INDEX IF NOT EXISTS idx_participantes_teste_status ON participantes_teste(status_participacao);
CREATE INDEX IF NOT EXISTS idx_participantes_teste_email ON participantes_teste(email);

CREATE INDEX IF NOT EXISTS idx_execucoes_teste_sessao ON execucoes_teste(sessao_id);
CREATE INDEX IF NOT EXISTS idx_execucoes_teste_participante ON execucoes_teste(participante_id);
CREATE INDEX IF NOT EXISTS idx_execucoes_teste_cenario ON execucoes_teste(cenario_id);
CREATE INDEX IF NOT EXISTS idx_execucoes_teste_status ON execucoes_teste(status);

CREATE INDEX IF NOT EXISTS idx_feedback_teste_execucao ON feedback_teste(execucao_id);
CREATE INDEX IF NOT EXISTS idx_feedback_teste_participante ON feedback_teste(participante_id);
CREATE INDEX IF NOT EXISTS idx_feedback_teste_cenario ON feedback_teste(cenario_id);

CREATE INDEX IF NOT EXISTS idx_templates_cenarios_categoria ON templates_cenarios(categoria);
CREATE INDEX IF NOT EXISTS idx_templates_cenarios_ativo ON templates_cenarios(ativo);

-- Triggers para atualizar updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_cenarios_teste_updated_at BEFORE UPDATE ON cenarios_teste FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sessoes_teste_updated_at BEFORE UPDATE ON sessoes_teste FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_templates_cenarios_updated_at BEFORE UPDATE ON templates_cenarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para calcular tempo total de execução
CREATE OR REPLACE FUNCTION calcular_tempo_execucao()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.data_fim IS NOT NULL AND NEW.data_inicio IS NOT NULL THEN
        NEW.tempo_total_segundos = EXTRACT(EPOCH FROM (NEW.data_fim - NEW.data_inicio));
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_calcular_tempo_execucao 
    BEFORE UPDATE ON execucoes_teste 
    FOR EACH ROW 
    EXECUTE FUNCTION calcular_tempo_execucao();

-- Função para atualizar contador de participantes confirmados
CREATE OR REPLACE FUNCTION atualizar_participantes_confirmados()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE sessoes_teste 
        SET participantes_confirmados = (
            SELECT COUNT(*) 
            FROM participantes_teste 
            WHERE sessao_id = NEW.sessao_id 
            AND status_participacao IN ('Confirmado', 'Participou')
        )
        WHERE sessao_id = NEW.sessao_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE sessoes_teste 
        SET participantes_confirmados = (
            SELECT COUNT(*) 
            FROM participantes_teste 
            WHERE sessao_id = OLD.sessao_id 
            AND status_participacao IN ('Confirmado', 'Participou')
        )
        WHERE sessao_id = OLD.sessao_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_atualizar_participantes_confirmados
    AFTER INSERT OR UPDATE OR DELETE ON participantes_teste
    FOR EACH ROW
    EXECUTE FUNCTION atualizar_participantes_confirmados();

-- Inserir templates padrão
INSERT INTO templates_cenarios (nome_template, descricao, papel_usuario, categoria, dificuldade, tempo_estimado_minutos, passos_template, criterios_sucesso_template, metricas_alvo_template) VALUES
('Primeiro Agendamento - Cliente', 'Template para testar o primeiro agendamento de um cliente novo', 'Cliente', 'Agendamento', 'Medio', 25, 
'[
    {"descricao": "Acessar a página de agendamento", "acao_esperada": "Clicar no botão Agendar Serviço", "resultado_esperado": "Página de agendamento carrega corretamente"},
    {"descricao": "Selecionar estabelecimento", "acao_esperada": "Escolher um estabelecimento da lista", "resultado_esperado": "Estabelecimento é selecionado e serviços aparecem"},
    {"descricao": "Escolher serviço", "acao_esperada": "Selecionar um serviço disponível", "resultado_esperado": "Serviço é selecionado e horários aparecem"},
    {"descricao": "Selecionar data e horário", "acao_esperada": "Escolher data e horário disponível", "resultado_esperado": "Data e horário são selecionados"},
    {"descricao": "Confirmar agendamento", "acao_esperada": "Clicar em confirmar agendamento", "resultado_esperado": "Agendamento é criado com sucesso"}
]',
'["Usuário consegue completar o agendamento sem ajuda", "Processo é intuitivo e claro", "Confirmação é exibida adequadamente"]',
'{"tempo_maximo_minutos": 35, "taxa_sucesso_minima": 85, "pontuacao_satisfacao_minima": 7, "taxa_erro_maxima": 15}'),

('Onboarding Proprietário', 'Template para testar o processo de onboarding de proprietários', 'Proprietario', 'Onboarding', 'Medio', 45,
'[
    {"descricao": "Completar cadastro inicial", "acao_esperada": "Preencher dados da empresa", "resultado_esperado": "Dados são salvos corretamente"},
    {"descricao": "Configurar serviços", "acao_esperada": "Adicionar pelo menos um serviço", "resultado_esperado": "Serviço é criado e aparece na lista"},
    {"descricao": "Definir horários de funcionamento", "acao_esperada": "Configurar horários da semana", "resultado_esperado": "Horários são salvos"},
    {"descricao": "Configurar colaboradores", "acao_esperada": "Adicionar pelo menos um colaborador", "resultado_esperado": "Colaborador é adicionado"},
    {"descricao": "Finalizar configuração", "acao_esperada": "Completar o onboarding", "resultado_esperado": "Dashboard principal é exibido"}
]',
'["Proprietário completa todo o onboarding", "Todas as configurações básicas são feitas", "Sistema fica pronto para uso"]',
'{"tempo_maximo_minutos": 60, "taxa_sucesso_minima": 80, "pontuacao_satisfacao_minima": 7, "taxa_erro_maxima": 20}'),

('Navegação Geral', 'Template para testar a navegação geral da plataforma', 'Cliente', 'Navegacao', 'Facil', 15,
'[
    {"descricao": "Explorar menu principal", "acao_esperada": "Navegar pelos itens do menu", "resultado_esperado": "Todas as páginas carregam corretamente"},
    {"descricao": "Usar busca", "acao_esperada": "Pesquisar por estabelecimentos", "resultado_esperado": "Resultados relevantes são exibidos"},
    {"descricao": "Acessar perfil", "acao_esperada": "Ir para página de perfil", "resultado_esperado": "Dados do usuário são exibidos"},
    {"descricao": "Ver histórico", "acao_esperada": "Acessar histórico de agendamentos", "resultado_esperado": "Lista de agendamentos é exibida"}
]',
'["Usuário navega sem se perder", "Todas as funcionalidades são encontradas facilmente", "Interface é intuitiva"]',
'{"tempo_maximo_minutos": 20, "taxa_sucesso_minima": 90, "pontuacao_satisfacao_minima": 8, "taxa_erro_maxima": 10}');

-- Comentários nas tabelas
COMMENT ON TABLE cenarios_teste IS 'Armazena cenários de teste de usuário com passos detalhados e critérios de sucesso';
COMMENT ON TABLE sessoes_teste IS 'Gerencia sessões de teste com múltiplos participantes e cenários';
COMMENT ON TABLE participantes_teste IS 'Registra participantes das sessões de teste com suas características';
COMMENT ON TABLE execucoes_teste IS 'Registra a execução individual de cada cenário por participante';
COMMENT ON TABLE feedback_teste IS 'Coleta feedback detalhado dos participantes sobre cada cenário testado';
COMMENT ON TABLE templates_cenarios IS 'Templates predefinidos para criação rápida de cenários comuns';
