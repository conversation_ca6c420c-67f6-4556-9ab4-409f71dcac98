# **Documento de Requisitos do Produto (PRD) \- ServiceTech**

Versão: 1.0  
Data: 30 de maio de 2025  
Produto: ServiceTech \- Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais  
Gerente de Produto: \[Seu Nome/IA\]  
Cliente: \[Nome do Cliente\]

## **1\. Introdução**

### **1.1. Visão Geral do Produto**

O ServiceTech é uma plataforma SaaS (Software as a Service) robusta e intuitiva, projetada para revolucionar a maneira como estabelecimentos de serviços pessoais (como barbearias, salões de beleza, clínicas de estética) gerenciam seus agendamentos, clientes e operações diárias. A plataforma facilitará a conexão entre esses estabelecimentos e seus clientes, oferecendo ferramentas completas de agendamento online, gestão de equipe, processamento de pagamentos e insights de negócios.

### **1.2. Objetivos do Produto**

* **Para os Estabelecimentos:**  
  * Simplificar e automatizar o processo de agendamento, reduzindo o tempo gasto em tarefas manuais.  
  * Otimizar a gestão de horários de colaboradores e a utilização da capacidade do estabelecimento.  
  * Aumentar a receita através de ferramentas de marketing, combos e planos de assinatura de serviços.  
  * Fornecer dados e relatórios para tomada de decisão estratégica.  
  * Melhorar a experiência do cliente e fomentar a fidelização.  
* **Para os Usuários Finais (Clientes):**  
  * Oferecer uma forma conveniente e fácil de descobrir, agendar e pagar por serviços pessoais.  
  * Proporcionar transparência nos horários disponíveis, preços e informações dos estabelecimentos.  
  * Gerenciar seus agendamentos de forma autônoma (cancelar, reagendar conforme políticas).  
* **Para a Plataforma ServiceTech:**  
  * Tornar-se a solução líder de mercado para gestão de estabelecimentos de serviços pessoais.  
  * Construir um modelo de negócio SaaS sustentável e escalável.  
  * Inovar continuamente, agregando valor aos seus usuários.

### **1.3. Público-Alvo**

* **Estabelecimentos de Serviços Pessoais:**  
  * Barbearias  
  * Salões de beleza  
  * Clínicas de estética  
  * Estúdios de tatuagem  
  * Spas  
  * Outros negócios que operam com base em agendamento de serviços pessoais.  
* **Usuários Finais:** Pessoas que buscam e consomem serviços oferecidos pelos estabelecimentos acima.

## **2\. Proposta de Valor**

### **2.1. Para Estabelecimentos (Proprietários e Colaboradores)**

O ServiceTech capacita os estabelecimentos a modernizar suas operações, aumentar a eficiência e o alcance de clientes. Com funcionalidades como agendamento online 24/7, gestão de equipe simplificada, opções de pagamento flexíveis (online e local), e ferramentas de marketing e fidelização, os negócios podem focar no que fazem de melhor: prestar serviços de qualidade. Os relatórios detalhados oferecem uma visão clara do desempenho, auxiliando no crescimento sustentável.

### **2.2. Para Usuários Finais (Clientes)**

O ServiceTech oferece conveniência e controle. Os clientes podem encontrar estabelecimentos, verificar a disponibilidade de horários e serviços, ler descrições, agendar e pagar online a qualquer hora e de qualquer lugar. Notificações e lembretes automáticos ajudam a não perder compromissos, e a gestão de agendamentos próprios (como cancelamentos dentro das políticas) empodera o usuário.

## **3\. Papéis de Usuário e Permissões**

A plataforma operará com os seguintes papéis principais:

* **Administrador da Plataforma:**  
  * **Responsabilidades:** Gerenciamento global do ServiceTech.  
  * **Acessos:** Controle total sobre configurações do sistema, gestão de planos de assinatura SaaS, supervisão de empresas cadastradas, acesso a relatórios agregados da plataforma (anonimizados). Não interfere diretamente na gestão interna das empresas clientes.  
* **Proprietário do Estabelecimento:**  
  * **Responsabilidades:** Representante legal e gestor da empresa assinante do ServiceTech. Responsável pelo pagamento da assinatura do plano.  
  * **Acessos:** Configuração completa do perfil da empresa (serviços, horários, fotos, descrição), gestão de colaboradores (convidar, associar, definir permissões granulares), definição de regras de negócio (políticas de cancelamento, comissionamento), acesso a relatórios detalhados da sua unidade, gestão de marketing e assinaturas (conforme plano). Pode optar por atuar também como colaborador ativo em sua empresa, gerenciando sua própria agenda.  
* **Colaborador do Estabelecimento:**  
  * **Responsabilidades:** Prestador de serviços associado a uma empresa pelo Proprietário.  
  * **Acessos:** Gerenciamento da própria agenda de horários (visualizar, bloquear horários, definir indisponibilidades recorrentes), visualização de seus agendamentos, marcação de pagamentos realizados no local. Acesso a outras funcionalidades pode ser concedido pelo Proprietário. Deve aceitar o convite do Proprietário em até 24 horas.  
* **Usuário Final (Cliente):**  
  * **Responsabilidades:** Consumidor dos serviços.  
  * **Acessos:** Busca por estabelecimentos, visualização de perfis de empresas, serviços e horários, realização de agendamentos, escolha de forma de pagamento (online ou local), gerenciamento de seus próprios agendamentos (cancelamento conforme regras). Papel inicial de qualquer pessoa que se cadastra na plataforma.

**Estrutura Técnica de Papéis:** A transição e atribuição de papéis (ex: Usuário → Proprietário ao assinar um plano) será gerenciada por um sistema robusto de controle de acesso baseado em papéis (RBAC), com papéis globais (Administrador, Usuário) e papéis vinculados a empresas (Proprietário, Colaborador).

## **4\. Funcionalidades Detalhadas**

Para aumentar a clareza e facilitar o desenvolvimento e testes, as funcionalidades principais serão descritas usando o formato de 'User Stories' (Histórias de Usuário), seguido de 'Critérios de Aceite' específicos sempre que aplicável.  
Exemplo geral:  
História de Usuário: 'Como um \[tipo de usuário\], quero \[realizar uma ação\] para que \[alcance um benefício\].'  
Critérios de Aceite:

* Condição 1 que deve ser atendida.  
* Condição 2 que deve ser atendida.  
  Isso ajuda a manter o foco no valor para o usuário e torna os requisitos mais testáveis.

### **4.1. Módulo de Agendamento**

Este é o coração da plataforma, permitindo uma experiência fluida tanto para o cliente quanto para o estabelecimento.

* **Descoberta de Estabelecimentos (Marketplace View \- para Usuário):**  
  * Usuários poderão buscar estabelecimentos por nome, tipo de serviço ou localização (cidade/bairro).  
  * Resultados exibirão informações essenciais: nome, endereço, e possivelmente uma avaliação média.  
* **Página Dedicada do Estabelecimento:**  
  * Cada empresa terá uma URL única e personalizável (ex: servicetech.com/nome-da-empresa).  
  * **Conteúdo:** Logo, imagem de capa, galeria de fotos (portfólio), descrição do negócio, endereço completo (com mapa), lista de serviços detalhada (com nome, descrição, duração e preço) e horários de funcionamento.  
  * **Operação:** O Proprietário gerencia todo o conteúdo desta página.  
* **Fluxo de Agendamento (Perspectiva do Usuário):**  
  1. **Seleção:** Usuário acessa a página do estabelecimento (diretamente ou via busca).  
  2. **Serviço(s):** Escolhe um ou mais serviços desejados. O sistema deve permitir a seleção de múltiplos serviços para um mesmo agendamento (potencialmente formando um combo).  
  3. **Colaborador (Opcional):** Usuário pode optar por um colaborador específico ou deixar que o sistema atribua (via round-robin entre os disponíveis e aptos).  
  4. **Horários:** Sistema exibe um calendário com os horários disponíveis, considerando a duração dos serviços selecionados, a disponibilidade do estabelecimento e do colaborador (se escolhido).  
  5. **Seleção de Horário:** Usuário escolhe data e hora.  
  6. **Observações:** Campo opcional para o usuário adicionar notas (ex: "cabelo com tinta vermelha").  
  7. **Pagamento:** Usuário escolhe a forma de pagamento (Online via Stripe ou Pagamento no Local).  
  8. **Confirmação:** Usuário revisa os detalhes e confirma a solicitação de agendamento. Recebe uma notificação de "solicitação enviada".  
* **Fluxo de Gerenciamento de Agendamentos (Perspectiva do Estabelecimento \- Proprietário/Colaborador):**  
  1. **Notificação:** Se o cliente especificou um colaborador, este recebe a notificação principal. Se o agendamento foi atribuído via round-robin, a notificação prioritária será enviada ao Colaborador designado pelo sistema. O Proprietário também pode receber notificações (configurável) para todas as solicitações ou apenas para as não atribuídas/pendentes após X tempo. As notificações devem ser enviadas via e-mail, push e in-app.  
  2. **Visualização:** Acesso aos detalhes do agendamento: cliente, serviço(s), horário, observações, forma de pagamento.  
  3. **Ação:** Opções de **Confirmar** ou **Recusar** o agendamento.  
     * Se **Confirmado:** Cliente é notificado. Se o pagamento foi online, ele é capturado (ou já foi autorizado).  
     * Se **Recusado:** Cliente é notificado. Se houve pagamento online, o reembolso é processado automaticamente.  
  4. **Prazo para Ação:** Se o estabelecimento não agir (confirmar/recusar) em **24 horas** após a solicitação do usuário, o agendamento é **automaticamente Recusado** pelo sistema. O cliente é notificado e, se aplicável, reembolsado.  
  5. **Lembrete para Confirmação:** O sistema enviará um lembrete ao estabelecimento caso um agendamento esteja pendente de confirmação próximo ao prazo de 24 horas.  
* **Gestão de Horários pelo Estabelecimento:**  
  * **Horários da Empresa:** Proprietário define os dias e horários de funcionamento padrão da empresa. Pode bloquear datas/horários específicos para toda a empresa (ex: feriados, reformas).  
  * **Horários Individuais dos Colaboradores:** A gestão é granular por colaborador.  
    * Proprietário (para si, se ativo, e para outros) e o próprio Colaborador podem definir seus horários de trabalho, pausas (ex: almoço), folgas e indisponibilidades recorrentes (ex: toda segunda de manhã, ou diariamente das 12h-13h).  
    * **Bloqueio de Horários:** Ao tentar bloquear um horário onde já existe um agendamento confirmado, o sistema alertará. Se o bloqueio prosseguir, o agendamento existente será cancelado (seguindo a regra de cancelamento pela empresa, com notificação e reembolso integral ao cliente).  
  * **Alocação Inteligente:** O sistema considera a duração dos serviços e os horários de trabalho/bloqueios para exibir apenas horários realmente disponíveis.  
  * **Alocação Round-Robin:** Se o cliente não especificar um colaborador, o sistema distribui o agendamento de forma equitativa (round-robin) entre os colaboradores qualificados e disponíveis no horário escolhido (incluindo o Proprietário, se ativo como prestador).  
* **Cancelamentos e Reembolsos:**  
  * **Pelo Usuário:**  
    * **Com \> 24h de antecedência E agendamento NÃO confirmado pelo estabelecimento:** Reembolso de 100% (se pago online).  
    * **Com \< 24h de antecedência E agendamento JÁ confirmado pelo estabelecimento:** Aplica-se a política de reembolso definida pelo Proprietário (0% a 100%).  
  * **Pelo Estabelecimento (Proprietário/Colaborador):** Se um agendamento confirmado é cancelado pelo estabelecimento, o cliente é notificado e recebe reembolso de 100% (se pago online).  
  * O sistema processará reembolsos automaticamente via Stripe quando aplicável.  
* **Notificações (Multicanal: E-mail, SMS, Push In-App):**  
  * **Para Usuários:** Confirmação de solicitação, confirmação/recusa de agendamento, lembretes (configuráveis), cancelamentos.  
  * **Para Estabelecimentos:** Nova solicitação de agendamento, lembrete para confirmação, cancelamento pelo usuário.  
  * **Configuração de Notificações:** Proprietário pode definir quais tipos de notificações ele e seus colaboradores recebem (e-mail, push, ambos \- todas ativas por padrão). Opções avançadas de configuração para Plano Premium.

### **4.2. Módulo de Pagamento**

Integrado e flexível para atender diferentes preferências.

* **Formas de Pagamento Aceitas:**  
  * Online: Pix, Cartão de Crédito, Cartão de Débito (via Stripe).  
  * No Local: Dinheiro, Cartão (processado pela maquininha do estabelecimento).  
* **Operação de Pagamento Online:**  
  * Integrado ao Stripe para processamento seguro.  
  * **Tratamento de Falhas no Pagamento Online:**  
    * Se houver falha na comunicação com o Stripe ou recusa do pagamento (ex: cartão sem fundos, dados inválidos):  
      * O sistema deve exibir uma mensagem clara e amigável ao usuário, informando a falha sem expor detalhes técnicos.  
      * O agendamento não deve ser confirmado/completado até o pagamento ser resolvido.  
      * O usuário deve ser orientado a tentar novamente com os mesmos dados, usar outro método de pagamento online, ou selecionar 'Pagamento no Local' (se aplicável e disponível).  
      * A plataforma deve registrar o erro internamente para análise e possível contato com o usuário, se necessário.  
  * O pagamento é solicitado no momento da confirmação do agendamento pelo usuário.  
* **Operação de Pagamento no Local:**  
  * Cliente escolhe esta opção durante o agendamento.  
  * O agendamento é confirmado (após aprovação do estabelecimento) e fica com status "Pagamento Pendente".  
  * Proprietário ou Colaborador marcam o agendamento como "Pago" na plataforma após o recebimento no local.  
* **Processamento de Reembolsos:**  
  * Automatizado via Stripe, conforme as regras de cancelamento.

### **4.3. Módulo de Gestão da Empresa**

Ferramentas para o Proprietário administrar seu negócio na plataforma.

**Gestão de Serviços:**

*   
  * Proprietário pode cadastrar, editar, ativar/desativar e excluir serviços.  
  * **Detalhes do Serviço:** Nome, descrição completa, duração (para cálculo de agenda), preço.  
  * **Limites:**  
    * Plano Essencial: Até **6 serviços** ativos.  
    * Plano Premium: Até **12 serviços** ativos.  
* **Gestão de Colaboradores:**  
  * **Adição:** Proprietário convida usuários já cadastrados na plataforma (ou que se cadastrarão) para serem Colaboradores de sua empresa.  
    * **Convite por Chave:** Geração de uma chave de convite única (expira em 24h) que o Proprietário compartilha. O usuário insere a chave na plataforma para aceitar. Se não cadastrado, é direcionado ao cadastro primeiro.  
    * Colaborador deve aceitar o convite em 24h.  
    * Um colaborador só pode estar associado a uma empresa por vez.  
  * **Visualização de Convites:** Proprietário pode ver status dos convites (pendente, aceito, expirado).  
  * **Associação de Serviços:** Definir quais serviços cada colaborador está apto a realizar.  
  * **Proprietário como Colaborador:** Opção para o Proprietário habilitar/desabilitar seu próprio perfil como prestador de serviços ativo (não consome um slot de colaborador extra do plano).  
  * **Limites de Colaboradores Extras (além do Proprietário):**  
    * Plano Essencial: Até **2 colaboradores extras**.  
    * Plano Premium: Até **6 colaboradores extras**.  
* **Configuração de Regras de Negócio da Empresa:**  
  * **Política de Cancelamento:** Proprietário define a porcentagem de reembolso (0-100%) para cancelamentos feitos pelo cliente com menos de 24h de antecedência para agendamentos já confirmados.  
  * **Modelo de Negócio Interno:**  
    * **Parceria:**  
      * Opção de controle de comissão (ativar/desativar).  
      * Se ativo, definir % fixa de comissão por serviço.  
      * Campo para "Custos Operacionais" por colaborador (valor fixo mensal ou semanal definido pelo Proprietário).  
    * **Funcionários:**  
      * Opção de controle de comissão (ativar/desativar).  
      * Se ativo, definir % fixa de comissão por serviço.  
      * Não possui campo de custos operacionais.

### **4.4. Módulo de Relatórios**

Visão clara do desempenho do negócio.

* **Relatórios Básicos (Plano Essencial):**  
  * Total de agendamentos (por Dia, Semana, Mês, Período customizado).  
  * Faturamento bruto (por Dia, Semana, Mês, Período customizado).  
* **Relatórios Completos (Plano Premium):**  
  * Faturamento Total (por período).  
  * Faturamento por Colaborador (considerando comissões e custos operacionais, se modelo "Parceria").  
  * Faturamento por Serviço.  
  * Faturamento por Forma de Pagamento (Pix, Débito, Crédito, Local).  
  * Relatório de Agendamentos (detalhando status: Confirmado, Pendente, Recusado/Cancelado).

### **4.5. Módulo de Marketing Básico (Exclusivo Plano Premium)**

Ferramentas para engajamento e promoção.

* **Comunicação com Clientes:**  
  * Envio segmentado de e-mail/SMS (ex: para clientes que agendaram X vezes, clientes inativos há Y tempo).  
* **Promoções:**  
  * Criação e gestão de cupons de desconto (valor fixo ou percentual).  
  * Notificação de promoções para a base de clientes.

### **4.6. Módulo de Assinaturas e Combos**

Estratégias para aumentar a receita recorrente e o ticket médio.

* **Assinatura de Serviços Mensal (Exclusivo Plano Premium):**  
  * **Configuração pela Empresa:** Proprietário define quais serviços podem ser oferecidos em planos de assinatura mensal.  
    * Define se o uso é ilimitado ou limitado (ex: 4 cortes de cabelo por mês).  
    * Define o preço mensal da assinatura.  
  * **Experiência do Cliente:**  
    * Cliente assina o plano (pagamento recorrente mensal via cartão de crédito \- Stripe).  
    * **Contador de Usos:** Cliente visualiza quantos usos restantes possui no ciclo atual (para planos limitados).  
    * **Agendamento:** Ao agendar serviços cobertos pela assinatura, o valor é R$0,00. Se incluir serviços não cobertos, paga apenas por estes.  
  * **Gestão da Assinatura:**  
    * **Falha na Renovação:** Cliente é notificado para atualizar dados de pagamento. Benefícios suspensos até regularização.  
    * **Cancelamento pelo Cliente:**  
      * Pode cancelar a qualquer momento.  
      * **Até 7 dias da assinatura E sem uso:** Reembolso 100%, cancelamento imediato.  
      * **Até 7 dias da assinatura E com uso:** Reembolso da diferença (valor da assinatura \- valor dos serviços usados sem desconto). Cancelamento imediato.  
      * **Após 7 dias:** Sem reembolso. Benefícios válidos até o fim do ciclo pago.  
  * **Gestão pela Empresa:** Proprietário ativa/desativa a oferta, configura planos, visualiza assinantes e status.  
* **Combo de Serviços (Plano Essencial e Premium):**  
  * **Configuração pela Empresa:** Proprietário cria combos de serviços (ex: Corte \+ Barba).  
    * Define os serviços que compõem o combo.  
    * Define o desconto (valor fixo ou % sobre o total dos serviços individuais).  
  * **Experiência do Cliente:**  
    * Ao selecionar serviços que formam um combo ativo, o sistema aplica o desconto automaticamente no resumo do agendamento e no pagamento.  
    * Os serviços do combo são agendados para o mesmo horário/sequência.  
  * **Prioridade Assinatura vs. Combo (Plano Premium):** Se um cliente assinante selecionar serviços, onde alguns estão na assinatura e outros formam um combo (não cobertos pela assinatura), o desconto do combo aplica-se apenas aos serviços fora da assinatura. Os serviços da assinatura permanecem com custo zero.

### **4.7. Fluxo de Adesão de Empresas (Onboarding SaaS)**

Processo para novos estabelecimentos se juntarem à plataforma.

1. **Página de Planos e Benefícios:** Uma página pública (SSG) detalhando os benefícios do ServiceTech, os planos Essencial e Premium, funcionalidades e preços.  
2. **Escolha do Plano e Cadastro Inicial:** Interessado escolhe o plano e realiza um cadastro básico (e-mail, senha).  
3. **Pagamento da Assinatura SaaS:** Fluxo de pagamento via Stripe para a primeira mensalidade do plano escolhido.  
4. **Transformação do Papel:** Após pagamento confirmado, o usuário é elevado ao papel de "Proprietário" e uma entidade "empresa" é criada e associada a ele.  
5. **Wizard de Configuração Inicial da Empresa (Setup Guiado):**  
   * Um fluxo passo-a-passo para o Proprietário configurar os dados essenciais da sua empresa:  
     * Nome da empresa.  
     * Endereço completo (para busca e geolocalização).  
     * Descrição, logo, fotos.  
     * Cadastro do(s) primeiro(s) serviço(s) (nome, descrição, valor, duração).  
     * Definição dos horários base de funcionamento.  
     * Configuração dos horários de trabalho para o primeiro colaborador (pode ser o próprio Proprietário).  
   * A empresa só se torna "listável" no marketplace (se houver) e apta a receber agendamentos após o preenchimento dos dados mínimos obrigatórios.

## **5\. Planos de Assinatura SaaS**

| Funcionalidade | Plano Essencial | Plano Premium |  
| Módulo de Agendamento | Completo | Completo |  
| Módulo de Pagamento | Completo | Completo |  
| Gestão de Serviços (Limite) | Até 6 serviços | Até 12 serviços |  
| Gestão de Colaboradores Extras | Até 2 (além do Proprietário) | Até 6 (além do Proprietário) |  
| Proprietário como Colaborador | Sim (não conta no limite) | Sim (não conta no limite) |  
| Config. Regras de Negócio | Sim | Sim |  
| Modelos de Negócio Internos | Sim | Sim |  
| Relatórios | Módulo Básico | Módulo Completo |  
| Combo de Serviços | Sim | Sim |  
| Módulo de Marketing Básico | Não | Sim |  
| Assinatura de Serviços Mensal | Não | Sim |  
| Config. Avançadas de Notificação | Não | Sim |  
| Suporte | E-mail | E-mail Prioritário |

## **6\. Requisitos Não Funcionais Chave**

* **Performance:** Resposta rápida da interface (\<2s para interações críticas), carregamento eficiente de listas e calendários.  
* **Escalabilidade:** Arquitetura capaz de suportar crescimento de usuários e dados sem degradação.  
* **Segurança:** Proteção de dados (LGPD), senhas criptografadas, prevenção contra ataques comuns (XSS, SQL Injection). Pagamentos seguros via Stripe (aderência PCI DSS gerenciada pelo gateway).  
* **Disponibilidade:** Uptime de 99.9% ou superior. Manutenções planejadas comunicadas com antecedência.  
* **Usabilidade:** Interface intuitiva, clara, com fluxos de usuário lógicos e acessíveis para diferentes níveis de habilidade tecnológica. Design responsivo para desktops e dispositivos móveis.  
* **Compatibilidade:** Suporte aos navegadores modernos (Chrome, Firefox, Safari, Edge) em suas versões estáveis mais recentes.  
* **Manutenibilidade:** Código bem documentado, modular, seguindo as melhores práticas da stack (Next.js, Supabase). Migrations de banco de dados versionadas e testadas.  
* **Conformidade Regulatória:** Aderência à LGPD e outras normas aplicáveis.

## **7\. Métricas de Sucesso (KPIs)**

* **Aquisição:**  
  * Número de novos estabelecimentos (Proprietários) cadastrados por mês: Meta de X novos estabelecimentos/mês no primeiro semestre após o lançamento.  
  * Taxa de conversão da página de planos para assinatura: Meta de Y% (medida por \[ferramenta de analytics\]).  
* **Engajamento:**  
  * Número de agendamentos realizados por dia/semana/mês.  
  * Número de usuários finais ativos.  
  * Número de estabelecimentos ativos (que recebem agendamentos).  
  * Taxa de adoção das funcionalidades Premium: Pelo menos Z% dos assinantes Premium utilizando ativamente o Módulo de Marketing ou de Assinaturas após 3 meses da ativação do plano.  
* **Retenção:**  
  * Taxa de Churn de estabelecimentos (cancelamento de assinatura).  
  * Taxa de retenção de usuários finais.  
* **Receita:**  
  * MRR (Receita Mensal Recorrente) dos planos SaaS.  
  * ARPU (Receita Média Por Usuário \- Estabelecimento).  
* **Satisfação:**  
  * NPS (Net Promoter Score) de Proprietários e Usuários Finais (a ser implementado via pesquisas).  
  * Avaliações e feedback dos usuários.

## **8\. Considerações Futuras (Pós-MVP/V1)**

* Integração com calendários externos (Google Calendar, Outlook Calendar) para colaboradores.  
* Aplicativo móvel dedicado (iOS e Android) para Proprietários, Colaboradores e Usuários Finais.  
* Módulo de gestão de estoque de produtos (para salões que vendem produtos).  
* Funcionalidades de CRM mais avançadas.  
* Recursos de IA para otimização de agenda, sugestões personalizadas para clientes, ou análise preditiva de negócios.  
* Programa de fidelidade mais robusto e personalizável.  
* Integração com ferramentas de marketing digital (ex: Meta Ads, Google Ads).  
* Marketplace de serviços mais elaborado com filtros avançados e avaliações de usuários.

## **9\. Stack Tecnológica (Conforme Levantamento)**

* **Frontend & Backend (API):** Next.js (App Router)  
  * Renderização: SSG, ISR, SSR conforme a necessidade da página/recurso.  
  * API Routes para webhooks (Stripe) e operações CRUD.  
  * Edge Functions para endpoints de baixa latência.  
* **Banco de Dados & Auth:** Supabase  
  * PostgreSQL gerenciado.  
  * Autenticação, Storage, Realtime.  
  * Multi-tenant via tenant\_id \+ RLS (Row Level Security).  
* **CI/CD:** GitHub Actions.

## **10\. Arquitetura de Páginas e Escopo Visual (MVP)**

Esta seção descreve a estrutura de páginas principais da plataforma ServiceTech para a primeira versão (MVP), organizada por tipo de acesso e papel do usuário.

### **10.1. Páginas Públicas (Acessíveis sem Login)**

* **Landing Page (/)**:  
  * **Escopo:** Página inicial principal da plataforma. Apresenta o ServiceTech, seus benefícios para estabelecimentos e usuários finais, CTAs (Call to Actions) para "Buscar Estabelecimentos" e "Cadastre Seu Negócio".  
  * **Elementos Chave:** Hero section atraente, breve explicação da plataforma, depoimentos (mockups iniciais), links para planos e cadastro.  
* **Página de Planos para Estabelecimentos (/planos)**:  
  * **Escopo:** Detalha os planos de assinatura SaaS (Essencial e Premium), suas funcionalidades, preços e um CTA para iniciar o processo de assinatura/cadastro de um novo estabelecimento.  
  * **Elementos Chave:** Tabela comparativa de planos, FAQ sobre os planos, CTA para assinatura.  
* **Página de Busca de Estabelecimentos (/buscar ou /explorar)**:  
  * **Escopo:** Permite que usuários não logados (e logados) busquem por estabelecimentos.  
  * **Elementos Chave:** Barra de busca (por nome, serviço, localização), filtros básicos (tipo de serviço, cidade), lista de resultados com cards dos estabelecimentos (nome, foto, endereço, avaliação média).  
* **Página Pública do Estabelecimento (/estabelecimento/{slug-da-empresa})**:  
  * **Escopo:** Página de perfil de um estabelecimento específico, acessível publicamente.  
  * **Elementos Chave:** Informações do estabelecimento (conforme seção 4.1), lista de serviços com preços e durações, CTA para "Ver Horários/Agendar".  
* **Páginas de Autenticação:**  
  * **Login (/login)**: Formulário para usuários existentes acessarem suas contas. Link para "Esqueci minha senha" e "Cadastre-se".  
  * **Cadastro (/cadastro)**: Formulário para novos usuários (clientes ou futuros proprietários) criarem uma conta.  
  * **Recuperação de Senha (/recuperar-senha)**: Processo para redefinição de senha.  
* **Página de Confirmação de Convite de Colaborador (/convite/{chave-convite})**:  
  * **Escopo:** Página para onde um usuário é direcionado ao clicar em um link de convite para ser colaborador. Permite inserir a chave (se não veio na URL) e aceitar o convite.  
  * **Elementos Chave:** Informação sobre qual empresa o está convidando, botão para aceitar. Se o usuário não estiver logado, será solicitado login/cadastro antes de prosseguir.

### **10.2. Área do Usuário Final (Cliente \- Requer Login)**

* **Dashboard do Cliente (/cliente/dashboard ou /meus-agendamentos)**:  
  * **Escopo:** Visão geral dos próximos agendamentos e agendamentos passados.  
  * **Elementos Chave:** Lista de agendamentos futuros (com opção de ver detalhes, cancelar/reagendar conforme regras), histórico de agendamentos.  
* **Detalhes do Agendamento (/cliente/agendamento/{id-agendamento})**:  
  * **Escopo:** Exibe todas as informações de um agendamento específico.  
  * **Elementos Chave:** Nome do estabelecimento, serviço, colaborador, data/hora, status, valor, opção de cancelamento (se aplicável).  
* **Perfil do Cliente (/cliente/perfil)**:  
  * **Escopo:** Permite ao cliente visualizar e editar suas informações pessoais e configurações.  
  * **Elementos Chave:** Dados cadastrais (nome, e-mail, telefone), gerenciamento de senha, (futuramente) preferências de notificação, gestão de assinaturas de serviços (se aplicável).  
* **Fluxo de Agendamento (dentro da página do estabelecimento, após login)**:  
  * Semelhante à página pública do estabelecimento, mas com a identidade do usuário já reconhecida, facilitando o preenchimento de dados e acesso a histórico/preferências.

### **10.3. Área do Proprietário do Estabelecimento (Requer Login e Papel Proprietário)**

* **Dashboard do Proprietário (/proprietario/dashboard)**:  
  * **Escopo:** Visão geral principal para o proprietário.  
  * **Elementos Chave:** Resumo de agendamentos do dia/semana, solicitações pendentes de confirmação, atalhos para seções importantes (agenda, serviços, colaboradores, relatórios), alertas importantes.  
* **Gestão de Agenda da Empresa (/proprietario/agenda)**:  
  * **Escopo:** Visualização e gerenciamento da agenda completa da empresa, incluindo todos os colaboradores.  
  * **Elementos Chave:** Calendário com visão diária/semanal/mensal, filtro por colaborador, visualização de horários ocupados/livres/bloqueados, opção de adicionar novos agendamentos manualmente, confirmar/recusar solicitações.  
* **Gestão de Serviços (/proprietario/servicos)**:  
  * **Escopo:** CRUD (Criar, Ler, Atualizar, Deletar) dos serviços oferecidos.  
  * **Elementos Chave:** Lista de serviços cadastrados, formulário para adicionar/editar serviço (nome, descrição, duração, preço).  
* **Gestão de Colaboradores (/proprietario/colaboradores ou /proprietario/equipe)**:  
  * **Escopo:** Gerenciamento da equipe de colaboradores.  
  * **Elementos Chave:** Lista de colaboradores associados, opção de convidar novo colaborador (gerar chave), definir quais serviços cada colaborador realiza, gerenciar horários de trabalho individuais dos colaboradores, ativar/desativar seu próprio perfil como colaborador.  
* **Configurações da Empresa (/proprietario/configuracoes)**:  
  * **Escopo:** Configurações gerais do perfil público da empresa e regras de negócio.  
  * **Sub-seções/Abas:**  
    * **Perfil da Empresa:** Nome, endereço, descrição, logo, fotos de capa/portfólio, horários de funcionamento da empresa.  
    * **Regras de Negócio:** Política de cancelamento/reembolso, modelo de negócio interno (parceria/funcionários) com configurações de comissão/custos.  
    * **Pagamentos:** Configuração da integração com Stripe (se necessário algum input do proprietário), visualização de repasses.  
    * **Notificações:** Configuração de quais notificações o proprietário e colaboradores recebem.  
* **Relatórios (/proprietario/relatorios)**:  
  * **Escopo:** Acesso aos relatórios financeiros e de agendamento (conforme plano).  
  * **Elementos Chave:** Filtros por período, tipo de relatório (faturamento total, por colaborador, etc.), visualização de gráficos e tabelas.  
* **Módulo de Marketing (Plano Premium) (/proprietario/marketing)**:  
  * **Escopo:** Ferramentas de marketing.  
  * **Sub-seções/Abas:**  
    * **Campanhas de E-mail/SMS:** Criação e envio.  
    * **Cupons de Desconto:** Criação e gerenciamento.  
* **Módulo de Assinaturas (Plano Premium) (/proprietario/assinaturas)**:  
  * **Escopo:** Configuração e gestão de planos de assinatura de serviços.  
  * **Elementos Chave:** Criação de planos de assinatura (serviços incluídos, limite, preço), lista de clientes assinantes.  
* **Módulo de Combos (/proprietario/combos)**:  
  * **Escopo:** Criação e gestão de combos de serviços.  
  * **Elementos Chave:** Formulário para criar combo (selecionar serviços, definir desconto), lista de combos ativos.  
* **Wizard de Onboarding (Fluxo inicial após assinatura SaaS)**:  
  * **Escopo:** Sequência de páginas guiadas para configuração inicial da empresa (conforme seção 4.7, item 5).

### **10.4. Área do Colaborador (Requer Login e Papel Colaborador)**

* **Dashboard do Colaborador / Minha Agenda (/colaborador/agenda)**:  
  * **Escopo:** Visão principal da agenda individual do colaborador.  
  * **Elementos Chave:** Calendário com seus agendamentos (dia/semana), opção de visualizar detalhes do agendamento, marcar como pago (para pagamentos no local), bloquear seus próprios horários/dias, definir indisponibilidades recorrentes.  
* **Meus Agendamentos (/colaborador/meus-agendamentos)**:  
  * **Escopo:** Lista detalhada dos seus agendamentos futuros e passados.  
  * **Elementos Chave:** Semelhante ao do cliente, mas focado nos seus atendimentos.  
* **Meu Perfil (/colaborador/perfil)**:  
  * **Escopo:** Visualização de seus dados e, possivelmente, configurações limitadas.  
  * **Elementos Chave:** Dados pessoais (não editáveis diretamente, geralmente gerenciados pelo Proprietário ou Admin da plataforma), talvez preferências de notificação (se permitido pelo Proprietário).

### **10.5. Área do Administrador da Plataforma (Requer Login e Papel Administrador)**

* **Dashboard do Admin (/admin/dashboard)**:  
  * **Escopo:** Visão geral da saúde da plataforma.  
  * **Elementos Chave:** Número total de empresas, usuários, agendamentos recentes, MRR, alertas do sistema.  
* **Gestão de Empresas (/admin/empresas)**:  
  * **Escopo:** Listar, visualizar detalhes, e gerenciar (ativar, desativar, modificar plano) as empresas cadastradas na plataforma.  
* **Gestão de Usuários (/admin/usuarios)**:  
  * **Escopo:** Listar e gerenciar usuários da plataforma (visualizar detalhes, alterar papéis globais se necessário, bloquear usuário).  
* **Gestão de Planos SaaS (/admin/planos-saas)**:  
  * **Escopo:** Configurar os detalhes dos planos de assinatura oferecidos pela ServiceTech (Essencial, Premium).  
* **Configurações Globais da Plataforma (/admin/configuracoes-sistema)**:  
  * **Escopo:** Configurações gerais do sistema ServiceTech.  
* **Relatórios da Plataforma (/admin/relatorios-plataforma)**:  
  * **Escopo:** Relatórios agregados sobre o uso da plataforma (anonimizados).

Esta estrutura de páginas visa cobrir as funcionalidades essenciais do MVP, proporcionando fluxos de usuário lógicos e separados por responsabilidades. A nomenclatura exata das rotas e a organização detalhada dos menus de navegação seriam definidas durante o design de UX/UI.

Este documento serve como base para o desenvolvimento do ServiceTech. Ele deverá ser revisado e atualizado conforme o produto evolui e novas informações e requisitos surgem.