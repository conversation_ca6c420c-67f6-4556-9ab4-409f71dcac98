- Levantamento de Requisitos - Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais
    
    ```markdown
    # Levantamento de Requisitos - Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais
    
    ## 1. Introdução
    
    Este documento apresenta os requisitos iniciais para uma plataforma SaaS de agendamento e gestão, voltada principalmente para empresas do ramo de barbearias, salões de beleza, clínicas de estética e outros estabelecimentos correlacionados de serviços pessoais. A solução oferecerá agendamento online, gestão de usuários e funcionalidades essenciais, com planos de assinatura flexíveis.
    
    ## 2. Escopo Inicial do Projeto
    
    A primeira versão da plataforma focará nas funcionalidades essenciais para gestão de agendamentos, serviços e pagamentos online para empresas e seus usuários. Funcionalidades avançadas, como análise de dados e recursos de IA, serão avaliadas em etapas futuras. O produto será ofertado em dois planos de assinatura: Essencial e Premium.
    
    ## 3. Papéis de Usuário
    
    A plataforma define níveis de acesso por papéis de usuário. Todo usuário inicia como "Usuário" após cadastro; demais papéis são atribuídos conforme uso e permissões.
    
    - **Administrador:**
        - Acesso total a todas as funcionalidades da plataforma, incluindo gestão de empresas, planos de assinatura do SaaS, relatórios agregados (sem dados sensíveis de usuários finais) e configurações gerais do sistema.
    - **Proprietário:** 
        - Representa a empresa assinante do SaaS e é responsável pelo pagamento da assinatura. Gerencia a unidade da empresa, incluindo: gestão de usuários existentes na plataforma, atribuindo a eles o papel de "Colaborador" associado à sua empresa (respeitando os limites do plano de assinatura), definição de serviços e horários, configuração de regras de negócio (cancelamento/reembolso, modelo de comissão), acesso a relatórios da sua unidade (conforme plano), e permissão granular para o papel "Colaborador". A transição do papel "Usuário" para "Proprietário" ocorre através da adesão a um plano de assinatura da plataforma. Neste momento de transição, uma entidade 'empresa' é criada no sistema e associada a este usuário, inicialmente sem dados detalhados, que serão preenchidos no setup inicial (conforme seção 5.7). **O Proprietário pode, opcionalmente, também atuar como um colaborador ativo em sua própria empresa. Haverá uma configuração na gestão da empresa onde ele pode habilitar/desabilitar seu perfil como prestador de serviços. Se habilitado, ele poderá gerenciar sua própria agenda de horários (conforme funcionalidades do papel Colaborador) e aparecerá na lista de colaboradores selecionáveis pelos usuários para agendamento, não consumindo um slot de "colaborador extra" do plano.**
    - **Colaborador:** 
        - Associado a uma empresa específica pelo Proprietário. Poderá visualizar e gerenciar sua própria agenda de horários, marcar agendamentos como pagos (para pagamentos no local), e potencialmente ter acesso limitado a relatórios ou outras funcionalidades conforme permissão do Proprietário. Um usuário com o papel Usuário que é convidado por um Proprietário para se tornar um Colaborador deve **confirmar a aceitação deste papel em até 24 horas** após a atribuição, caso contrário, a atribuição expira e o usuário permanece como Usuário.
    - **Usuário:** 
        - Usuário final que busca empresas, visualiza serviços e horários, realiza agendamentos e efetua pagamentos online. Este é o papel inicial de todo usuário cadastrado.
    
    ### 3.1. Estrutura Técnica de Papéis e Permissões
    
    A transição de papéis (ex: **Usuário → Proprietário**) será via uma tabela **user_roles**:
    
    1. **Papéis Globais:**
        - **Administrador**: Acesso total ao sistema (não vinculado a uma empresa específica).
        - **Usuário**: Papel padrão para novos usuários.
    2. **Papéis por empresa:**
        - **Proprietário**: Vinculado a uma **`empresa_id`**.
        - **Colaborador**: Vinculado a uma **`empresa_id`**.
    
    ### **Atualizações no Banco de Dados**
    
    **Novas Tabelas:**
    
    - **`roles`:** Define os papéis do sistema (**Administrador**, **Proprietário**, **Colaborador**, **Usuário**).
    - **`user_roles`:** Associa usuários a papéis e empresas (controle de acesso granular).
    
    **Políticas de Segurança (RLS):**
    
    - **`empresas`:** Acesso restrito ao **Proprietário** da empresa correspondente.
    - **`agendamentos`:** Colaboradores só visualizam agendamentos de sua empresa.
    - **`user_roles`:** Apenas o **Administrador** pode atribuir papéis globais.
    
    **Funções e Gatilhos:**
    
    - **`verificar_limite_colaboradores()`:** Garante que o limite de colaboradores do plano SaaS seja respeitado.
    - **`trigger_limite_colaboradores`:** Bloqueia inserções que excedam o limite.
    
    ---
    
    ## 4. Planos de Assinatura SaaS para empresas
    
    A plataforma oferecerá os seguintes planos de assinatura para as empresas:
    
    ### 4.1. Plano Essencial
    
    Destinado a empresas que necessitam das ferramentas fundamentais para agendamento online e gestão básica.
    
    - **Gestão de Serviços:** Cadastro de até **6 serviços**.
    - **Gestão de Colaboradores:** Permite o Proprietário (atuando ou não como colaborador) + até **2 colaboradores extras** associados.
    - **Módulo de Relatórios:** Acesso a um **Módulo de Relatórios Básico**, incluindo:
        - Total de agendamentos por período (Dia, Semana, Mês, Período customizado).
        - Faturamento bruto por período (Dia, Semana, Mês, Período customizado).
    - **Combo de Serviços:** Funcionalidade incluída.
    - **Outras Funcionalidades Incluídas:** Todas as funcionalidades marcadas como "Plano Essencial e Premium" nas seções de Requisitos Funcionais (5.1, 5.2, 5.3).
    
    ### 4.2. Plano Premium
    
    Para empresas que buscam funcionalidades avançadas de gestão, marketing e relatórios detalhados.
    
    - **Inclui todas as funcionalidades do Plano Essencial.**
    - **Gestão de Serviços:** Cadastro de até **12 serviços**.
    - **Gestão de Colaboradores:** Permite o Proprietário (atuando ou não como colaborador) + até **6 colaboradores extras** associados.
    - **Módulo de Relatórios:** Acesso ao **Módulo de Relatórios Completo** (conforme detalhado na seção 5.4).
    - **Módulo de Marketing Básico:** Funcionalidade incluída (conforme detalhado na seção 5.5).
    - **Módulo de Assinatura de Serviços Mensal:** Funcionalidade incluída (conforme detalhado na seção 5.6).
    - **Suporte Prioritário.**
    - **Opções Avançadas de Configuração de Notificação.**
    
    ## 5. Requisitos Funcionais
    
    ### 5.1. Módulo de Agendamento
    
    *(Disponível no Plano Essencial e Premium)*
    
    - **Busca de empresas (para o Usuário):**
        - Permitir busca de empresas por localização (cidade, bairro) ou por nome.
        - Exibir lista de empresas encontradas com informações básicas (nome, endereço, talvez avaliação).
    - **Página Dedicada da empresa:**
        - Cada empresa terá uma URL única para sua página na plataforma.
        - A página exibirá: logo da empresa, imagem de capa, carrossel de fotos de portfólio, descrição, endereço completo e lista de serviços com valores.
        - Permitir customização desses elementos pelo Proprietário.
    - **Fluxo de Agendamento (para o Usuário):**
        - Usuário pode escolher a empresa (se acessando pelo marketplace) ou já estará na página da empresa.
        - Usuário pode escolher um colaborador específico (opcional). Se não escolher, o sistema buscará horários disponíveis com qualquer colaborador apto para o serviço.
        - Usuário escolhe o(s) serviço(s) desejado(s).
        - Sistema exibe horários disponíveis com base na empresa, colaborador (se selecionado), serviço(s) e duração(ões).
        - Usuário seleciona o horário desejado.
        - Aparece um campo de Observações (opcional) para o Usuário.
        - Usuário escolhe a forma de pagamento (online ou no local).
        - Usuário confirma o agendamento.
    - **Fluxo de Agendamento (para a empresa - Proprietário/Colaborador):**
        - Receber notificação (e-mail, push, in-app) sobre novo agendamento pendente.
        - Receber notificação de lembrete caso o agendamento não seja confirmado próximo ao prazo de 24 horas.
        - Visualizar detalhes do agendamento (usuário, serviço, horário, colaborador, forma de pagamento, observações do usuário).
        - Opção de **Confirmar** ou **Recusar** o agendamento.
        - Se não houver ação em até 24 horas após a solicitação do Usuário, o agendamento é automaticamente **Recusado** pelo sistema. O Usuário é notificado e, caso tenha pago online, o reembolso de 100% é processado automaticamente, seguindo a lógica de cancelamento pela empresa.
    - **Notificações:**
        - Enviar notificações (SMS, e-mail, push) para o Usuário sobre: confirmação de agendamento, cancelamento de agendamento, lembrete de agendamento (com antecedência configurável).
        - Enviar notificações para a empresa (Proprietário/Colaborador) sobre novos agendamentos pendentes, lembretes para confirmação e cancelamentos por parte do Usuário.
        - O Proprietário poderá **configurar quais tipos de notificações** ele e seus colaboradores associados desejam receber (e-mail, push, ambos), com todas as opções marcadas por padrão. **(Opções avançadas de configuração de notificação disponíveis no Plano Premium).**
    - **Cancelamento e Reembolso:**
        - Usuário pode cancelar o agendamento.
        - **Regra Geral (Cancelamento pelo Usuário):** Se o Usuário cancelar com mais de 24 horas de antecedência do horário agendado, e o colaborador ainda não tiver confirmado, o reembolso é de 100% (se pagou online).
        - **Regra Específica (Cancelamento pelo Usuário):** Se o Usuário cancelar com menos de 24 horas de antecedência do horário agendado, *e* o colaborador já tiver confirmado, aplica-se a regra de porcentagem de reembolso definida pelo Proprietário daquela empresa (podendo ser 0% a 100%).
        - **Cancelamento pela empresa (Proprietário/Colaborador):** Se a empresa (Proprietário ou Colaborador) cancelar um agendamento que já foi confirmado, o Usuário é notificado e há reembolso automático de 100% (se pagou online).
        - Sistema deve processar o reembolso automático via gateway de pagamento (Stripe) caso as condições se apliquem.
    - **Gestão de Horários (para a empresa):**
        - Proprietário define horários base de funcionamento da empresa e dias da semana que funcionará. O Proprietário pode definir horários ou dias indisponíveis para a empresa como um todo.
        - A gestão de horários é **individual por colaborador**. Tanto o Proprietário (para si mesmo, se ativo, e para outros colaboradores) quanto o Colaborador (para si mesmo) podem modificar os horários de trabalho do colaborador.
        - Proprietário e Colaborador podem bloquear horários específicos ou dias inteiros na agenda individual do colaborador (ex: almoço, imprevistos, folgas).
        - Haverá uma opção que pode ser ativada/desativada para o colaborador, permitindo definir horários ou dias recorrentes que ficarão indisponíveis para atendimento (ex: toda segunda-feira, ou todos os dias das 12h às 13h).
        - **Conflito de Agendamento ao Bloquear Horário:** Se um Proprietário ou Colaborador tentar bloquear um horário onde já existe um agendamento confirmado, o sistema avisará sobre o agendamento existente. Caso o usuário decida prosseguir com o bloqueio, o agendamento existente será cancelado (aplicando a regra de cancelamento pela empresa, incluindo notificação do Usuário e reembolso de 100% se aplicável).
        - Sistema deve considerar a duração dos serviços e a disponibilidade da empresa/colaborador ao buscar horários disponíveis.
    - **Alocação de Agendamento (Sem Colaborador Específico):** Se o Usuário optar por não escolher um colaborador específico durante o agendamento, o sistema utilizará um **sistema de rodízio (round-robin)** entre os colaboradores disponíveis (incluindo o Proprietário se ativo) para aquele serviço no horário selecionado para alocar o agendamento.
    
    ### 5.2. Módulo de Pagamento
    
    *(Disponível no Plano Essencial e Premium)*
    
    - **Formas de Pagamento Aceitas:** Pix, Cartão de Débito, Cartão de Crédito, Pagamento no Local.
    - **Pagamento Online:** Integração com gateway de pagamento (Stripe) para processar pagamentos via Pix, Débito e Crédito no momento do agendamento.
    - **Pagamento no Local:**
        - Opção para o cliente escolher pagar diretamente no estabelecimento.
        - Agendamento é confirmado (após aprovação do estabelecimento) mesmo sem pagamento online.
        - O agendamento fica com um status indicando pagamento pendente para o estabelecimento.
        - Proprietário ou Colaborador podem marcar o agendamento como "Pago" na plataforma após receber o valor no local.
    - **Processamento de Reembolso:** Sistema deve integrar com o gateway de pagamento (Stripe) para iniciar reembolsos automáticos conforme as regras de cancelamento definidas.
    
    ### 5.3. Módulo de Gestão da empresa
    
    *(Funcionalidades básicas disponíveis no Plano Essencial e Premium, com limites e adições conforme o plano)*
    
    - **Gestão de Serviços:**
        - Proprietário cadastra, edita e remove serviços oferecidos pela empresa.
        - Definir nome do serviço, descrição, valor e duração.
        - **Limite de Serviços:** Até **6 serviços** no Plano Essencial, até **12 serviços** no Plano Premium.
    - **Gestão de Colaboradores:**
        - Proprietário gerencia (associa/desassocia) usuários existentes na plataforma com o papel Colaborador à sua unidade da empresa.
        - O Proprietário pode gerar uma **chave de convite única** para ser compartilhada com o usuário (via WhatsApp, e-mail, etc.).
        - A chave de convite **expira em 24 horas** se o usuário convidado não confirmar a aceitação do papel Colaborador.
        - O link associado à chave de convite levará o usuário para uma página na plataforma onde ele pode inserir a chave para ser associado à empresa. Se o usuário não tiver cadastro, será direcionado para a página de cadastro primeiro.
        - Um usuário com o papel Usuário que é convidado para o papel Colaborador deve confirmar a aceitação em até 24 horas.
        - Um colaborador só pode estar associado a uma única empresa por vez.
        - O Proprietário terá uma seção "Equipe" nas configurações da empresa onde poderá visualizar os convites enviados, gerar novas chaves e ver o status de cada convite (pendente, aceito, expirado).
        - Associar serviços a colaboradores específicos (se nem todos os colaboradores realizam todos os serviços).
        - **Configuração do Proprietário como Colaborador Ativo:** Opção para o Proprietário habilitar/desabilitar seu próprio perfil como prestador de serviços na sua empresa.
        - **Limite de Colaboradores Extras:** Até **2 colaboradores extras** no Plano Essencial, até **6 colaboradores extras** no Plano Premium (além do Proprietário, caso ele atue como colaborador).
    - **Configuração de Regras de Negócio:** *(Disponível no Plano Essencial e Premium)*
        - Proprietário define a política de cancelamento/reembolso (%) para cancelamentos com menos de 24h e agendamento confirmado.
        - Proprietário escolhe o modelo de negócio da empresa: "Parceria" ou "Funcionários".
    - **Modelos de Negócio Internos:** *(Disponível no Plano Essencial e Premium)*
        - **Parceria:**
            - Opção de ativar/desativar controle de comissão.
            - Se ativo, definir % fixa de comissão por serviço.
            - Campo para definir "Custos Operacionais" por colaborador. O Proprietário define um **valor fixo que pode ser mensal ou semanal** para os custos operacionais de cada colaborador.
            - Sistema deve considerar custos operacionais na dedução do faturamento do colaborador para relatórios.
        - **Funcionários:**
            - Opção de ativar/desativar controle de comissão.
            - Se ativo, definir % fixa de comissão por serviço.
            - NÃO possui campo de custos operacionais.
            - Sistema registra faturamento por funcionário para relatórios.
    
    ### 5.4. Módulo de Relatórios
    
    - **Relatórios Básicos (Plano Essencial):**
        - Acesso a relatórios simplificados para o Proprietário:
            - Total de agendamentos por período (Dia, Semana, Mês, Período customizado).
            - Faturamento bruto por período (Dia, Semana, Mês, Período customizado).
    - **Relatórios Completos (Plano Premium):**
        - Disponível apenas para empresas no Plano Premium.
        - **Relatórios Essenciais para o Proprietário:**
            - Faturamento Total por Período (Dia, Semana, Mês, Período customizado).
            - Faturamento por Colaborador (no período).
            - Faturamento por Serviço (no período).
            - Faturamento por Forma de Pagamento (Pix, Débito, Crédito, Local - no período).
            - **Relatório de Agendamentos** por Período (incluindo status: Confirmado, Pendente, Recusado/Cancelado).
            - Considerar modelo de negócio (Parceria/Funcionários) e comissão/custos operacionais nos relatórios de faturamento por colaborador.
    
    ### 5.5. Módulo de Marketing Básico
    
    *(Disponível exclusivamente no Plano Premium)*
    
    - Funcionalidades básicas para ações de marketing:
        - Envio de e-mail/SMS para clientes (ex: clientes que agendaram X vezes, clientes inativos).
        - Criação e gestão de cupons de desconto.
        - Notificação sobre promoções.
    
    ### 5.6. Módulo de Assinaturas e Combos
    
    - A empresa pode ativar ou desativar estas funcionalidades conforme seu plano.
    - **Assinatura de Serviços Mensal:** *(Disponível exclusivamente no Plano Premium)*
        - A empresa pode configurar serviços específicos (cadastrados previamente) para serem oferecidos em planos de assinatura mensal.
        - O Proprietário define para cada plano de assinatura se o uso dos serviços incluídos é ilimitado dentro do mês ou limitado a um número fixo de usos.
        - **Contador de Usos (para o Cliente):** Para planos com número fixo de usos, o cliente poderá visualizar na plataforma quantos usos do serviço assinado ainda tem disponíveis no ciclo atual.
        - **Fluxo de Agendamento para Assinantes:** O cliente segue o fluxo de agendamento normal. No momento do pagamento, o sistema verifica se o cliente possui uma assinatura ativa que cobre os serviços selecionados.
            - Se todos os serviços selecionados estiverem cobertos pela assinatura, o valor a pagar é R$ 0,00.
            - Se houver serviços selecionados que *não* estão cobertos pela assinatura, o cliente será cobrado apenas pelo valor desses serviços adicionais (sem o desconto da assinatura).
        - **Pagamento da Assinatura:** Pagamento recorrente mensal via cartão de crédito (integrado com Stripe).
        - **Renovação Falha:** Se a renovação do pagamento falhar, o usuário é notificado para atualizar o meio de pagamento. Enquanto a assinatura não estiver vigente, o cliente não terá os benefícios do plano (desconto/uso incluído) no agendamento.
        - **Cancelamento da Assinatura pelo Cliente:**
            - O cliente pode cancelar a assinatura a qualquer momento.
            - O valor pago permanece vigente até o último dia do ciclo de assinatura atual.
            - **Regra de Cancelamento em até 7 dias:**
                - Se o cliente cancelar em até 7 dias após a assinatura *e não tiver utilizado nenhum benefício* do plano nesse período, o reembolso é de 100% e a assinatura é cancelada imediatamente.
                - Se o cliente cancelar em até 7 dias após a assinatura *e tiver utilizado algum benefício* do plano nesse período, o sistema calcula o preço total dos serviços utilizados (sem o desconto da assinatura) e reembolsa a diferença entre o valor pago pela assinatura e o valor dos serviços utilizados. A assinatura é cancelada imediatamente.
            - **Regra de Cancelamento após 7 dias:** Se o cliente cancelar após 7 dias da assinatura, não há direito a reembolso. O cliente pode continuar usando os benefícios da assinatura até a data da próxima renovação, quando a recorrência será cancelada automaticamente.
        - **Gestão de Assinaturas pela Empresa:** O Proprietário pode ativar/desativar a oferta de assinaturas, configurar os planos (serviços incluídos, limite de uso, preço), e visualizar os clientes assinantes e o status de suas assinaturas.
    - **Combo de Serviços:** *(Disponível no Plano Essencial e Premium)*
        - A empresa pode definir regras de "Combos" baseadas em combinações de serviços.
        - O Proprietário cria, edita e exclui combos, especificando quais serviços compõem o combo e qual desconto será aplicado ao preço total desses serviços quando agendados juntos. O desconto pode ser um **valor fixo ou uma porcentagem**.
        - **Aplicação do Combo para o Cliente:** Quando o cliente seleciona um conjunto de serviços que correspondem a um combo ativo na empresa, o sistema detecta automaticamente a combinação.
        - No resumo do agendamento e na tela de pagamento, o desconto do combo é exibido e aplicado ao valor total.
        - **Uso dos Serviços do Combo:** Os serviços incluídos em um combo são agendados e realizados no mesmo horário.
        - **Prioridade Assinatura vs Combo (Aplicável a empresas do Plano Premium que também usam combos):** Se uma empresa tiver ambas as funcionalidades ativas e um cliente selecionar serviços que incluem tanto serviços cobertos pela assinatura quanto serviços que formam um combo (mas que não estão na assinatura), o combo será aplicado *apenas* aos serviços que *não* fazem parte da assinatura do cliente. O cliente pagará apenas pelo valor com desconto dos serviços do combo que não estão na assinatura, e os serviços da assinatura terão custo zero.
    
    ### 5.7. Adesão de empresas (Fluxo de Assinatura SaaS)
    
    *(Disponível no Plano Essencial e Premium)*
    
    - Página de promoção de planos (SSG).
    - Fluxo de compra (Stripe) e Wizard de setup inicial (5 passos).
    - Haverá uma opção de destaque na página inicial da plataforma para que interessados em colocar sua empresa na plataforma possam clicar.
    - Este clique levará a uma **página dedicada** contendo informações detalhadas sobre os benefícios de usar a plataforma, os planos de assinatura disponíveis para as empresas (Essencial e Premium), seus respectivos preços e funcionalidades incluídas.
    - Nesta página, o usuário poderá escolher um plano de assinatura para a empresa e **realizar a compra do acesso SaaS sem a necessidade de informar os dados completos da empresa neste momento**.
    - Seguirá um fluxo de pagamento para a assinatura do SaaS (integrado com Stripe para pagamentos recorrentes).
    - Após a confirmação do pagamento da primeira mensalidade, o usuário terá seu papel elevado para "Proprietário" e terá acesso às funcionalidades de gestão da empresa conforme o plano contratado. Uma entidade 'empresa' é formalmente criada e associada a este usuário no sistema.
    - **Setup Inicial da empresa:**
        - O usuário será então redirecionado para uma página/fluxo guiado de configuração inicial da sua empresa. As informações detalhadas (nome, endereço, descrição, etc.) serão configuradas nesta etapa para que a empresa se torne operacional.
        - Os campos mínimos obrigatórios para que a empresa possa ser listada (se aplicável) e receber agendamentos são:
            - Nome da empresa.
            - Endereço Completo (incluindo cidade e estado para filtros de busca).
            - Cadastro de ao menos um Serviço (com nome, descrição, valor e duração).
            - Definição dos Horários Base de Funcionamento da empresa (dias da semana e faixas horárias).
            - Configuração dos Horários de Trabalho para pelo menos um Colaborador (que pode ser o próprio Proprietário se ele se habilitar, ou um primeiro colaborador associado).
        - Apenas após o preenchimento destes dados mínimos obrigatórios a empresa estará apta a aparecer em buscas (se houver um marketplace) e ter seus horários disponíveis para agendamento pelos usuários.
    
    ## 6. Requisitos Não Funcionais
    
    - **Performance:** A plataforma deve ser fluida, reativa e responsiva, com tempos de carregamento e resposta rápidos, especialmente na busca e agendamento de horários.
    - **Escalabilidade:** A arquitetura deve ser escalável para suportar o crescimento no número de barbearias e clientes ao longo do tempo sem degradação significativa da performance.
    - **Segurança:** Implementar medidas de segurança robustas para proteger dados sensíveis dos usuários e das barbearias. Conformidade total com a Lei Geral de Proteção de Dados (LGPD) no Brasil é mandatória.
    - **Disponibilidade:** A plataforma deve estar acessível 24 horas por dia, 7 dias por semana, com mínimo tempo de inatividade planejado (para manutenção) e não planejado.
    - **Usabilidade:** A interface deve ser básica, moderna e intuitiva, fácil de usar para todos os papéis de usuário, incluindo aqueles com pouca familiaridade com tecnologia.
    - **Compatibilidade:** A plataforma web deve possuir **design responsivo** para se adaptar adequadamente a diferentes tamanhos de tela e ser compatível com as principais versões estáveis dos navegadores modernos (ex: Google Chrome, Mozilla Firefox, Apple Safari, Microsoft Edge) em dispositivos desktop e mobile.
    - **Suporte:** Oferecer suporte inicial às barbearias assinantes via e-mail. (Suporte prioritário para Plano Premium).
    - **Manutenibilidade:** O código-fonte deverá ser bem estruturado, modular, seguir as melhores práticas de desenvolvimento da stack tecnológica definida (Node.js/NestJS, React) e possuir documentação adequada para facilitar a manutenção, correção de bugs e evolução futura do sistema.
    - **Conformidade Regulatória:** Além da conformidade com a LGPD, a plataforma deve buscar aderência às normas de conformidade relevantes para o processamento de pagamentos online (ex: PCI DSS, que é largamente gerenciado através da integração com o gateway de pagamento Stripe) e outras regulamentações aplicáveis ao setor de serviços digitais no Brasil.
    - **Tecnologia Stack:** A stack de desenvolvimento para a plataforma será:
        - **Next.js (App Router)**
            - SSG: landing, planos, perfil público.
            - ISR: busca (5 min), perfil (10 min).
            - SSR: autenticação, onboarding, dashboards, agendamento.
            - API Routes: Stripe webhooks, CRUD.
            - Edge Functions: endpoints públicos de baixa latência (geolocalização).
        - **Supabase**
            - Auth, Storage, Realtime e PostgreSQL gerenciado.
            - Multi-tenant via `tenant_id` + RLS.


    

    - **Rollback:** instruções comentadas ou bloco separado.
    - **Organização:** cada alteração em arquivo próprio; revisão em PR; testes periódicos de rollback.
    
    ```