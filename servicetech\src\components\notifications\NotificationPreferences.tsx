'use client';

import { useState } from 'react';
import { useNotificationPreferences } from '@/hooks/useNotificationPreferences';
import { usePushNotificationSupport } from '@/hooks/useDeviceTokens';
import { TipoNotificacao } from '@/types/notifications';

interface NotificationPreferencesProps {
  className?: string;
}

export default function NotificationPreferences({ className = '' }: NotificationPreferencesProps) {
  const { 
    preferences, 
    loading, 
    error, 
    toggleChannel, 
    toggleNotificationType, 
    resetToDefault 
  } = useNotificationPreferences();
  
  const { 
    isSupported: pushSupported, 
    isGranted: pushGranted, 
    requestPermission 
  } = usePushNotificationSupport();

  const [saving, setSaving] = useState(false);

  const tiposNotificacao: { tipo: TipoNotificacao; label: string; description: string }[] = [
    {
      tipo: 'novo_agendamento',
      label: 'Novo Agendamento',
      description: 'Quando você faz um novo agendamento'
    },
    {
      tipo: 'agendamento_confirmado',
      label: 'Agendamento Confirmado',
      description: 'Quando seu agendamento é confirmado'
    },
    {
      tipo: 'agendamento_recusado',
      label: 'Agendamento Recusado',
      description: 'Quando seu agendamento é recusado'
    },
    {
      tipo: 'agendamento_cancelado',
      label: 'Agendamento Cancelado',
      description: 'Quando um agendamento é cancelado'
    },
    {
      tipo: 'lembrete_confirmacao',
      label: 'Lembrete de Confirmação',
      description: 'Para proprietários: lembrete para confirmar agendamentos'
    },
    {
      tipo: 'lembrete_agendamento',
      label: 'Lembrete de Agendamento',
      description: 'Lembrete antes do seu agendamento'
    },
    {
      tipo: 'pagamento_confirmado',
      label: 'Pagamento Confirmado',
      description: 'Quando seu pagamento é processado'
    }
  ];

  const handleChannelToggle = async (channel: 'email' | 'sms' | 'push') => {
    if (channel === 'push' && !pushGranted && pushSupported) {
      const granted = await requestPermission();
      if (!granted) {
        alert('Permissão para notificações push é necessária');
        return;
      }
    }

    setSaving(true);
    await toggleChannel(channel);
    setSaving(false);
  };

  const handleTypeToggle = async (type: TipoNotificacao) => {
    setSaving(true);
    await toggleNotificationType(type);
    setSaving(false);
  };

  const handleReset = async () => {
    if (confirm('Tem certeza que deseja resetar todas as preferências para o padrão?')) {
      setSaving(true);
      await resetToDefault();
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-4 bg-gray-200 rounded w-full"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="text-red-600">
          <h3 className="text-lg font-semibold mb-2">Erro ao carregar preferências</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Preferências de Notificação
        </h3>
        <button
          onClick={handleReset}
          disabled={saving}
          className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
        >
          Resetar Padrão
        </button>
      </div>

      {/* Canais de Notificação */}
      <div className="mb-8">
        <h4 className="text-md font-medium text-gray-900 mb-4">Canais de Notificação</h4>
        <div className="space-y-4">
          {/* Email */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">📧 Email</label>
              <p className="text-xs text-gray-500">Receber notificações por email</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences?.email_enabled ?? true}
                onChange={() => handleChannelToggle('email')}
                disabled={saving}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* SMS */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">📱 SMS</label>
              <p className="text-xs text-gray-500">Receber notificações por SMS</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences?.sms_enabled ?? true}
                onChange={() => handleChannelToggle('sms')}
                disabled={saving}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* Push */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">🔔 Push</label>
              <p className="text-xs text-gray-500">
                Receber notificações push no navegador
                {!pushSupported && ' (não suportado neste navegador)'}
                {pushSupported && !pushGranted && ' (permissão necessária)'}
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences?.push_enabled ?? true}
                onChange={() => handleChannelToggle('push')}
                disabled={saving || !pushSupported}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 disabled:opacity-50"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Tipos de Notificação */}
      <div>
        <h4 className="text-md font-medium text-gray-900 mb-4">Tipos de Notificação</h4>
        <div className="space-y-3">
          {tiposNotificacao.map(({ tipo, label, description }) => (
            <div key={tipo} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">{label}</label>
                <p className="text-xs text-gray-500">{description}</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={preferences?.tipos_habilitados?.includes(tipo) ?? true}
                  onChange={() => handleTypeToggle(tipo)}
                  disabled={saving}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {saving && (
        <div className="mt-4 text-center">
          <div className="inline-flex items-center text-sm text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
            Salvando...
          </div>
        </div>
      )}
    </div>
  );
}
