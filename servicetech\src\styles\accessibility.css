/* Estilos de Acessibilidade para ServiceTech */

/* Screen Reader Only - Oculta visualmente mas mantém acessível para screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Mostra elementos sr-only quando focados */
.sr-only:focus,
.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Estilos para foco visível melhorado */
.focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Estilos para elementos interativos */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Estilos para alto contraste */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --background: #ffffff;
    --surface: #f8f9fa;
    --border-color: #000000;
    --primary: #0066cc;
    --primary-hover: #0052a3;
    --accent: #ff6600;
    --error: #cc0000;
    --success: #006600;
    --warning: #cc6600;
  }

  /* Aumenta o contraste de bordas */
  .border,
  .border-t,
  .border-b,
  .border-l,
  .border-r {
    border-color: #000000 !important;
    border-width: 2px !important;
  }

  /* Melhora o contraste de botões */
  button,
  .btn {
    border: 2px solid #000000 !important;
    font-weight: bold !important;
  }

  /* Melhora o contraste de links */
  a {
    text-decoration: underline !important;
    font-weight: bold !important;
  }
}

/* Estilos para movimento reduzido */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Remove animações de loading */
  .animate-spin {
    animation: none !important;
  }

  /* Remove transições suaves */
  .transition-all,
  .transition-colors,
  .transition-opacity,
  .transition-transform {
    transition: none !important;
  }
}

/* Estilos para texto grande */
@media (prefers-reduced-data: reduce) {
  body {
    font-size: 18px !important;
    line-height: 1.6 !important;
  }

  h1 { font-size: 2.5rem !important; }
  h2 { font-size: 2rem !important; }
  h3 { font-size: 1.75rem !important; }
  h4 { font-size: 1.5rem !important; }
  h5 { font-size: 1.25rem !important; }
  h6 { font-size: 1.125rem !important; }

  /* Aumenta o tamanho de elementos interativos */
  button,
  input,
  select,
  textarea,
  a {
    min-height: 44px !important;
    padding: 12px 16px !important;
    font-size: 18px !important;
  }
}

/* Estilos para indicadores de foco */
.focus-ring {
  box-shadow: 0 0 0 2px var(--primary);
  border-radius: 4px;
}

.focus-ring-error {
  box-shadow: 0 0 0 2px var(--error);
  border-radius: 4px;
}

.focus-ring-success {
  box-shadow: 0 0 0 2px var(--success);
  border-radius: 4px;
}

/* Estilos para skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--text-on-primary);
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Estilos para indicadores de estado */
.loading-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--primary);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Estilos para mensagens de erro acessíveis */
.error-message {
  color: var(--error);
  font-size: 0.875rem;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-message::before {
  content: "⚠";
  font-weight: bold;
  aria-hidden: true;
}

/* Estilos para mensagens de sucesso */
.success-message {
  color: var(--success);
  font-size: 0.875rem;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.success-message::before {
  content: "✓";
  font-weight: bold;
  aria-hidden: true;
}

/* Estilos para campos obrigatórios */
.required-field label::after {
  content: " *";
  color: var(--error);
  font-weight: bold;
}

/* Estilos para tabelas acessíveis */
.accessible-table {
  border-collapse: collapse;
  width: 100%;
}

.accessible-table th,
.accessible-table td {
  border: 1px solid var(--border-color);
  padding: 12px;
  text-align: left;
}

.accessible-table th {
  background-color: var(--surface);
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.accessible-table tr:nth-child(even) {
  background-color: var(--background);
}

.accessible-table tr:hover {
  background-color: var(--primary-hover);
  color: var(--text-on-primary);
}

/* Estilos para formulários acessíveis */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 16px;
  line-height: 1.5;
  background-color: var(--background);
  color: var(--text-primary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px var(--primary);
}

.form-input:invalid {
  border-color: var(--error);
}

.form-input:invalid:focus {
  box-shadow: 0 0 0 2px var(--error);
}

/* Estilos para modais acessíveis */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--background);
  border-radius: 8px;
  padding: 24px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-content:focus {
  outline: 2px solid var(--primary);
  outline-offset: -2px;
}

/* Estilos para navegação por teclado */
.keyboard-navigation {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Estilos para indicadores de progresso acessíveis */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--surface);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary);
  transition: width 0.3s ease;
}

/* Estilos para tooltips acessíveis */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--text-primary);
  color: var(--background);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.tooltip:hover .tooltip-content,
.tooltip:focus .tooltip-content {
  opacity: 1;
  visibility: visible;
}
