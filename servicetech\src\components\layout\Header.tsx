'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button, buttonVariants } from '@/components/ui/Button';
import { useAuth } from '@/contexts/AuthContext';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { PWAStatus } from '@/components/ui/PWAPrompt';

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const { user, signOut } = useAuth();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleLogout = async () => {
    await signOut();
    router.push('/');
    setIsMenuOpen(false);
  };

  const getUserDisplayName = () => {
    if (!user) return '';
    return user.name ?? user.email.split('@')[0];
  };

  const getDashboardUrl = () => {
    if (!user) return '/';

    switch (user.role) {
      case 'Administrador':
        return '/admin/dashboard';
      case 'Proprietario':
        return '/proprietario/dashboard';
      case 'Colaborador':
        return '/colaborador/agenda';
      default:
        return '/cliente/dashboard';
    }
  };

  return (
    <header
      className="bg-[var(--surface)] border-b border-[var(--border-color)] sticky top-0 z-50 shadow-sm"
      role="banner"
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center space-x-2 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md"
            aria-label="ServiceTech - Página inicial"
          >
            <div className="w-8 h-8 bg-[var(--primary)] rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg" aria-hidden="true">S</span>
            </div>
            <span className="text-xl font-bold text-[var(--text-primary)]">ServiceTech</span>
          </Link>

          {/* Desktop Navigation */}
          <nav
            className="hidden md:flex items-center space-x-8"
            role="navigation"
            aria-label="Navegação principal"
            id="main-navigation"
          >
            <Link
              href="/"
              className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md px-2 py-1"
            >
              Início
            </Link>
            <Link
              href="/buscar"
              className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md px-2 py-1"
            >
              Buscar Estabelecimentos
            </Link>
            <Link
              href="/planos"
              className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md px-2 py-1"
            >
              Planos
            </Link>
          </nav>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              // Usuário logado
              <div className="flex items-center space-x-4">
                <Link
                  href={getDashboardUrl()}
                  className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200"
                >
                  Dashboard
                </Link>
                <Link
                  href="/test-webhook"
                  className="text-[var(--text-secondary)] hover:text-[var(--primary)] transition-colors duration-200 text-sm"
                  title="Página de testes do sistema"
                >
                  🧪 Testes
                </Link>
                <div className="flex items-center space-x-3">
                  <PWAStatus />
                  <ThemeToggle variant="button" size="sm" />
                  <div className="text-right">
                    <p className="text-sm font-medium text-[var(--text-primary)]">
                      {getUserDisplayName()}
                    </p>
                    <p className="text-xs text-[var(--text-secondary)]">
                      {user.role}
                    </p>
                  </div>
                  <Button
                    onClick={handleLogout}
                    variant="outline"
                    size="sm"
                  >
                    Sair
                  </Button>
                </div>
              </div>
            ) : (
              // Usuário não logado
              <>
                <ThemeToggle variant="button" size="sm" />
                <Link
                  href="/login"
                  className={buttonVariants({
                    variant: 'ghost',
                    size: 'sm',
                    className: 'text-[var(--text-primary)] hover:text-[var(--primary)]'
                  })}
                >
                  Entrar
                </Link>
                <Link
                  href="/cadastro"
                  className={buttonVariants({
                    variant: 'primary',
                    size: 'sm'
                  })}
                >
                  Cadastrar
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="md:hidden p-2 rounded-md text-[var(--text-primary)] hover:bg-[var(--background)] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2"
            aria-expanded={isMenuOpen}
            aria-controls="mobile-menu"
            aria-label={isMenuOpen ? 'Fechar menu' : 'Abrir menu'}
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              {isMenuOpen ? (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              ) : (
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <nav
            className="md:hidden py-4 border-t border-[var(--border-color)] flex flex-col space-y-4"
            id="mobile-menu"
            aria-label="Menu de navegação mobile"
          >
              <Link
                href="/"
                className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Início
              </Link>
              <Link
                href="/buscar"
                className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Buscar Estabelecimentos
              </Link>
              <Link
                href="/planos"
                className="text-[var(--text-primary)] hover:text-[var(--primary)] transition-colors duration-200 px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
                Planos
              </Link>
              <div className="flex flex-col space-y-2 pt-4 border-t border-[var(--border-color)]">
                {/* Toggle de tema para mobile */}
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-[var(--text-primary)]">Tema</span>
                  <ThemeToggle variant="dropdown" size="sm" showLabel />
                </div>

                {user ? (
                  // Usuário logado - Mobile
                  <>
                    <div className="bg-[var(--surface)] p-3 rounded-lg mb-2">
                      <p className="text-sm font-medium text-[var(--text-primary)]">
                        {getUserDisplayName()}
                      </p>
                      <p className="text-xs text-[var(--text-secondary)]">
                        {user.role}
                      </p>
                    </div>
                    <Link
                      href={getDashboardUrl()}
                      className={buttonVariants({
                        variant: 'outline',
                        size: 'sm'
                      })}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/test-webhook"
                      className={buttonVariants({
                        variant: 'ghost',
                        size: 'sm',
                        className: 'text-[var(--text-secondary)]'
                      })}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      🧪 Testes
                    </Link>
                    <Button
                      onClick={handleLogout}
                      variant="primary"
                      size="sm"
                    >
                      Sair
                    </Button>
                  </>
                ) : (
                  // Usuário não logado - Mobile
                  <>
                    <Link
                      href="/login"
                      className={buttonVariants({
                        variant: 'outline',
                        size: 'sm'
                      })}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Entrar
                    </Link>
                    <Link
                      href="/cadastro"
                      className={buttonVariants({
                        variant: 'primary',
                        size: 'sm'
                      })}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Cadastrar
                    </Link>
                  </>
                )}
              </div>
          </nav>
        )}
      </div>
    </header>
  );
}
