'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { createClient } from '@/utils/supabase/client';

interface PaymentStatusPollingOptions {
  maxAttempts?: number;
  intervalMs?: number;
  timeoutMs?: number;
  onSuccess?: () => void;
  onTimeout?: () => void;
  onError?: (error: string) => void;
}

interface PaymentStatusResult {
  isPolling: boolean;
  attempts: number;
  maxAttempts: number;
  timeRemaining: number;
  error: string | null;
  userStatus: {
    pagamento_confirmado: boolean;
    onboarding_pendente: boolean;
    role: string;
  } | null;
}

export function usePaymentStatusPolling(options: PaymentStatusPollingOptions = {}) {
  const {
    maxAttempts = 15, // 15 tentativas
    intervalMs = 2000, // 2 segundos entre tentativas
    timeoutMs = 30000, // 30 segundos total
    onSuccess,
    onTimeout,
    onError
  } = options;

  const { refreshUser } = useAuth();
  const [result, setResult] = useState<PaymentStatusResult>({
    isPolling: false,
    attempts: 0,
    maxAttempts,
    timeRemaining: timeoutMs,
    error: null,
    userStatus: null
  });

  const checkUserStatus = useCallback(async (): Promise<{
    pagamento_confirmado: boolean;
    onboarding_pendente: boolean;
    role: string;
  } | null> => {
    try {
      // Primeiro, obter o ID do usuário atual
      const supabase = createClient();
      const { data: { user }, error: getUserError } = await supabase.auth.getUser();

      if (getUserError || !user) {
        throw new Error('Usuário não encontrado');
      }

      // Usar o endpoint de status para obter dados mais recentes
      const response = await fetch(`/api/user/status?userId=${user.id}`);

      if (!response.ok) {
        throw new Error('Erro ao verificar status do usuário');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Erro desconhecido');
      }

      // Forçar atualização do contexto após obter dados atualizados
      await refreshUser();

      return {
        pagamento_confirmado: data.user.pagamento_confirmado,
        onboarding_pendente: data.user.onboarding_pendente,
        role: data.user.role
      };
    } catch (error) {
      console.error('Erro ao verificar status do usuário:', error);
      return null;
    }
  }, [refreshUser]);

  const startPolling = useCallback(() => {
    setResult(prev => ({
      ...prev,
      isPolling: true,
      attempts: 0,
      timeRemaining: timeoutMs,
      error: null
    }));

    let attempts = 0;
    let timeoutId: NodeJS.Timeout;
    let intervalId: NodeJS.Timeout;

    // Timeout geral
    const timeoutTimer = setTimeout(() => {
      clearInterval(intervalId);
      setResult(prev => ({
        ...prev,
        isPolling: false,
        error: 'Timeout: O processamento do pagamento está demorando mais que o esperado'
      }));
      onTimeout?.();
    }, timeoutMs);

    // Timer para atualizar tempo restante
    const startTime = Date.now();
    const updateTimeRemaining = () => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, timeoutMs - elapsed);
      setResult(prev => ({
        ...prev,
        timeRemaining: remaining
      }));
    };

    // Função de polling
    const poll = async () => {
      attempts++;
      
      setResult(prev => ({
        ...prev,
        attempts
      }));

      console.log(`🔍 Tentativa ${attempts}/${maxAttempts} - Verificando status do pagamento...`);

      try {
        const userStatus = await checkUserStatus();
        
        if (userStatus) {
          setResult(prev => ({
            ...prev,
            userStatus
          }));

          // Verificar se o pagamento foi confirmado
          if (userStatus.pagamento_confirmado && userStatus.role === 'Proprietario') {
            console.log('✅ Pagamento confirmado! Redirecionando...');
            
            clearTimeout(timeoutTimer);
            clearInterval(intervalId);
            
            setResult(prev => ({
              ...prev,
              isPolling: false
            }));
            
            onSuccess?.();
            return;
          }
        }

        // Se chegou ao máximo de tentativas
        if (attempts >= maxAttempts) {
          console.log('⚠️ Máximo de tentativas atingido');
          
          clearTimeout(timeoutTimer);
          clearInterval(intervalId);
          
          setResult(prev => ({
            ...prev,
            isPolling: false,
            error: 'Máximo de tentativas atingido. Tente atualizar a página.'
          }));
          
          onTimeout?.();
          return;
        }

      } catch (error) {
        console.error('Erro durante polling:', error);
        
        const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        
        setResult(prev => ({
          ...prev,
          error: errorMessage
        }));
        
        onError?.(errorMessage);
      }
    };

    // Iniciar polling
    poll(); // Primeira verificação imediata
    intervalId = setInterval(poll, intervalMs);
    
    // Atualizar tempo restante a cada 100ms
    const timeUpdateInterval = setInterval(updateTimeRemaining, 100);

    // Cleanup
    return () => {
      clearTimeout(timeoutTimer);
      clearInterval(intervalId);
      clearInterval(timeUpdateInterval);
    };
  }, [checkUserStatus, maxAttempts, intervalMs, timeoutMs, onSuccess, onTimeout, onError]);

  const stopPolling = useCallback(() => {
    setResult(prev => ({
      ...prev,
      isPolling: false
    }));
  }, []);

  return {
    ...result,
    startPolling,
    stopPolling
  };
}
