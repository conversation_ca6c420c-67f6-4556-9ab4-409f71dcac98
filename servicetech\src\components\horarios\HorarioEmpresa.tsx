'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { HorarioEmpresa, HorarioDia, HorarioPausa, DiaSemana } from '@/types/horarios';

interface HorarioEmpresaProps {
  horarios: HorarioEmpresa | null;
  onSalvar: (horarios: HorarioEmpresa) => Promise<boolean>;
  loading?: boolean;
}

const diasSemana: { key: DiaSemana; label: string }[] = [
  { key: 'segunda', label: 'Segunda-feira' },
  { key: 'terca', label: 'Terça-feira' },
  { key: 'quarta', label: 'Quarta-feira' },
  { key: 'quinta', label: 'Quinta-feira' },
  { key: 'sexta', label: 'Sexta-feira' },
  { key: 'sabado', label: 'S<PERSON>bado' },
  { key: 'domingo', label: 'Domingo' },
];

export function HorarioEmpresaComponent({ horarios, onSalvar, loading = false }: HorarioEmpresaProps) {
  const [horariosEditados, setHorariosEditados] = useState<HorarioEmpresa | null>(null);
  const [salvando, setSalvando] = useState(false);
  const [mensagem, setMensagem] = useState<{ tipo: 'success' | 'error'; texto: string } | null>(null);

  // Inicializar horários editados quando os horários mudarem
  useEffect(() => {
    if (horarios) {
      setHorariosEditados({ ...horarios });
    }
  }, [horarios]);

  // Limpar mensagens após 5 segundos
  useEffect(() => {
    if (mensagem) {
      const timer = setTimeout(() => setMensagem(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [mensagem]);

  const handleDiaChange = (dia: DiaSemana, campo: keyof HorarioDia, valor: any) => {
    if (!horariosEditados) return;

    setHorariosEditados(prev => {
      if (!prev) return prev;
      
      return {
        ...prev,
        [dia]: {
          ...prev[dia],
          [campo]: valor
        }
      };
    });
  };

  const handlePausaChange = (dia: DiaSemana, indice: number, campo: keyof HorarioPausa, valor: string) => {
    if (!horariosEditados) return;

    setHorariosEditados(prev => {
      if (!prev) return prev;
      
      const pausasAtuais = prev[dia].pausas || [];
      const novasPausas = [...pausasAtuais];
      novasPausas[indice] = {
        ...novasPausas[indice],
        [campo]: valor
      };

      return {
        ...prev,
        [dia]: {
          ...prev[dia],
          pausas: novasPausas
        }
      };
    });
  };

  const adicionarPausa = (dia: DiaSemana) => {
    if (!horariosEditados) return;

    setHorariosEditados(prev => {
      if (!prev) return prev;
      
      const pausasAtuais = prev[dia].pausas || [];
      const novaPausa: HorarioPausa = {
        inicio: '12:00',
        fim: '13:00',
        descricao: 'Pausa'
      };

      return {
        ...prev,
        [dia]: {
          ...prev[dia],
          pausas: [...pausasAtuais, novaPausa]
        }
      };
    });
  };

  const removerPausa = (dia: DiaSemana, indice: number) => {
    if (!horariosEditados) return;

    setHorariosEditados(prev => {
      if (!prev) return prev;
      
      const pausasAtuais = prev[dia].pausas || [];
      const novasPausas = pausasAtuais.filter((_, i) => i !== indice);

      return {
        ...prev,
        [dia]: {
          ...prev[dia],
          pausas: novasPausas
        }
      };
    });
  };

  const aplicarParaTodos = (dia: DiaSemana) => {
    if (!horariosEditados) return;

    const horarioReferencia = horariosEditados[dia];
    
    setHorariosEditados(prev => {
      if (!prev) return prev;
      
      const novosHorarios = { ...prev };
      
      diasSemana.forEach(({ key }) => {
        if (key !== dia) {
          novosHorarios[key] = {
            ...horarioReferencia,
            pausas: horarioReferencia.pausas ? [...horarioReferencia.pausas] : []
          };
        }
      });

      return novosHorarios;
    });

    setMensagem({
      tipo: 'success',
      texto: `Horário de ${diasSemana.find(d => d.key === dia)?.label} aplicado para todos os dias`
    });
  };

  const handleSalvar = async () => {
    if (!horariosEditados) return;

    setSalvando(true);
    setMensagem(null);

    try {
      const sucesso = await onSalvar(horariosEditados);
      
      if (sucesso) {
        setMensagem({
          tipo: 'success',
          texto: 'Horários da empresa salvos com sucesso!'
        });
      } else {
        setMensagem({
          tipo: 'error',
          texto: 'Erro ao salvar horários. Tente novamente.'
        });
      }
    } catch (error) {
      setMensagem({
        tipo: 'error',
        texto: 'Erro inesperado ao salvar horários.'
      });
    } finally {
      setSalvando(false);
    }
  };

  if (loading || !horariosEditados) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
        <span className="ml-2 text-[var(--text-secondary)]">Carregando horários...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Mensagem de feedback */}
      {mensagem && (
        <div className={`p-4 rounded-md ${
          mensagem.tipo === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {mensagem.texto}
        </div>
      )}

      {/* Cabeçalho */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-[var(--text-primary)]">
            Horários de Funcionamento
          </h2>
          <p className="text-[var(--text-secondary)] mt-1">
            Configure os horários de funcionamento da sua empresa
          </p>
        </div>
        
        <Button
          onClick={handleSalvar}
          disabled={salvando}
          className="min-w-[120px]"
        >
          {salvando ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Salvando...
            </>
          ) : (
            'Salvar Horários'
          )}
        </Button>
      </div>

      {/* Configuração por dia */}
      <div className="grid gap-4">
        {diasSemana.map(({ key, label }) => {
          const horarioDia = horariosEditados[key];
          
          return (
            <Card key={key} className="p-6">
              <div className="space-y-4">
                {/* Cabeçalho do dia */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={horarioDia.ativo}
                      onChange={(e) => handleDiaChange(key, 'ativo', e.target.checked)}
                      className="h-4 w-4 text-[var(--primary)] focus:ring-[var(--primary)] border-gray-300 rounded"
                    />
                    <h3 className="text-lg font-semibold text-[var(--text-primary)]">
                      {label}
                    </h3>
                  </div>
                  
                  {horarioDia.ativo && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => aplicarParaTodos(key)}
                    >
                      Aplicar para todos
                    </Button>
                  )}
                </div>

                {/* Configuração de horários */}
                {horarioDia.ativo && (
                  <div className="space-y-4 pl-7">
                    {/* Horário de funcionamento */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                          Abertura
                        </label>
                        <Input
                          type="time"
                          value={horarioDia.abertura}
                          onChange={(e) => handleDiaChange(key, 'abertura', e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-[var(--text-primary)] mb-1">
                          Fechamento
                        </label>
                        <Input
                          type="time"
                          value={horarioDia.fechamento}
                          onChange={(e) => handleDiaChange(key, 'fechamento', e.target.value)}
                        />
                      </div>
                    </div>

                    {/* Pausas */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-[var(--text-primary)]">
                          Pausas
                        </label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => adicionarPausa(key)}
                        >
                          + Adicionar Pausa
                        </Button>
                      </div>
                      
                      {horarioDia.pausas && horarioDia.pausas.length > 0 && (
                        <div className="space-y-2">
                          {horarioDia.pausas.map((pausa, indice) => (
                            <div key={indice} className="grid grid-cols-1 md:grid-cols-4 gap-2 items-end">
                              <div>
                                <label className="block text-xs text-[var(--text-secondary)] mb-1">
                                  Início
                                </label>
                                <Input
                                  type="time"
                                  value={pausa.inicio}
                                  onChange={(e) => handlePausaChange(key, indice, 'inicio', e.target.value)}
                                />
                              </div>
                              <div>
                                <label className="block text-xs text-[var(--text-secondary)] mb-1">
                                  Fim
                                </label>
                                <Input
                                  type="time"
                                  value={pausa.fim}
                                  onChange={(e) => handlePausaChange(key, indice, 'fim', e.target.value)}
                                />
                              </div>
                              <div>
                                <label className="block text-xs text-[var(--text-secondary)] mb-1">
                                  Descrição
                                </label>
                                <Input
                                  type="text"
                                  value={pausa.descricao || ''}
                                  onChange={(e) => handlePausaChange(key, indice, 'descricao', e.target.value)}
                                  placeholder="Ex: Almoço"
                                />
                              </div>
                              <div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removerPausa(key, indice)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  Remover
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </Card>
          );
        })}
      </div>

      {/* Observações */}
      <Card className="p-6">
        <div>
          <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
            Observações (opcional)
          </label>
          <textarea
            value={horariosEditados.observacoes || ''}
            onChange={(e) => setHorariosEditados(prev => prev ? { ...prev, observacoes: e.target.value } : prev)}
            className="w-full h-20 px-3 py-2 border border-[var(--border-color)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)] resize-none"
            placeholder="Informações adicionais sobre os horários de funcionamento..."
          />
        </div>
      </Card>
    </div>
  );
}
