import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { 
  PoliticaCancelamento, 
  AtualizarPoliticaCancelamentoData,
  POLITICA_CANCELAMENTO_PADRAO 
} from '@/types/politicas';
import { CancelamentoUtils } from '@/utils/cancelamento';

// GET - Buscar política de cancelamento da empresa
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient();
    const resolvedParams = await params;
    const empresaId = parseInt(resolvedParams.id);

    if (isNaN(empresaId)) {
      return NextResponse.json(
        { success: false, error: 'ID da empresa inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Buscar empresa e verificar permissão
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, proprietario_user_id, politica_cancelamento')
      .eq('empresa_id', empresaId)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se o usuário é proprietário da empresa
    const userRole = user.user_metadata?.role;
    const isProprietario = empresa.proprietario_user_id === user.id;
    const isAdmin = userRole === 'Administrador';

    if (!isProprietario && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Sem permissão para acessar esta empresa' },
        { status: 403 }
      );
    }

    // Retornar política (usar padrão se não existir)
    const politica = empresa.politica_cancelamento 
      ? CancelamentoUtils.mesclarComPadrao(empresa.politica_cancelamento)
      : POLITICA_CANCELAMENTO_PADRAO;

    return NextResponse.json({
      success: true,
      data: politica
    });

  } catch (error) {
    console.error('Erro ao buscar política de cancelamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PUT - Atualizar política de cancelamento da empresa
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createClient();
    const resolvedParams = await params;
    const empresaId = parseInt(resolvedParams.id);

    if (isNaN(empresaId)) {
      return NextResponse.json(
        { success: false, error: 'ID da empresa inválido' },
        { status: 400 }
      );
    }

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Buscar empresa e verificar permissão
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, proprietario_user_id, politica_cancelamento')
      .eq('empresa_id', empresaId)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Verificar se o usuário é proprietário da empresa
    const isProprietario = empresa.proprietario_user_id === user.id;
    const userRole = user.user_metadata?.role;
    const isAdmin = userRole === 'Administrador';

    if (!isProprietario && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Sem permissão para modificar esta empresa' },
        { status: 403 }
      );
    }

    // Obter dados da requisição
    const body: AtualizarPoliticaCancelamentoData = await request.json();

    // Obter política atual
    const politicaAtual = empresa.politica_cancelamento 
      ? CancelamentoUtils.mesclarComPadrao(empresa.politica_cancelamento)
      : POLITICA_CANCELAMENTO_PADRAO;

    // Criar nova política mesclando com os dados recebidos
    const novaPolitica: PoliticaCancelamento = {
      ...politicaAtual,
      cliente: {
        ...politicaAtual.cliente,
        antecedencia_24h_confirmado: {
          ...politicaAtual.cliente.antecedencia_24h_confirmado,
          ...body.cliente?.antecedencia_24h_confirmado
        },
        mesmo_dia: {
          ...politicaAtual.cliente.mesmo_dia,
          ...body.cliente?.mesmo_dia
        }
      },
      configuracoes: {
        ...politicaAtual.configuracoes,
        ...body.configuracoes
      },
      updated_at: new Date().toISOString()
    };

    // Validar nova política
    const validacao = CancelamentoUtils.validarPolitica(novaPolitica);
    if (!validacao.valida) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Dados inválidos',
          details: validacao.erros
        },
        { status: 400 }
      );
    }

    // Atualizar no banco de dados
    const { error: updateError } = await supabase
      .from('empresas')
      .update({
        politica_cancelamento: novaPolitica,
        updated_at: new Date().toISOString()
      })
      .eq('empresa_id', empresaId);

    if (updateError) {
      console.error('Erro ao atualizar política:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao salvar política de cancelamento' },
        { status: 500 }
      );
    }

    console.log('✅ Política de cancelamento atualizada:', {
      empresa_id: empresaId,
      usuario: user.id,
      politica: novaPolitica
    });

    return NextResponse.json({
      success: true,
      data: novaPolitica,
      message: 'Política de cancelamento atualizada com sucesso'
    });

  } catch (error) {
    console.error('Erro ao atualizar política de cancelamento:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
