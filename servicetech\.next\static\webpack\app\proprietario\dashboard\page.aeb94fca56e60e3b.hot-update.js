"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/proprietario/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/agendamentos/CardAgendamento.tsx":
/*!*********************************************************!*\
  !*** ./src/components/agendamentos/CardAgendamento.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CardAgendamento: () => (/* binding */ CardAgendamento)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _BotaoCancelamento__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BotaoCancelamento */ \"(app-pages-browser)/./src/components/agendamentos/BotaoCancelamento.tsx\");\n/* harmony import */ var _barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=addHours,format,isAfter,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=addHours,format,isAfter,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* harmony import */ var _barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addHours,format,isAfter,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addHours.js\");\n/* harmony import */ var _barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addHours,format,isAfter,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* __next_internal_client_entry_do_not_use__ CardAgendamento auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CardAgendamento = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function CardAgendamento(param) {\n    let { agendamento, onConfirmar, onRecusar, onCancelar, onConcluir, onMarcarPago, onVerDetalhes, mostrarAcoes = true, loading = false, userRole } = param;\n    var _agendamento_empresa, _agendamento_colaborador, _agendamento_cliente, _agendamento_colaborador1;\n    _s();\n    // Memoizar cálculos de data para evitar re-processamento\n    const dateCalculations = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CardAgendamento.CardAgendamento.useMemo[dateCalculations]\": ()=>{\n            const dataHoraInicio = (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_5__.parseISO)(agendamento.data_hora_inicio);\n            const dataHoraFim = (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_5__.parseISO)(agendamento.data_hora_fim);\n            const prazoConfirmacao = (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_5__.parseISO)(agendamento.prazo_confirmacao);\n            const agora = new Date();\n            // Verificar se está próximo do prazo\n            const proximoPrazo = agendamento.status_agendamento === 'Pendente' && (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_6__.isAfter)(agora, (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_7__.addHours)(prazoConfirmacao, -2)); // 2 horas antes do prazo\n            const prazoExpirado = agendamento.status_agendamento === 'Pendente' && (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_6__.isAfter)(agora, prazoConfirmacao);\n            return {\n                dataHoraInicio,\n                dataHoraFim,\n                prazoConfirmacao,\n                proximoPrazo,\n                prazoExpirado\n            };\n        }\n    }[\"CardAgendamento.CardAgendamento.useMemo[dateCalculations]\"], [\n        agendamento.data_hora_inicio,\n        agendamento.data_hora_fim,\n        agendamento.prazo_confirmacao,\n        agendamento.status_agendamento\n    ]);\n    const { dataHoraInicio, dataHoraFim, prazoConfirmacao, proximoPrazo, prazoExpirado } = dateCalculations;\n    // Memoizar configuração de status para evitar recálculos\n    const statusConfig = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CardAgendamento.CardAgendamento.useMemo[statusConfig]\": ()=>{\n            const getStatusConfig = {\n                \"CardAgendamento.CardAgendamento.useMemo[statusConfig].getStatusConfig\": (status)=>{\n                    switch(status){\n                        case 'Pendente':\n                            const color = prazoExpirado ? 'text-red-600 bg-red-50 border-red-200' : proximoPrazo ? 'text-orange-600 bg-orange-50 border-orange-200' : 'text-yellow-600 bg-yellow-50 border-yellow-200';\n                            return {\n                                color,\n                                icon: '⏳',\n                                label: prazoExpirado ? 'Expirado' : 'Pendente'\n                            };\n                        case 'Confirmado':\n                            return {\n                                color: 'text-blue-600 bg-blue-50 border-blue-200',\n                                icon: '✅',\n                                label: 'Confirmado'\n                            };\n                        case 'Recusado':\n                            return {\n                                color: 'text-red-600 bg-red-50 border-red-200',\n                                icon: '❌',\n                                label: 'Recusado'\n                            };\n                        case 'Cancelado':\n                            return {\n                                color: 'text-gray-600 bg-gray-50 border-gray-200',\n                                icon: '🚫',\n                                label: 'Cancelado'\n                            };\n                        case 'Concluido':\n                            return {\n                                color: 'text-green-600 bg-green-50 border-green-200',\n                                icon: '✨',\n                                label: 'Concluído'\n                            };\n                        default:\n                            return {\n                                color: 'text-gray-600 bg-gray-50 border-gray-200',\n                                icon: '❓',\n                                label: status\n                            };\n                    }\n                }\n            }[\"CardAgendamento.CardAgendamento.useMemo[statusConfig].getStatusConfig\"];\n            return getStatusConfig(agendamento.status_agendamento);\n        }\n    }[\"CardAgendamento.CardAgendamento.useMemo[statusConfig]\"], [\n        agendamento.status_agendamento,\n        prazoExpirado,\n        proximoPrazo\n    ]);\n    // Verificar quais ações são permitidas\n    const podeConfirmar = agendamento.status_agendamento === 'Pendente' && !prazoExpirado && (userRole === 'Proprietario' || userRole === 'Colaborador');\n    const podeRecusar = agendamento.status_agendamento === 'Pendente' && (userRole === 'Proprietario' || userRole === 'Colaborador');\n    const podeCancelar = [\n        'Pendente',\n        'Confirmado'\n    ].includes(agendamento.status_agendamento);\n    const podeConcluir = agendamento.status_agendamento === 'Confirmado' && (userRole === 'Proprietario' || userRole === 'Colaborador');\n    const podeMarcarPago = agendamento.forma_pagamento === 'Local' && agendamento.status_pagamento === 'Pendente' && (userRole === 'Proprietario' || userRole === 'Colaborador');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"transition-all duration-200 hover:shadow-md \".concat(proximoPrazo ? 'ring-2 ring-orange-200' : ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border \".concat(statusConfig.color),\n                                            children: [\n                                                statusConfig.icon,\n                                                \" \",\n                                                statusConfig.label\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        proximoPrazo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-orange-600 bg-orange-100\",\n                                            children: \"⚠️ Prazo pr\\xf3ximo\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-[var(--text-primary)]\",\n                                    children: agendamento.servico.nome_servico\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-[var(--text-secondary)] space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDCC5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_8__.format)(dataHoraInicio, \"dd 'de' MMMM 'de' yyyy\", {\n                                                        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_9__.ptBR\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD50\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_8__.format)(dataHoraInicio, 'HH:mm'),\n                                                        \" - \",\n                                                        (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_8__.format)(dataHoraFim, 'HH:mm')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        userRole === 'Usuario' ? // Para clientes, mostrar empresa e colaborador\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83C\\uDFE2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: ((_agendamento_empresa = agendamento.empresa) === null || _agendamento_empresa === void 0 ? void 0 : _agendamento_empresa.nome_empresa) || 'Empresa não identificada'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: ((_agendamento_colaborador = agendamento.colaborador) === null || _agendamento_colaborador === void 0 ? void 0 : _agendamento_colaborador.name) || 'Colaborador não identificado'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : // Para proprietários e colaboradores, mostrar cliente\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDC64\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: ((_agendamento_cliente = agendamento.cliente) === null || _agendamento_cliente === void 0 ? void 0 : _agendamento_cliente.name) || 'Cliente não identificado'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                userRole === 'Proprietario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: ((_agendamento_colaborador1 = agendamento.colaborador) === null || _agendamento_colaborador1 === void 0 ? void 0 : _agendamento_colaborador1.name) || 'Colaborador não identificado'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-bold text-[var(--primary)]\",\n                                    children: [\n                                        \"R$ \",\n                                        agendamento.valor_total.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-[var(--text-secondary)]\",\n                                    children: agendamento.forma_pagamento\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                agendamento.forma_pagamento === 'Local' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs \".concat(agendamento.status_pagamento === 'Pago' ? 'text-green-600' : 'text-orange-600'),\n                                    children: agendamento.status_pagamento === 'Pago' ? '✅ Pago' : '⏳ Pendente'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"pt-0\",\n                children: [\n                    agendamento.observacoes_cliente && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-medium text-[var(--text-primary)] mb-1\",\n                                children: \"Observa\\xe7\\xf5es do cliente:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-[var(--text-secondary)]\",\n                                children: agendamento.observacoes_cliente\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this),\n                    agendamento.status_agendamento === 'Pendente' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 p-3 bg-blue-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Prazo para confirma\\xe7\\xe3o:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' ',\n                                    (0,_barrel_optimize_names_addHours_format_isAfter_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_8__.format)(prazoConfirmacao, \"dd/MM/yyyy 'às' HH:mm\", {\n                                        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_9__.ptBR\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            proximoPrazo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-orange-600 mt-1\",\n                                children: \"⚠️ Confirme ou recuse em breve para evitar cancelamento autom\\xe1tico\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this),\n                    mostrarAcoes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            podeConfirmar && onConfirmar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                onClick: ()=>onConfirmar(agendamento.agendamento_id),\n                                disabled: loading,\n                                className: \"bg-green-600 hover:bg-green-700\",\n                                children: \"✅ Confirmar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this),\n                            podeRecusar && onRecusar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onRecusar(agendamento.agendamento_id),\n                                disabled: loading,\n                                className: \"border-red-300 text-red-600 hover:bg-red-50\",\n                                children: \"❌ Recusar\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, this),\n                            podeConcluir && onConcluir && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                onClick: ()=>onConcluir(agendamento.agendamento_id),\n                                disabled: loading,\n                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                children: \"✨ Concluir\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this),\n                            podeMarcarPago && onMarcarPago && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onMarcarPago(agendamento.agendamento_id),\n                                disabled: loading,\n                                className: \"border-green-300 text-green-600 hover:bg-green-50\",\n                                children: \"\\uD83D\\uDCB0 Marcar como Pago\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, this),\n                            podeCancelar && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BotaoCancelamento__WEBPACK_IMPORTED_MODULE_4__.BotaoCancelamento, {\n                                agendamento: agendamento,\n                                onCancelado: ()=>{\n                                    // Callback para quando o agendamento for cancelado\n                                    if (onCancelar) {\n                                        onCancelar(agendamento.agendamento_id);\n                                    }\n                                },\n                                size: \"sm\",\n                                variant: \"outline\",\n                                className: \"border-gray-300 text-gray-600 hover:bg-gray-50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, this),\n                            onVerDetalhes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onVerDetalhes(agendamento),\n                                disabled: loading,\n                                children: \"\\uD83D\\uDC41️ Ver Detalhes\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this),\n                    agendamento.codigo_confirmacao && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-[var(--text-secondary)]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"C\\xf3digo:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                agendamento.codigo_confirmacao\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\agendamentos\\\\CardAgendamento.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}, \"R5sekVQAsSCXgaQAz1duz/y2oCU=\")), \"R5sekVQAsSCXgaQAz1duz/y2oCU=\");\n_c1 = CardAgendamento;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"CardAgendamento$memo\");\n$RefreshReg$(_c1, \"CardAgendamento\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/agendamentos/CardAgendamento.tsx\n"));

/***/ })

});