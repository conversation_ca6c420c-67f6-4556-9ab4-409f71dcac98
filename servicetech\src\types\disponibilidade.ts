// Tipos para verificação de disponibilidade de horários

// Horário disponível para agendamento
export interface HorarioDisponivel {
  data_hora_inicio: string;
  data_hora_fim: string;
  colaborador_user_id?: string; // Se específico para um colaborador
  colaborador_nome?: string;
  disponivel: boolean;
}

// Colaborador disponível para um serviço
export interface ColaboradorDisponivel {
  colaborador_user_id: string;
  name: string;
  email: string;
  pode_realizar_servico: boolean;
  horarios_disponiveis: HorarioDisponivel[];
}

// Parâmetros para busca de disponibilidade
export interface ParametrosDisponibilidade {
  empresa_id: number;
  servico_id: number;
  colaborador_user_id?: string; // Opcional - se não informado, busca todos
  data_inicio: string; // Data de início da busca (YYYY-MM-DD)
  data_fim: string; // Data de fim da busca (YYYY-MM-DD)
  incluir_ocupados?: boolean; // Se deve incluir horários ocupados na resposta
}

// Resposta da verificação de disponibilidade
export interface DisponibilidadeResponse {
  success: boolean;
  data?: {
    empresa_id: number;
    servico: {
      servico_id: number;
      nome_servico: string;
      duracao_minutos: number;
      preco: number;
    };
    colaboradores_disponiveis: ColaboradorDisponivel[];
    horarios_disponiveis: HorarioDisponivel[];
    periodo_consultado: {
      data_inicio: string;
      data_fim: string;
    };
  };
  error?: string;
  message?: string;
}

// Slot de tempo para agendamento
export interface SlotTempo {
  inicio: string; // HH:mm
  fim: string; // HH:mm
  disponivel: boolean;
  motivo_indisponivel?: string; // Ex: "Agendamento existente", "Fora do horário comercial"
}

// Dia com slots de tempo
export interface DiaDisponibilidade {
  data: string; // YYYY-MM-DD
  dia_semana: number; // 0=Domingo, 6=Sábado
  funcionamento: {
    aberto: boolean;
    horario_abertura?: string;
    horario_fechamento?: string;
    pausas?: Array<{
      inicio: string;
      fim: string;
      descricao?: string;
    }>;
  };
  slots: SlotTempo[];
  colaboradores_disponiveis: string[]; // IDs dos colaboradores disponíveis neste dia
}

// Calendário de disponibilidade
export interface CalendarioDisponibilidade {
  empresa_id: number;
  servico_id: number;
  colaborador_user_id?: string;
  mes: number; // 1-12
  ano: number;
  dias: DiaDisponibilidade[];
  configuracoes: {
    intervalo_agendamento: number; // Minutos entre agendamentos
    antecedencia_minima: number; // Horas de antecedência mínima
    antecedencia_maxima: number; // Dias de antecedência máxima
  };
}

// Conflito de horário
export interface ConflitoHorario {
  tipo: 'agendamento' | 'bloqueio' | 'horario_comercial' | 'pausa';
  descricao: string;
  data_hora_inicio: string;
  data_hora_fim: string;
  agendamento_id?: number;
  colaborador_user_id?: string;
}

// Verificação de conflito
export interface VerificacaoConflito {
  tem_conflito: boolean;
  conflitos: ConflitoHorario[];
  pode_agendar: boolean;
  sugestoes_alternativas?: HorarioDisponivel[];
}

// Configurações de disponibilidade
export interface ConfiguracoesDisponibilidade {
  empresa_id: number;
  intervalo_agendamento: number; // Minutos
  antecedencia_minima: number; // Horas
  antecedencia_maxima: number; // Dias
  permitir_agendamento_feriados: boolean;
  permitir_agendamento_finais_semana: boolean;
  horario_limite_agendamento_mesmo_dia?: string; // HH:mm
}

// Estatísticas de disponibilidade
export interface EstatisticasDisponibilidade {
  empresa_id: number;
  periodo: {
    data_inicio: string;
    data_fim: string;
  };
  total_slots_possiveis: number;
  total_slots_disponiveis: number;
  total_slots_ocupados: number;
  taxa_ocupacao: number; // Percentual
  por_colaborador: Array<{
    colaborador_user_id: string;
    nome: string;
    slots_disponiveis: number;
    slots_ocupados: number;
    taxa_ocupacao: number;
  }>;
  por_dia_semana: Array<{
    dia_semana: number;
    nome_dia: string;
    slots_disponiveis: number;
    slots_ocupados: number;
    taxa_ocupacao: number;
  }>;
}

// Resposta da API de disponibilidade
export interface DisponibilidadeApiResponse {
  success: boolean;
  data?: CalendarioDisponibilidade | DiaDisponibilidade[] | HorarioDisponivel[] | VerificacaoConflito;
  error?: string;
  message?: string;
}
