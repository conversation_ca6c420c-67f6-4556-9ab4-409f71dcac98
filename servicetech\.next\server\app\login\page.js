/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5CSkipLinks.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Conboarding%5C%5COnboardingTour.tsx%22%2C%22ids%22%3A%5B%22OnboardingTour%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CPWAPrompt.tsx%22%2C%22ids%22%3A%5B%22PWAPrompt%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CBrandingContext.tsx%22%2C%22ids%22%3A%5B%22BrandingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5COnboardingContext.tsx%22%2C%22ids%22%3A%5B%22OnboardingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5CSkipLinks.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Conboarding%5C%5COnboardingTour.tsx%22%2C%22ids%22%3A%5B%22OnboardingTour%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CPWAPrompt.tsx%22%2C%22ids%22%3A%5B%22PWAPrompt%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CBrandingContext.tsx%22%2C%22ids%22%3A%5B%22BrandingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5COnboardingContext.tsx%22%2C%22ids%22%3A%5B%22OnboardingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/SkipLinks.tsx */ \"(rsc)/./src/components/accessibility/SkipLinks.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/onboarding/OnboardingTour.tsx */ \"(rsc)/./src/components/onboarding/OnboardingTour.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/PWAPrompt.tsx */ \"(rsc)/./src/components/ui/PWAPrompt.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/BrandingContext.tsx */ \"(rsc)/./src/contexts/BrandingContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/OnboardingContext.tsx */ \"(rsc)/./src/contexts/OnboardingContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(rsc)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5CSkipLinks.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Conboarding%5C%5COnboardingTour.tsx%22%2C%22ids%22%3A%5B%22OnboardingTour%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CPWAPrompt.tsx%22%2C%22ids%22%3A%5B%22PWAPrompt%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CBrandingContext.tsx%22%2C%22ids%22%3A%5B%22BrandingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5COnboardingContext.tsx%22%2C%22ids%22%3A%5B%22OnboardingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXRvcyU1QyU1QzElNUMlNUNnZXJlbWlhcyU1QyU1Q3NlcnZpY2V0ZWNoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRvc1xcXFwxXFxcXGdlcmVtaWFzXFxcXHNlcnZpY2V0ZWNoXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e6831ec61a27\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTY4MzFlYzYxYTI3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_BrandingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/BrandingContext */ \"(rsc)/./src/contexts/BrandingContext.tsx\");\n/* harmony import */ var _contexts_OnboardingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/OnboardingContext */ \"(rsc)/./src/contexts/OnboardingContext.tsx\");\n/* harmony import */ var _components_accessibility_SkipLinks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/accessibility/SkipLinks */ \"(rsc)/./src/components/accessibility/SkipLinks.tsx\");\n/* harmony import */ var _components_ui_PWAPrompt__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/PWAPrompt */ \"(rsc)/./src/components/ui/PWAPrompt.tsx\");\n/* harmony import */ var _components_onboarding_OnboardingTour__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/onboarding/OnboardingTour */ \"(rsc)/./src/components/onboarding/OnboardingTour.tsx\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"ServiceTech\",\n    description: \"Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais\",\n    keywords: \"agendamento, serviços pessoais, barbearia, salão, estética\",\n    authors: [\n        {\n            name: \"ServiceTech Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"ServiceTech\",\n        description: \"Plataforma de Agendamento e Gestão para Estabelecimentos de Serviços Pessoais\",\n        type: \"website\"\n    },\n    manifest: '/manifest.json',\n    appleWebApp: {\n        capable: true,\n        statusBarStyle: 'default',\n        title: 'ServiceTech'\n    },\n    other: {\n        'mobile-web-app-capable': 'yes',\n        'apple-mobile-web-app-capable': 'yes',\n        'apple-mobile-web-app-status-bar-style': 'default',\n        'apple-mobile-web-app-title': 'ServiceTech',\n        'application-name': 'ServiceTech',\n        'msapplication-TileColor': '#3B82F6',\n        'theme-color': '#3B82F6'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_9___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_10___default().variable)} antialiased`,\n            suppressHydrationWarning: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_accessibility_SkipLinks__WEBPACK_IMPORTED_MODULE_6__.SkipLinks, {}, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_BrandingContext__WEBPACK_IMPORTED_MODULE_4__.BrandingProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_OnboardingContext__WEBPACK_IMPORTED_MODULE_5__.OnboardingProvider, {\n                                children: [\n                                    children,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PWAPrompt__WEBPACK_IMPORTED_MODULE_7__.PWAPrompt, {}, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_onboarding_OnboardingTour__WEBPACK_IMPORTED_MODULE_8__.OnboardingTour, {}, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/accessibility/SkipLinks.tsx":
/*!****************************************************!*\
  !*** ./src/components/accessibility/SkipLinks.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),
/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),
/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const SkipLinks = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SkipLinks() from the server but SkipLinks is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\accessibility\\SkipLinks.tsx",
"SkipLinks",
);const useFocusManagement = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useFocusManagement() from the server but useFocusManagement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\accessibility\\SkipLinks.tsx",
"useFocusManagement",
);const LiveRegion = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LiveRegion() from the server but LiveRegion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\accessibility\\SkipLinks.tsx",
"LiveRegion",
);

/***/ }),

/***/ "(rsc)/./src/components/onboarding/OnboardingTour.tsx":
/*!******************************************************!*\
  !*** ./src/components/onboarding/OnboardingTour.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnboardingTour: () => (/* binding */ OnboardingTour)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const OnboardingTour = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call OnboardingTour() from the server but OnboardingTour is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\onboarding\\OnboardingTour.tsx",
"OnboardingTour",
);

/***/ }),

/***/ "(rsc)/./src/components/ui/PWAPrompt.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/PWAPrompt.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PWAPrompt: () => (/* binding */ PWAPrompt),
/* harmony export */   PWAStatus: () => (/* binding */ PWAStatus)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const PWAPrompt = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PWAPrompt() from the server but PWAPrompt is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ui\\PWAPrompt.tsx",
"PWAPrompt",
);const PWAStatus = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PWAStatus() from the server but PWAStatus is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\components\\ui\\PWAPrompt.tsx",
"PWAStatus",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth),
/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),
/* harmony export */   useRole: () => (/* binding */ useRole)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const useRole = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useRole() from the server but useRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\AuthContext.tsx",
"useRole",
);const useRequireAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useRequireAuth() from the server but useRequireAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\AuthContext.tsx",
"useRequireAuth",
);

/***/ }),

/***/ "(rsc)/./src/contexts/BrandingContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/BrandingContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BrandingProvider: () => (/* binding */ BrandingProvider),
/* harmony export */   colorUtils: () => (/* binding */ colorUtils),
/* harmony export */   useBranding: () => (/* binding */ useBranding),
/* harmony export */   useCompanyBranding: () => (/* binding */ useCompanyBranding)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const BrandingProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call BrandingProvider() from the server but BrandingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\BrandingContext.tsx",
"BrandingProvider",
);const useBranding = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useBranding() from the server but useBranding is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\BrandingContext.tsx",
"useBranding",
);const useCompanyBranding = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useCompanyBranding() from the server but useCompanyBranding is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\BrandingContext.tsx",
"useCompanyBranding",
);const colorUtils = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call colorUtils() from the server but colorUtils is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\BrandingContext.tsx",
"colorUtils",
);

/***/ }),

/***/ "(rsc)/./src/contexts/OnboardingContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/OnboardingContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OnboardingProvider: () => (/* binding */ OnboardingProvider),
/* harmony export */   useOnboarding: () => (/* binding */ useOnboarding)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const OnboardingProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call OnboardingProvider() from the server but OnboardingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\OnboardingContext.tsx",
"OnboardingProvider",
);const useOnboarding = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useOnboarding() from the server but useOnboarding is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\OnboardingContext.tsx",
"useOnboarding",
);

/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projetos\\1\\geremias\\servicetech\\src\\contexts\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5CSkipLinks.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Conboarding%5C%5COnboardingTour.tsx%22%2C%22ids%22%3A%5B%22OnboardingTour%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CPWAPrompt.tsx%22%2C%22ids%22%3A%5B%22PWAPrompt%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CBrandingContext.tsx%22%2C%22ids%22%3A%5B%22BrandingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5COnboardingContext.tsx%22%2C%22ids%22%3A%5B%22OnboardingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5CSkipLinks.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Conboarding%5C%5COnboardingTour.tsx%22%2C%22ids%22%3A%5B%22OnboardingTour%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CPWAPrompt.tsx%22%2C%22ids%22%3A%5B%22PWAPrompt%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CBrandingContext.tsx%22%2C%22ids%22%3A%5B%22BrandingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5COnboardingContext.tsx%22%2C%22ids%22%3A%5B%22OnboardingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/accessibility/SkipLinks.tsx */ \"(ssr)/./src/components/accessibility/SkipLinks.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/onboarding/OnboardingTour.tsx */ \"(ssr)/./src/components/onboarding/OnboardingTour.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/PWAPrompt.tsx */ \"(ssr)/./src/components/ui/PWAPrompt.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/BrandingContext.tsx */ \"(ssr)/./src/contexts/BrandingContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/OnboardingContext.tsx */ \"(ssr)/./src/contexts/OnboardingContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Caccessibility%5C%5CSkipLinks.tsx%22%2C%22ids%22%3A%5B%22SkipLinks%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Conboarding%5C%5COnboardingTour.tsx%22%2C%22ids%22%3A%5B%22OnboardingTour%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CPWAPrompt.tsx%22%2C%22ids%22%3A%5B%22PWAPrompt%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CBrandingContext.tsx%22%2C%22ids%22%3A%5B%22BrandingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5COnboardingContext.tsx%22%2C%22ids%22%3A%5B%22OnboardingProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZXRvcyU1QyU1QzElNUMlNUNnZXJlbWlhcyU1QyU1Q3NlcnZpY2V0ZWNoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZXRvc1xcXFwxXFxcXGdlcmVtaWFzXFxcXHNlcnZpY2V0ZWNoXFxcXHNyY1xcXFxhcHBcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjetos%5C%5C1%5C%5Cgeremias%5C%5Cservicetech%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LoginContent() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { signIn, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // URL de redirecionamento após login\n    const redirectUrl = searchParams.get('redirect') || '/';\n    // Redirecionar se já estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginContent.useEffect\": ()=>{\n            if (user) {\n                router.push(redirectUrl);\n            }\n        }\n    }[\"LoginContent.useEffect\"], [\n        user,\n        router,\n        redirectUrl\n    ]);\n    // Validação do formulário\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Validar e-mail\n        if (!email.trim()) {\n            newErrors.email = 'E-mail é obrigatório';\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email)) {\n            newErrors.email = 'E-mail inválido';\n        }\n        // Validar senha\n        if (!password) {\n            newErrors.password = 'Senha é obrigatória';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    // Submissão do formulário\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setLoading(true);\n        setMessage('');\n        try {\n            const { error } = await signIn(email, password);\n            if (error) {\n                setMessage(error.message || 'Erro ao fazer login. Verifique suas credenciais.');\n            }\n        // Se não houver erro, o useEffect acima irá redirecionar\n        } catch (error) {\n            setMessage('Erro inesperado. Tente novamente.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Limpar erros quando o usuário começar a digitar\n    const handleEmailChange = (value)=>{\n        setEmail(value);\n        if (errors.email) {\n            setErrors((prev)=>({\n                    ...prev,\n                    email: undefined\n                }));\n        }\n    };\n    const handlePasswordChange = (value)=>{\n        setPassword(value);\n        if (errors.password) {\n            setErrors((prev)=>({\n                    ...prev,\n                    password: undefined\n                }));\n        }\n    };\n    if (user) {\n        return null; // Não renderizar se já estiver logado\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-[var(--background)] p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            className: \"text-2xl font-bold text-center text-[var(--text-primary)]\",\n                            children: \"Entrar\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-[var(--text-secondary)] text-sm\",\n                            children: \"Fa\\xe7a login em sua conta\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-[var(--text-primary)] mb-1\",\n                                            children: \"E-mail\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"email\",\n                                            id: \"email\",\n                                            value: email,\n                                            onChange: (e)=>handleEmailChange(e.target.value),\n                                            placeholder: \"Digite seu e-mail\",\n                                            className: errors.email ? 'border-red-500' : ''\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-[var(--text-primary)] mb-1\",\n                                            children: \"Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"password\",\n                                            id: \"password\",\n                                            value: password,\n                                            onChange: (e)=>handlePasswordChange(e.target.value),\n                                            placeholder: \"Digite sua senha\",\n                                            className: errors.password ? 'border-red-500' : ''\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-full\",\n                                    disabled: loading,\n                                    children: loading ? 'Entrando...' : 'Entrar'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 rounded-md text-sm text-center bg-red-50 text-red-700 border border-red-200\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                    className: \"flex flex-col items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/recuperar-senha\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-[var(--primary)] hover:text-[var(--primary-hover)] cursor-pointer\",\n                                    children: \"Esqueci minha senha\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[var(--text-secondary)]\",\n                                    children: \"N\\xe3o tem uma conta? \"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/cadastro\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-[var(--primary)] hover:text-[var(--primary-hover)] cursor-pointer\",\n                                        children: \"Cadastre-se\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction LoginPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-[var(--background)] p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[var(--primary)] mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-[var(--text-secondary)]\",\n                        children: \"Carregando...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginContent, {}, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/accessibility/SkipLinks.tsx":
/*!****************************************************!*\
  !*** ./src/components/accessibility/SkipLinks.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   SkipLinks: () => (/* binding */ SkipLinks),\n/* harmony export */   useFocusManagement: () => (/* binding */ useFocusManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SkipLinks,useFocusManagement,LiveRegion auto */ \n\n\nconst defaultLinks = [\n    {\n        href: '#main-content',\n        label: 'Pular para o conteúdo principal'\n    },\n    {\n        href: '#main-navigation',\n        label: 'Pular para a navegação principal'\n    },\n    {\n        href: '#footer',\n        label: 'Pular para o rodapé'\n    }\n];\nfunction SkipLinks({ links = defaultLinks }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sr-only focus-within:not-sr-only\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            \"aria-label\": \"Links de navega\\xe7\\xe3o r\\xe1pida\",\n            className: \"fixed top-0 left-0 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"flex flex-col\",\n                children: links.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: link.href,\n                            className: \"   block px-4 py-2 bg-[var(--primary)] text-[var(--text-on-primary)]    font-medium text-sm border-b border-[var(--primary-hover)]   focus:outline-none focus:ring-2 focus:ring-[var(--accent)]    focus:ring-offset-2 hover:bg-[var(--primary-hover)]   transition-colors duration-200   \",\n                            onKeyDown: (e)=>{\n                                if (e.key === 'Enter' || e.key === ' ') {\n                                    e.preventDefault();\n                                    const target = document.querySelector(link.href);\n                                    if (target) {\n                                        target.scrollIntoView({\n                                            behavior: 'smooth'\n                                        });\n                                        // Focar no elemento se ele for focável\n                                        if (target instanceof HTMLElement && target.tabIndex >= 0) {\n                                            target.focus();\n                                        }\n                                    }\n                                }\n                            },\n                            children: link.label\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\accessibility\\\\SkipLinks.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\accessibility\\\\SkipLinks.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\accessibility\\\\SkipLinks.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\accessibility\\\\SkipLinks.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\accessibility\\\\SkipLinks.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n// Hook para gerenciar foco em elementos\nfunction useFocusManagement() {\n    const focusElement = (selector)=>{\n        const element = document.querySelector(selector);\n        if (element) {\n            element.focus();\n            element.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n        }\n    };\n    const trapFocus = (containerRef)=>{\n        const container = containerRef.current;\n        if (!container) return;\n        const focusableElements = container.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n        const firstElement = focusableElements[0];\n        const lastElement = focusableElements[focusableElements.length - 1];\n        const handleKeyDown = (e)=>{\n            if (e.key === 'Tab') {\n                if (e.shiftKey) {\n                    if (document.activeElement === firstElement) {\n                        e.preventDefault();\n                        lastElement.focus();\n                    }\n                } else {\n                    if (document.activeElement === lastElement) {\n                        e.preventDefault();\n                        firstElement.focus();\n                    }\n                }\n            }\n            if (e.key === 'Escape') {\n                const closeButton = container.querySelector('[data-close]');\n                if (closeButton) {\n                    closeButton.click();\n                }\n            }\n        };\n        container.addEventListener('keydown', handleKeyDown);\n        // Focar no primeiro elemento focável\n        if (firstElement) {\n            firstElement.focus();\n        }\n        return ()=>{\n            container.removeEventListener('keydown', handleKeyDown);\n        };\n    };\n    return {\n        focusElement,\n        trapFocus\n    };\n}\n// Componente para anunciar mudanças para screen readers\nfunction LiveRegion({ message, level = 'polite' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"aria-live\": level,\n        \"aria-atomic\": \"true\",\n        className: \"sr-only\",\n        role: \"status\",\n        children: message\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\accessibility\\\\SkipLinks.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/accessibility/SkipLinks.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/onboarding/OnboardingTour.tsx":
/*!******************************************************!*\
  !*** ./src/components/onboarding/OnboardingTour.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnboardingTour: () => (/* binding */ OnboardingTour)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_OnboardingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/OnboardingContext */ \"(ssr)/./src/contexts/OnboardingContext.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ OnboardingTour auto */ \n\n\n\nfunction OnboardingTour() {\n    const { isActive, currentStep, currentStepIndex, currentFlow, nextStep, previousStep, skipStep, finishOnboarding } = (0,_contexts_OnboardingContext__WEBPACK_IMPORTED_MODULE_2__.useOnboarding)();\n    const [tooltipPosition, setTooltipPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [highlightPosition, setHighlightPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Calcular posição do tooltip e highlight\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingTour.useEffect\": ()=>{\n            if (!isActive || !currentStep?.target) {\n                setTooltipPosition(null);\n                setHighlightPosition(null);\n                return;\n            }\n            const updatePosition = {\n                \"OnboardingTour.useEffect.updatePosition\": ()=>{\n                    const targetElement = document.querySelector(currentStep.target);\n                    if (!targetElement) {\n                        console.warn(`Elemento alvo não encontrado: ${currentStep.target}`);\n                        return;\n                    }\n                    const rect = targetElement.getBoundingClientRect();\n                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n                    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\n                    // Posição do highlight (elemento alvo)\n                    const highlight = {\n                        top: rect.top + scrollTop,\n                        left: rect.left + scrollLeft,\n                        width: rect.width,\n                        height: rect.height\n                    };\n                    setHighlightPosition(highlight);\n                    // Posição do tooltip baseada na posição preferida\n                    const tooltipWidth = 320;\n                    const tooltipHeight = 200; // Estimativa\n                    const spacing = 16;\n                    let tooltipTop = 0;\n                    let tooltipLeft = 0;\n                    switch(currentStep.position){\n                        case 'top':\n                            tooltipTop = highlight.top - tooltipHeight - spacing;\n                            tooltipLeft = highlight.left + highlight.width / 2 - tooltipWidth / 2;\n                            break;\n                        case 'bottom':\n                            tooltipTop = highlight.top + highlight.height + spacing;\n                            tooltipLeft = highlight.left + highlight.width / 2 - tooltipWidth / 2;\n                            break;\n                        case 'left':\n                            tooltipTop = highlight.top + highlight.height / 2 - tooltipHeight / 2;\n                            tooltipLeft = highlight.left - tooltipWidth - spacing;\n                            break;\n                        case 'right':\n                            tooltipTop = highlight.top + highlight.height / 2 - tooltipHeight / 2;\n                            tooltipLeft = highlight.left + highlight.width + spacing;\n                            break;\n                        default:\n                            // Centro da tela\n                            tooltipTop = window.innerHeight / 2 - tooltipHeight / 2 + scrollTop;\n                            tooltipLeft = window.innerWidth / 2 - tooltipWidth / 2;\n                    }\n                    // Ajustar para não sair da tela\n                    const maxLeft = window.innerWidth - tooltipWidth - spacing;\n                    const maxTop = window.innerHeight - tooltipHeight - spacing + scrollTop;\n                    tooltipLeft = Math.max(spacing, Math.min(tooltipLeft, maxLeft));\n                    tooltipTop = Math.max(spacing + scrollTop, Math.min(tooltipTop, maxTop));\n                    setTooltipPosition({\n                        top: tooltipTop,\n                        left: tooltipLeft,\n                        width: tooltipWidth,\n                        height: tooltipHeight\n                    });\n                    // Scroll para o elemento se necessário\n                    if (rect.top < 0 || rect.bottom > window.innerHeight) {\n                        targetElement.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'center'\n                        });\n                    }\n                }\n            }[\"OnboardingTour.useEffect.updatePosition\"];\n            // Delay para garantir que o DOM foi atualizado\n            const timer = setTimeout(updatePosition, 100);\n            // Atualizar posição quando a janela for redimensionada\n            window.addEventListener('resize', updatePosition);\n            window.addEventListener('scroll', updatePosition);\n            return ({\n                \"OnboardingTour.useEffect\": ()=>{\n                    clearTimeout(timer);\n                    window.removeEventListener('resize', updatePosition);\n                    window.removeEventListener('scroll', updatePosition);\n                }\n            })[\"OnboardingTour.useEffect\"];\n        }\n    }[\"OnboardingTour.useEffect\"], [\n        isActive,\n        currentStep\n    ]);\n    // Não renderizar se não estiver ativo\n    if (!isActive || !currentStep || !currentFlow) {\n        return null;\n    }\n    const isFirstStep = currentStepIndex === 0;\n    const isLastStep = currentStepIndex === currentFlow.steps.length - 1;\n    const hasTarget = !!currentStep.target;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40\",\n                style: {\n                    pointerEvents: hasTarget ? 'auto' : 'none'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            highlightPosition && hasTarget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed z-50 pointer-events-none\",\n                style: {\n                    top: highlightPosition.top - 4,\n                    left: highlightPosition.left - 4,\n                    width: highlightPosition.width + 8,\n                    height: highlightPosition.height + 8,\n                    border: '2px solid var(--primary)',\n                    borderRadius: '8px',\n                    boxShadow: '0 0 0 4px rgba(59, 130, 246, 0.3)',\n                    background: 'transparent'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: tooltipRef,\n                className: \"fixed z-50 bg-[var(--surface)] border border-[var(--border-color)] rounded-lg shadow-xl p-6 max-w-sm\",\n                style: {\n                    top: tooltipPosition?.top || '50%',\n                    left: tooltipPosition?.left || '50%',\n                    transform: tooltipPosition ? 'none' : 'translate(-50%, -50%)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-[var(--text-primary)] mb-1\",\n                                        children: currentStep.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-[var(--text-secondary)]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: currentFlow.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    currentStepIndex + 1,\n                                                    \" de \",\n                                                    currentFlow.steps.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: finishOnboarding,\n                                className: \"text-[var(--text-secondary)] hover:text-[var(--text-primary)] p-1\",\n                                \"aria-label\": \"Fechar tour\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-[var(--text-primary)] leading-relaxed\",\n                            children: currentStep.description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-[var(--text-secondary)]\",\n                                        children: \"Progresso\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-[var(--text-secondary)]\",\n                                        children: [\n                                            Math.round((currentStepIndex + 1) / currentFlow.steps.length * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-[var(--border-color)] rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-[var(--primary)] h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: `${(currentStepIndex + 1) / currentFlow.steps.length * 100}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    !isFirstStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: previousStep,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        children: \"Anterior\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: skipStep,\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"text-[var(--text-secondary)]\",\n                                        children: \"Pular\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: isLastStep ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: finishOnboarding,\n                                    variant: \"primary\",\n                                    size: \"sm\",\n                                    children: \"Concluir\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: nextStep,\n                                    variant: \"primary\",\n                                    size: \"sm\",\n                                    children: \"Pr\\xf3ximo\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    hasTarget && currentStep.position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute w-3 h-3 bg-[var(--surface)] border-[var(--border-color)] transform rotate-45 ${currentStep.position === 'top' ? 'bottom-[-6px] left-1/2 -translate-x-1/2 border-b border-r' : currentStep.position === 'bottom' ? 'top-[-6px] left-1/2 -translate-x-1/2 border-t border-l' : currentStep.position === 'left' ? 'right-[-6px] top-1/2 -translate-y-1/2 border-t border-r' : currentStep.position === 'right' ? 'left-[-6px] top-1/2 -translate-y-1/2 border-b border-l' : 'hidden'}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\onboarding\\\\OnboardingTour.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/onboarding/OnboardingTour.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center justify-center rounded-md text-sm font-medium font-[var(--font-geist-sans)] ring-offset-[var(--background)] transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2', {\n    variants: {\n        variant: {\n            primary: 'bg-[var(--primary)] text-[var(--text-on-primary)] hover:bg-[var(--primary-hover)] focus-visible:ring-[var(--primary)] focus:ring-[var(--primary)]',\n            secondary: 'bg-[var(--secondary)] text-[var(--text-on-primary)] hover:bg-[var(--secondary)]/80 focus-visible:ring-[var(--secondary)] focus:ring-[var(--secondary)]',\n            accent: 'bg-[var(--accent)] text-[var(--text-on-accent)] hover:bg-[var(--accent-hover)] focus-visible:ring-[var(--accent)] focus:ring-[var(--accent)]',\n            outline: 'border border-[var(--primary)] bg-[var(--background)] text-[var(--primary)] hover:bg-[var(--primary)] hover:text-[var(--text-on-primary)] focus-visible:ring-[var(--primary)] focus:ring-[var(--primary)]',\n            ghost: 'text-[var(--primary)] hover:bg-[var(--primary)]/10 focus-visible:ring-[var(--primary)] focus:ring-[var(--primary)]',\n            link: 'text-[var(--primary)] underline-offset-4 hover:underline focus-visible:ring-[var(--primary)] focus:ring-[var(--primary)]'\n        },\n        size: {\n            sm: 'h-9 rounded-md px-3 text-sm',\n            md: 'h-10 px-4 py-2 text-base',\n            lg: 'h-11 rounded-md px-8 text-lg',\n            icon: 'h-10 w-10'\n        }\n    },\n    defaultVariants: {\n        variant: 'primary',\n        size: 'md'\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant, size, children, loading, loadingText, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_3__.twMerge)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: loading || props.disabled,\n        \"aria-busy\": loading,\n        ...props,\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: \"flex items-center gap-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin h-4 w-4\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: loadingText ?? 'Carregando...'\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n            lineNumber: 60,\n            columnNumber: 11\n        }, undefined) : children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 52,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardTitle,CardDescription,CardContent,CardFooter auto */ \n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('rounded-lg border border-[var(--border-color)] bg-[var(--surface)] text-[var(--text-primary)] shadow-md transition-shadow duration-300 ease-in-out hover:shadow-lg', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 10,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = 'Card';\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('flex flex-col space-y-1.5 p-6', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = 'CardHeader';\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\" // Using h3 as a sensible default for card titles\n    , {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('text-2xl font-semibold leading-none tracking-tight text-[var(--text-primary)]', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = 'CardTitle';\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('text-sm text-[var(--text-secondary)]', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = 'CardDescription';\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('p-6 pt-0', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 64,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = 'CardContent';\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('flex items-center p-6 pt-0', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 72,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = 'CardFooter';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* __next_internal_client_entry_do_not_use__ Input auto */ \n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, label, error, helperText, ...props }, ref)=>{\n    const inputId = props.id ?? `input-${Math.random().toString(36).substring(2, 11)}`;\n    const errorId = error ? `${inputId}-error` : undefined;\n    const helperId = helperText ? `${inputId}-helper` : undefined;\n    const describedBy = [\n        errorId,\n        helperId,\n        props['aria-describedby']\n    ].filter(Boolean).join(' ');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: inputId,\n                className: \"block text-sm font-medium text-[var(--text-primary)] mb-2\",\n                children: [\n                    label,\n                    props.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[var(--error)] ml-1\",\n                        \"aria-label\": \"obrigat\\xf3rio\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 26,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ...props,\n                id: inputId,\n                type: type,\n                className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)('flex h-10 w-full rounded-md border bg-[var(--background)] px-3 py-2 text-sm font-[var(--font-geist-sans)] text-[var(--text-primary)] border-[var(--border-color)] ring-offset-[var(--background)] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-[var(--text-secondary)] focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--primary)] focus-visible:ring-offset-2 focus:outline-none focus:ring-2 focus:ring-[var(--primary)] focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors', error && 'border-[var(--error)] focus:ring-[var(--error)] focus-visible:ring-[var(--error)]', className),\n                ref: ref,\n                \"aria-invalid\": error ? true : props['aria-invalid'],\n                \"aria-describedby\": describedBy || undefined\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 36,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: errorId,\n                className: \"text-[var(--error)] text-sm mt-1\",\n                role: \"alert\",\n                \"aria-live\": \"polite\",\n                children: error\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 50,\n                columnNumber: 11\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                id: helperId,\n                className: \"text-[var(--text-secondary)] text-sm mt-1\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 60,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 24,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = 'Input';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/PWAPrompt.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/PWAPrompt.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PWAPrompt: () => (/* binding */ PWAPrompt),\n/* harmony export */   PWAStatus: () => (/* binding */ PWAStatus)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_usePWA__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/usePWA */ \"(ssr)/./src/hooks/usePWA.ts\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ PWAPrompt,PWAStatus auto */ \n\n\n\nfunction PWAPrompt({ className = '' }) {\n    const { isInstallable, isInstalled, isOffline, installApp, updateAvailable, updateApp } = (0,_hooks_usePWA__WEBPACK_IMPORTED_MODULE_2__.usePWA)();\n    const [isInstalling, setIsInstalling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPrompt, setShowPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleInstall = async ()=>{\n        if (!isInstallable) return;\n        setIsInstalling(true);\n        try {\n            await installApp();\n            setShowPrompt(false);\n        } catch (error) {\n            console.error('Erro ao instalar PWA:', error);\n        } finally{\n            setIsInstalling(false);\n        }\n    };\n    const handleUpdate = ()=>{\n        updateApp();\n    };\n    const handleDismiss = ()=>{\n        setShowPrompt(false);\n        // Salvar no localStorage para não mostrar novamente por um tempo\n        localStorage.setItem('pwa-prompt-dismissed', Date.now().toString());\n    };\n    // Verificar se o prompt foi dispensado recentemente (24 horas)\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"PWAPrompt.useEffect\": ()=>{\n            const dismissed = localStorage.getItem('pwa-prompt-dismissed');\n            if (dismissed) {\n                const dismissedTime = parseInt(dismissed);\n                const now = Date.now();\n                const dayInMs = 24 * 60 * 60 * 1000;\n                if (now - dismissedTime < dayInMs) {\n                    setShowPrompt(false);\n                }\n            }\n        }\n    }[\"PWAPrompt.useEffect\"], []);\n    // Não mostrar se já está instalado ou não é instalável\n    if (isInstalled || !isInstallable && !updateAvailable || !showPrompt) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-[var(--surface)] border border-[var(--border-color)] rounded-lg shadow-lg p-4\",\n            children: [\n                isOffline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3 p-2 bg-[var(--warning)] bg-opacity-10 border border-[var(--warning)] rounded-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-4 h-4 text-[var(--warning)]\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                \"aria-hidden\": \"true\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-[var(--warning)]\",\n                                children: \"Voc\\xea est\\xe1 offline. Algumas funcionalidades podem estar limitadas.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 11\n                }, this),\n                updateAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-semibold text-[var(--text-primary)] mb-2\",\n                            children: \"Atualiza\\xe7\\xe3o Dispon\\xedvel\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-[var(--text-secondary)] mb-3\",\n                            children: \"Uma nova vers\\xe3o do ServiceTech est\\xe1 dispon\\xedvel. Atualize para ter acesso \\xe0s \\xfaltimas funcionalidades.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleUpdate,\n                                    size: \"sm\",\n                                    variant: \"primary\",\n                                    className: \"flex-1\",\n                                    children: \"Atualizar\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleDismiss,\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    children: \"Depois\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this),\n                isInstallable && !updateAvailable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start space-x-3 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8 text-[var(--primary)]\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        \"aria-hidden\": \"true\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-[var(--text-primary)] mb-1\",\n                                            children: \"Instalar ServiceTech\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-[var(--text-secondary)]\",\n                                            children: \"Instale o app para acesso r\\xe1pido, notifica\\xe7\\xf5es e uso offline.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleInstall,\n                                    disabled: isInstalling,\n                                    size: \"sm\",\n                                    variant: \"primary\",\n                                    className: \"flex-1\",\n                                    children: isInstalling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 animate-spin\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                \"aria-hidden\": \"true\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Instalando...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 19\n                                    }, this) : 'Instalar'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleDismiss,\n                                    size: \"sm\",\n                                    variant: \"ghost\",\n                                    children: \"N\\xe3o agora\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n// Componente para mostrar status PWA no header\nfunction PWAStatus() {\n    const { isInstalled, isOffline } = (0,_hooks_usePWA__WEBPACK_IMPORTED_MODULE_2__.usePWA)();\n    if (!isInstalled && !isOffline) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [\n            isInstalled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1 px-2 py-1 bg-[var(--success)] bg-opacity-10 text-[var(--success)] rounded-md\",\n                title: \"App instalado\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-3 h-3\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        \"aria-hidden\": \"true\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-medium\",\n                        children: \"PWA\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this),\n            isOffline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1 px-2 py-1 bg-[var(--warning)] bg-opacity-10 text-[var(--warning)] rounded-md\",\n                title: \"Modo offline\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-3 h-3\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        \"aria-hidden\": \"true\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-medium\",\n                        children: \"Offline\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n                lineNumber: 229,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\components\\\\ui\\\\PWAPrompt.tsx\",\n        lineNumber: 206,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/PWAPrompt.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth),\n/* harmony export */   useRole: () => (/* binding */ useRole)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/supabase/client */ \"(ssr)/./src/utils/supabase/client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,useRole,useRequireAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        user: null,\n        session: null,\n        loading: true,\n        initialized: false\n    });\n    const supabase = (0,_utils_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n    // Função para extrair perfil do usuário\n    const extractUserProfile = (user)=>{\n        return {\n            id: user.id,\n            email: user.email || '',\n            name: user.user_metadata?.name || user.user_metadata?.full_name || '',\n            phone: user.user_metadata?.phone || '',\n            role: user.user_metadata?.role || 'Usuario',\n            created_at: user.created_at,\n            updated_at: user.updated_at || user.created_at,\n            pagamento_confirmado: user.user_metadata?.pagamento_confirmado || false,\n            onboarding_pendente: user.user_metadata?.onboarding_pendente || false,\n            plano_selecionado: user.user_metadata?.plano_selecionado || null\n        };\n    };\n    // Função para atualizar o estado de autenticação\n    const updateAuthState = (session)=>{\n        if (session?.user) {\n            const userProfile = extractUserProfile(session.user);\n            setAuthState({\n                user: userProfile,\n                session,\n                loading: false,\n                initialized: true\n            });\n        } else {\n            setAuthState({\n                user: null,\n                session: null,\n                loading: false,\n                initialized: true\n            });\n        }\n    };\n    // Inicializar autenticação\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            let mounted = true;\n            // Obter sessão inicial\n            const getInitialSession = {\n                \"AuthProvider.useEffect.getInitialSession\": async ()=>{\n                    try {\n                        const { data: { session }, error } = await supabase.auth.getSession();\n                        if (error) {\n                            console.error('Erro ao obter sessão:', error);\n                        }\n                        if (mounted) {\n                            updateAuthState(session);\n                        }\n                    } catch (error) {\n                        console.error('Erro ao inicializar autenticação:', error);\n                        if (mounted) {\n                            setAuthState({\n                                \"AuthProvider.useEffect.getInitialSession\": (prev)=>({\n                                        ...prev,\n                                        loading: false,\n                                        initialized: true\n                                    })\n                            }[\"AuthProvider.useEffect.getInitialSession\"]);\n                        }\n                    }\n                }\n            }[\"AuthProvider.useEffect.getInitialSession\"];\n            getInitialSession();\n            // Escutar mudanças de autenticação\n            const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    console.log('Auth state change:', event, session?.user?.user_metadata);\n                    if (mounted) {\n                        updateAuthState(session);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    mounted = false;\n                    subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        supabase.auth\n    ]);\n    // Função de login\n    const signIn = async (email, password)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            const { error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            return {\n                error\n            };\n        } catch (error) {\n            return {\n                error\n            };\n        }\n    };\n    // Função de cadastro\n    const signUp = async (email, password, userData)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            const { error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        name: userData.name,\n                        full_name: userData.name,\n                        phone: userData.phone,\n                        role: userData.role || 'Usuario'\n                    }\n                }\n            });\n            return {\n                error\n            };\n        } catch (error) {\n            return {\n                error\n            };\n        }\n    };\n    // Função de logout\n    const signOut = async ()=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true\n                }));\n            await supabase.auth.signOut();\n        } catch (error) {\n            console.error('Erro ao fazer logout:', error);\n        }\n    };\n    // Função de recuperação de senha\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/callback?next=/reset-password`\n            });\n            return {\n                error\n            };\n        } catch (error) {\n            return {\n                error\n            };\n        }\n    };\n    // Função para atualizar perfil\n    const updateProfile = async (updates)=>{\n        try {\n            const { error } = await supabase.auth.updateUser({\n                data: updates\n            });\n            return {\n                error\n            };\n        } catch (error) {\n            return {\n                error\n            };\n        }\n    };\n    // Função para forçar atualização dos dados do usuário\n    const refreshUser = async ()=>{\n        try {\n            const { data: { session }, error } = await supabase.auth.getSession();\n            if (error) {\n                console.error('Erro ao atualizar sessão:', error);\n            } else {\n                updateAuthState(session);\n            }\n        } catch (error) {\n            console.error('Erro ao forçar atualização do usuário:', error);\n        }\n    };\n    const value = {\n        ...authState,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n// Hook para usar o contexto de autenticação\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth deve ser usado dentro de um AuthProvider');\n    }\n    return context;\n}\n// Hook para verificar se o usuário tem um papel específico\nfunction useRole(requiredRole) {\n    const { user } = useAuth();\n    if (!user) return false;\n    if (Array.isArray(requiredRole)) {\n        return requiredRole.includes(user.role);\n    }\n    return user.role === requiredRole;\n}\n// Hook para verificar se o usuário está autenticado\nfunction useRequireAuth() {\n    const { user, loading, initialized } = useAuth();\n    return {\n        isAuthenticated: !!user,\n        isLoading: loading || !initialized,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BrandingContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/BrandingContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrandingProvider: () => (/* binding */ BrandingProvider),\n/* harmony export */   colorUtils: () => (/* binding */ colorUtils),\n/* harmony export */   useBranding: () => (/* binding */ useBranding),\n/* harmony export */   useCompanyBranding: () => (/* binding */ useCompanyBranding)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BrandingProvider,useBranding,useCompanyBranding,colorUtils auto */ \n\nconst BrandingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Cores padrão do ServiceTech\nconst DEFAULT_COLORS = {\n    primary: '#3B82F6',\n    primaryHover: '#2563EB',\n    secondary: '#6B7280',\n    accent: '#F59E0B',\n    accentHover: '#D97706'\n};\nconst DEFAULT_BRANDING = {\n    colors: DEFAULT_COLORS,\n    isCustomized: false\n};\nfunction BrandingProvider({ children }) {\n    const [branding, setBrandingState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_BRANDING);\n    const [isPreviewMode, setIsPreviewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [originalColors, setOriginalColors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Carregar configuração salva\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BrandingProvider.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"BrandingProvider.useEffect\"], []);\n    // Aplicar cores ao CSS\n    const applyColorsToCSS = (colors)=>{\n        if (true) return;\n        const root = document.documentElement;\n        root.style.setProperty('--primary', colors.primary);\n        root.style.setProperty('--primary-hover', colors.primaryHover);\n        root.style.setProperty('--secondary', colors.secondary);\n        root.style.setProperty('--accent', colors.accent);\n        root.style.setProperty('--accent-hover', colors.accentHover);\n    };\n    // Salvar configuração\n    const saveBranding = (newBranding)=>{\n        if (false) {}\n    };\n    const setBranding = (updates)=>{\n        const newBranding = {\n            ...branding,\n            ...updates\n        };\n        setBrandingState(newBranding);\n        if (updates.colors) {\n            applyColorsToCSS(newBranding.colors);\n        }\n        saveBranding(newBranding);\n    };\n    const resetBranding = ()=>{\n        setBrandingState(DEFAULT_BRANDING);\n        applyColorsToCSS(DEFAULT_COLORS);\n        if (false) {}\n    };\n    const applyBranding = (colors)=>{\n        const newColors = {\n            ...branding.colors,\n            ...colors\n        };\n        const newBranding = {\n            ...branding,\n            colors: newColors,\n            isCustomized: true\n        };\n        setBrandingState(newBranding);\n        applyColorsToCSS(newColors);\n        saveBranding(newBranding);\n        // Limpar preview se estiver ativo\n        if (isPreviewMode) {\n            setIsPreviewMode(false);\n            setOriginalColors(null);\n        }\n    };\n    const previewBranding = (colors)=>{\n        // Salvar cores originais se não estiver em preview\n        if (!isPreviewMode) {\n            setOriginalColors(branding.colors);\n            setIsPreviewMode(true);\n        }\n        const previewColors = {\n            ...branding.colors,\n            ...colors\n        };\n        applyColorsToCSS(previewColors);\n    };\n    const clearPreview = ()=>{\n        if (isPreviewMode && originalColors) {\n            applyColorsToCSS(originalColors);\n            setIsPreviewMode(false);\n            setOriginalColors(null);\n        }\n    };\n    const value = {\n        branding,\n        setBranding,\n        resetBranding,\n        applyBranding,\n        previewBranding,\n        clearPreview,\n        isPreviewMode\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrandingContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\contexts\\\\BrandingContext.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\nfunction useBranding() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BrandingContext);\n    if (context === undefined) {\n        throw new Error('useBranding must be used within a BrandingProvider');\n    }\n    return context;\n}\n// Hook para aplicar branding de uma empresa específica\nfunction useCompanyBranding(companySlug) {\n    const { setBranding, resetBranding } = useBranding();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useCompanyBranding.useEffect\": ()=>{\n            if (!companySlug) {\n                resetBranding();\n                return;\n            }\n            const loadCompanyBranding = {\n                \"useCompanyBranding.useEffect.loadCompanyBranding\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        // Aqui você faria uma chamada à API para buscar as cores da empresa\n                        const response = await fetch(`/api/empresas/${companySlug}/branding`);\n                        if (response.ok) {\n                            const companyBranding = await response.json();\n                            if (companyBranding.colors) {\n                                setBranding({\n                                    colors: companyBranding.colors,\n                                    logoUrl: companyBranding.logoUrl,\n                                    companyName: companyBranding.companyName,\n                                    isCustomized: true\n                                });\n                            }\n                        } else {\n                            // Se não encontrar branding customizado, usar padrão\n                            resetBranding();\n                        }\n                    } catch (error) {\n                        console.error('Erro ao carregar branding da empresa:', error);\n                        resetBranding();\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useCompanyBranding.useEffect.loadCompanyBranding\"];\n            loadCompanyBranding();\n        }\n    }[\"useCompanyBranding.useEffect\"], [\n        companySlug,\n        setBranding,\n        resetBranding\n    ]);\n    return {\n        isLoading\n    };\n}\n// Utilitários para cores\nconst colorUtils = {\n    // Converter hex para RGB\n    hexToRgb: (hex)=>{\n        const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n        return result ? {\n            r: parseInt(result[1], 16),\n            g: parseInt(result[2], 16),\n            b: parseInt(result[3], 16)\n        } : null;\n    },\n    // Converter RGB para hex\n    rgbToHex: (r, g, b)=>{\n        return \"#\" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);\n    },\n    // Escurecer uma cor\n    darken: (hex, amount = 0.1)=>{\n        const rgb = colorUtils.hexToRgb(hex);\n        if (!rgb) return hex;\n        const r = Math.max(0, Math.floor(rgb.r * (1 - amount)));\n        const g = Math.max(0, Math.floor(rgb.g * (1 - amount)));\n        const b = Math.max(0, Math.floor(rgb.b * (1 - amount)));\n        return colorUtils.rgbToHex(r, g, b);\n    },\n    // Clarear uma cor\n    lighten: (hex, amount = 0.1)=>{\n        const rgb = colorUtils.hexToRgb(hex);\n        if (!rgb) return hex;\n        const r = Math.min(255, Math.floor(rgb.r + (255 - rgb.r) * amount));\n        const g = Math.min(255, Math.floor(rgb.g + (255 - rgb.g) * amount));\n        const b = Math.min(255, Math.floor(rgb.b + (255 - rgb.b) * amount));\n        return colorUtils.rgbToHex(r, g, b);\n    },\n    // Verificar se uma cor é clara ou escura\n    isLight: (hex)=>{\n        const rgb = colorUtils.hexToRgb(hex);\n        if (!rgb) return true;\n        // Calcular luminância\n        const luminance = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;\n        return luminance > 0.5;\n    },\n    // Gerar cor de hover automaticamente\n    generateHoverColor: (hex)=>{\n        return colorUtils.isLight(hex) ? colorUtils.darken(hex, 0.1) : colorUtils.lighten(hex, 0.1);\n    },\n    // Validar se é uma cor hex válida\n    isValidHex: (hex)=>{\n        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BrandingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/OnboardingContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/OnboardingContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnboardingProvider: () => (/* binding */ OnboardingProvider),\n/* harmony export */   useOnboarding: () => (/* binding */ useOnboarding)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ OnboardingProvider,useOnboarding auto */ \n\n\nconst OnboardingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Fluxos de onboarding por papel\nconst ONBOARDING_FLOWS = [\n    {\n        id: 'proprietario-inicial',\n        name: 'Primeiros Passos - Proprietário',\n        description: 'Configure sua empresa e comece a receber agendamentos',\n        role: 'Proprietário',\n        steps: [\n            {\n                id: 'welcome',\n                title: 'Bem-vindo ao ServiceTech!',\n                description: 'Vamos configurar sua empresa em alguns passos simples.'\n            },\n            {\n                id: 'empresa-info',\n                title: 'Informações da Empresa',\n                description: 'Complete as informações básicas da sua empresa.',\n                target: '[data-onboarding=\"empresa-form\"]',\n                position: 'bottom'\n            },\n            {\n                id: 'servicos',\n                title: 'Cadastre seus Serviços',\n                description: 'Adicione os serviços que sua empresa oferece.',\n                target: '[data-onboarding=\"servicos-section\"]',\n                position: 'top'\n            },\n            {\n                id: 'horarios',\n                title: 'Configure os Horários',\n                description: 'Defina os horários de funcionamento.',\n                target: '[data-onboarding=\"horarios-section\"]',\n                position: 'top'\n            },\n            {\n                id: 'colaboradores',\n                title: 'Convide Colaboradores',\n                description: 'Adicione colaboradores à sua equipe.',\n                target: '[data-onboarding=\"colaboradores-section\"]',\n                position: 'top'\n            },\n            {\n                id: 'pagamentos',\n                title: 'Configure Pagamentos',\n                description: 'Configure as formas de pagamento aceitas.',\n                target: '[data-onboarding=\"pagamentos-section\"]',\n                position: 'top'\n            },\n            {\n                id: 'complete',\n                title: 'Configuração Concluída!',\n                description: 'Sua empresa está pronta para receber agendamentos.'\n            }\n        ]\n    },\n    {\n        id: 'colaborador-inicial',\n        name: 'Primeiros Passos - Colaborador',\n        description: 'Aprenda a usar o sistema como colaborador',\n        role: 'Colaborador',\n        steps: [\n            {\n                id: 'welcome',\n                title: 'Bem-vindo à Equipe!',\n                description: 'Vamos te mostrar como usar o sistema.'\n            },\n            {\n                id: 'dashboard',\n                title: 'Seu Dashboard',\n                description: 'Aqui você vê seus agendamentos e estatísticas.',\n                target: '[data-onboarding=\"dashboard\"]',\n                position: 'bottom'\n            },\n            {\n                id: 'agendamentos',\n                title: 'Gerenciar Agendamentos',\n                description: 'Veja e gerencie seus agendamentos aqui.',\n                target: '[data-onboarding=\"agendamentos-list\"]',\n                position: 'top'\n            },\n            {\n                id: 'perfil',\n                title: 'Seu Perfil',\n                description: 'Configure seus horários e informações pessoais.',\n                target: '[data-onboarding=\"perfil-section\"]',\n                position: 'left'\n            },\n            {\n                id: 'complete',\n                title: 'Tudo Pronto!',\n                description: 'Você está pronto para começar a trabalhar.'\n            }\n        ]\n    },\n    {\n        id: 'cliente-inicial',\n        name: 'Primeiros Passos - Cliente',\n        description: 'Aprenda a agendar serviços',\n        role: 'Cliente',\n        steps: [\n            {\n                id: 'welcome',\n                title: 'Bem-vindo ao ServiceTech!',\n                description: 'Descubra como agendar seus serviços favoritos.'\n            },\n            {\n                id: 'buscar',\n                title: 'Buscar Estabelecimentos',\n                description: 'Use a busca para encontrar estabelecimentos próximos.',\n                target: '[data-onboarding=\"search-bar\"]',\n                position: 'bottom'\n            },\n            {\n                id: 'agendar',\n                title: 'Fazer Agendamento',\n                description: 'Clique em um estabelecimento para ver serviços e agendar.',\n                target: '[data-onboarding=\"empresa-card\"]',\n                position: 'top'\n            },\n            {\n                id: 'meus-agendamentos',\n                title: 'Seus Agendamentos',\n                description: 'Acompanhe seus agendamentos aqui.',\n                target: '[data-onboarding=\"meus-agendamentos\"]',\n                position: 'left'\n            },\n            {\n                id: 'complete',\n                title: 'Pronto para Agendar!',\n                description: 'Agora você sabe como usar o ServiceTech.'\n            }\n        ]\n    }\n];\nfunction OnboardingProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentFlow, setCurrentFlow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentStepIndex, setCurrentStepIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [completedSteps, setCompletedSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Carregar estado do localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingProvider.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"OnboardingProvider.useEffect\"], []);\n    // Salvar estado no localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingProvider.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"OnboardingProvider.useEffect\"], [\n        completedSteps\n    ]);\n    // Verificar se deve iniciar onboarding automaticamente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OnboardingProvider.useEffect\": ()=>{\n            if (user && !isActive) {\n                const userRole = user.role;\n                const hasCompletedOnboarding = completedSteps.includes(`${userRole}-onboarding-complete`);\n                if (!hasCompletedOnboarding) {\n                    // Iniciar onboarding baseado no papel do usuário\n                    const flowId = getFlowIdForRole(userRole);\n                    if (flowId) {\n                        setTimeout({\n                            \"OnboardingProvider.useEffect\": ()=>startOnboarding(flowId)\n                        }[\"OnboardingProvider.useEffect\"], 1000); // Delay para garantir que a página carregou\n                    }\n                }\n            }\n        }\n    }[\"OnboardingProvider.useEffect\"], [\n        user,\n        completedSteps\n    ]);\n    const getFlowIdForRole = (role)=>{\n        switch(role){\n            case 'Proprietário':\n                return 'proprietario-inicial';\n            case 'Colaborador':\n                return 'colaborador-inicial';\n            case 'Cliente':\n                return 'cliente-inicial';\n            default:\n                return null;\n        }\n    };\n    const startOnboarding = (flowId)=>{\n        const flow = ONBOARDING_FLOWS.find((f)=>f.id === flowId);\n        if (flow) {\n            setCurrentFlow(flow);\n            setCurrentStepIndex(0);\n            setIsActive(true);\n        }\n    };\n    const nextStep = ()=>{\n        if (currentFlow && currentStepIndex < currentFlow.steps.length - 1) {\n            setCurrentStepIndex((prev)=>prev + 1);\n        } else {\n            finishOnboarding();\n        }\n    };\n    const previousStep = ()=>{\n        if (currentStepIndex > 0) {\n            setCurrentStepIndex((prev)=>prev - 1);\n        }\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const finishOnboarding = ()=>{\n        if (currentFlow && user) {\n            const completionKey = `${user.role}-onboarding-complete`;\n            setCompletedSteps((prev)=>[\n                    ...prev,\n                    completionKey\n                ]);\n        }\n        setIsActive(false);\n        setCurrentFlow(null);\n        setCurrentStepIndex(0);\n    };\n    const restartOnboarding = ()=>{\n        if (user) {\n            const flowId = getFlowIdForRole(user.role);\n            if (flowId) {\n                startOnboarding(flowId);\n            }\n        }\n    };\n    const isStepCompleted = (stepId)=>{\n        return completedSteps.includes(stepId);\n    };\n    const markStepCompleted = (stepId)=>{\n        setCompletedSteps((prev)=>{\n            if (!prev.includes(stepId)) {\n                return [\n                    ...prev,\n                    stepId\n                ];\n            }\n            return prev;\n        });\n    };\n    const currentStep = currentFlow?.steps[currentStepIndex] || null;\n    const value = {\n        isActive,\n        currentFlow,\n        currentStepIndex,\n        currentStep,\n        startOnboarding,\n        nextStep,\n        previousStep,\n        skipStep,\n        finishOnboarding,\n        restartOnboarding,\n        isStepCompleted,\n        markStepCompleted\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OnboardingContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\contexts\\\\OnboardingContext.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, this);\n}\nfunction useOnboarding() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(OnboardingContext);\n    if (context === undefined) {\n        throw new Error('useOnboarding must be used within an OnboardingProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/OnboardingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Função para obter a preferência do sistema\n    const getSystemTheme = ()=>{\n        if (false) {}\n        return 'light';\n    };\n    // Função para aplicar o tema\n    const applyTheme = (newTheme)=>{\n        if (true) return;\n        const root = document.documentElement;\n        // Remove classes anteriores\n        root.classList.remove('light', 'dark');\n        let actualTheme;\n        if (newTheme === 'system') {\n            actualTheme = getSystemTheme();\n        } else {\n            actualTheme = newTheme;\n        }\n        // Aplica a classe do tema\n        root.classList.add(actualTheme);\n        setResolvedTheme(actualTheme);\n        // Salva a preferência no localStorage\n        localStorage.setItem('theme', newTheme);\n    };\n    // Inicializar tema\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (true) return;\n            // Recupera a preferência salva ou usa 'system' como padrão\n            const savedTheme = localStorage.getItem('theme') || 'system';\n            setTheme(savedTheme);\n            applyTheme(savedTheme);\n            // Listener para mudanças na preferência do sistema\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        applyTheme('system');\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Atualizar tema quando mudado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            applyTheme(theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    const value = {\n        theme,\n        setTheme,\n        resolvedTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/usePWA.ts":
/*!*****************************!*\
  !*** ./src/hooks/usePWA.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePWA: () => (/* binding */ usePWA),\n/* harmony export */   usePushNotifications: () => (/* binding */ usePushNotifications)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ usePWA,usePushNotifications auto */ \nfunction usePWA() {\n    const [isInstallable, setIsInstallable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isInstalled, setIsInstalled] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isOffline, setIsOffline] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [updateAvailable, setUpdateAvailable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [deferredPrompt, setDeferredPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [registration, setRegistration] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Registrar Service Worker\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePWA.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"usePWA.useEffect\"], []);\n    // Detectar se o app está instalado\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePWA.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"usePWA.useEffect\"], []);\n    // Detectar status offline/online\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePWA.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"usePWA.useEffect\"], []);\n    // Detectar prompt de instalação\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePWA.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"usePWA.useEffect\"], []);\n    // Função para instalar o app\n    const installApp = async ()=>{\n        if (!deferredPrompt) {\n            throw new Error('Prompt de instalação não disponível');\n        }\n        try {\n            await deferredPrompt.prompt();\n            const choiceResult = await deferredPrompt.userChoice;\n            if (choiceResult.outcome === 'accepted') {\n                console.log('✅ Usuário aceitou instalar o PWA');\n            } else {\n                console.log('❌ Usuário recusou instalar o PWA');\n            }\n            setDeferredPrompt(null);\n            setIsInstallable(false);\n        } catch (error) {\n            console.error('❌ Erro ao instalar PWA:', error);\n            throw error;\n        }\n    };\n    // Função para atualizar o app\n    const updateApp = ()=>{\n        if (registration && registration.waiting) {\n            registration.waiting.postMessage({\n                type: 'SKIP_WAITING'\n            });\n            // Recarregar a página após a atualização\n            navigator.serviceWorker.addEventListener('controllerchange', ()=>{\n                window.location.reload();\n            });\n        }\n    };\n    return {\n        isInstallable,\n        isInstalled,\n        isOffline,\n        installApp,\n        updateAvailable,\n        updateApp\n    };\n}\n// Hook para notificações push\nfunction usePushNotifications() {\n    const [isSupported, setIsSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [permission, setPermission] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('default');\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePushNotifications.useEffect\": ()=>{\n            if (false) {}\n        }\n    }[\"usePushNotifications.useEffect\"], []);\n    const requestPermission = async ()=>{\n        if (!isSupported) {\n            throw new Error('Notificações push não são suportadas');\n        }\n        try {\n            const permission = await Notification.requestPermission();\n            setPermission(permission);\n            return permission === 'granted';\n        } catch (error) {\n            console.error('❌ Erro ao solicitar permissão para notificações:', error);\n            return false;\n        }\n    };\n    const subscribe = async (vapidPublicKey)=>{\n        if (!isSupported || permission !== 'granted') {\n            return null;\n        }\n        try {\n            const registration = await navigator.serviceWorker.ready;\n            const sub = await registration.pushManager.subscribe({\n                userVisibleOnly: true,\n                applicationServerKey: vapidPublicKey\n            });\n            setSubscription(sub);\n            return sub;\n        } catch (error) {\n            console.error('❌ Erro ao se inscrever para notificações push:', error);\n            return null;\n        }\n    };\n    const unsubscribe = async ()=>{\n        if (!subscription) {\n            return false;\n        }\n        try {\n            const success = await subscription.unsubscribe();\n            if (success) {\n                setSubscription(null);\n            }\n            return success;\n        } catch (error) {\n            console.error('❌ Erro ao cancelar inscrição de notificações push:', error);\n            return false;\n        }\n    };\n    return {\n        isSupported,\n        permission,\n        subscription,\n        requestPermission,\n        subscribe,\n        unsubscribe\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/usePWA.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/supabase/client.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/client.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\nconst supabaseUrl = \"https://tlbpsdgoklkekoxzmzlo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsYnBzZGdva2xrZWtveHptemxvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2OTI4ODYsImV4cCI6MjA2NDI2ODg4Nn0._0Jj8aLx_WTyMkFoiFviThur0EW5jC3hYVOoVrrntWA\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase URL or Anon Key');\n}\nconst createClient = ()=>(0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLGVBQWUsSUFBTVIsa0VBQW1CQSxDQUFDQyxhQUFhSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxzcmNcXHV0aWxzXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcclxuXHJcbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMO1xyXG5jb25zdCBzdXBhYmFzZUFub25LZXkgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWTtcclxuXHJcbmlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlQW5vbktleSkge1xyXG4gIHRocm93IG5ldyBFcnJvcignTWlzc2luZyBTdXBhYmFzZSBVUkwgb3IgQW5vbiBLZXknKTtcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGNyZWF0ZUNsaWVudCA9ICgpID0+IGNyZWF0ZUJyb3dzZXJDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSk7XHJcblxyXG4vLyBUaXBvcyBwYXJhIGF1dGVudGljYcOnw6NvXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlclByb2ZpbGUge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICBuYW1lPzogc3RyaW5nO1xyXG4gIHBob25lPzogc3RyaW5nO1xyXG4gIHJvbGU6ICdBZG1pbmlzdHJhZG9yJyB8ICdQcm9wcmlldGFyaW8nIHwgJ0NvbGFib3JhZG9yJyB8ICdVc3VhcmlvJztcclxuICBjcmVhdGVkX2F0OiBzdHJpbmc7XHJcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xyXG4gIHBhZ2FtZW50b19jb25maXJtYWRvPzogYm9vbGVhbjtcclxuICBvbmJvYXJkaW5nX3BlbmRlbnRlPzogYm9vbGVhbjtcclxuICBwbGFub19zZWxlY2lvbmFkbz86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBdXRoU3RhdGUge1xyXG4gIHVzZXI6IFVzZXJQcm9maWxlIHwgbnVsbDtcclxuICBzZXNzaW9uOiBhbnkgfCBudWxsO1xyXG4gIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgaW5pdGlhbGl6ZWQ6IGJvb2xlYW47XHJcbn0iXSwibmFtZXMiOlsiY3JlYXRlQnJvd3NlckNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiRXJyb3IiLCJjcmVhdGVDbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/supabase/client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/cookie","vendor-chunks/tailwind-merge","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();