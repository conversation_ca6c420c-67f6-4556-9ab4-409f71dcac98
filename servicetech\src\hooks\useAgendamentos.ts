import { useState, useCallback, useMemo } from 'react';
import {
  Agendamento,
  AgendamentoCompleto,
  CriarAgendamentoData,
  FiltrosAgendamentos,
  FluxoAgendamento,
  ServicoSelecionado
} from '@/types/agendamentos';
import { ComboDetectado } from '@/types/combos';
import { detectarCombosAplicaveis, aplicarMelhorCombo } from '@/utils/combos';

interface EstadoAgendamentos {
  agendamentos: AgendamentoCompleto[];
  agendamentoAtual: Agendamento | null;
  fluxoAgendamento: FluxoAgendamento | null;
  combosDisponiveis: ComboDetectado[];
  comboAplicado: ComboDetectado | null;
  loading: boolean;
  error: string | null;
  salvando: boolean;
}

export function useAgendamentos() {
  const [estado, setEstado] = useState<EstadoAgendamentos>({
    agendamentos: [],
    agendamentoAtual: null,
    fluxoAgendamento: null,
    combosDisponiveis: [],
    comboAplicado: null,
    loading: false,
    error: null,
    salvando: false
  });

  // Buscar agendamentos
  const buscarAgendamentos = useCallback(async (filtros?: FiltrosAgendamentos) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const params = new URLSearchParams();
      
      if (filtros?.empresa_id) params.append('empresa_id', filtros.empresa_id.toString());
      if (filtros?.status_agendamento) params.append('status_agendamento', filtros.status_agendamento);
      if (filtros?.status_pagamento) params.append('status_pagamento', filtros.status_pagamento);
      if (filtros?.forma_pagamento) params.append('forma_pagamento', filtros.forma_pagamento);
      if (filtros?.data_inicio) params.append('data_inicio', filtros.data_inicio);
      if (filtros?.data_fim) params.append('data_fim', filtros.data_fim);
      if (filtros?.colaborador_user_id) params.append('colaborador_user_id', filtros.colaborador_user_id);
      if (filtros?.cliente_user_id) params.append('cliente_user_id', filtros.cliente_user_id);

      const response = await fetch(`/api/agendamentos?${params.toString()}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar agendamentos');
      }

      setEstado(prev => ({
        ...prev,
        agendamentos: result.data || [],
        loading: false
      }));

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
    }
  }, []);

  // Criar agendamento
  const criarAgendamento = useCallback(async (dados: CriarAgendamentoData): Promise<boolean> => {
    setEstado(prev => ({ ...prev, salvando: true, error: null }));

    try {
      const response = await fetch('/api/agendamentos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao criar agendamento');
      }

      setEstado(prev => ({
        ...prev,
        agendamentoAtual: result.data,
        salvando: false
      }));

      return true;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        salvando: false
      }));
      return false;
    }
  }, []);

  // Inicializar fluxo de agendamento
  const iniciarFluxoAgendamento = useCallback((empresa_id: number) => {
    setEstado(prev => ({
      ...prev,
      fluxoAgendamento: {
        empresa_id,
        servicos_selecionados: [],
        colaborador_selecionado: undefined,
        horario_selecionado: undefined,
        observacoes: '',
        forma_pagamento: undefined,
        valor_total: 0,
        valor_original: 0,
        valor_desconto: 0,
        combo_aplicado: undefined
      },
      combosDisponiveis: [],
      comboAplicado: null,
      error: null
    }));
  }, []);

  // Buscar combos da empresa
  const buscarCombosEmpresa = useCallback(async (empresa_id: number) => {
    try {
      const response = await fetch(`/api/combos?empresa_id=${empresa_id}&ativo=true`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.data)) {
          return data.data;
        }
      }
      return [];
    } catch (error) {
      console.error('Erro ao buscar combos:', error);
      return [];
    }
  }, []);

  // Calcular valores com base nos serviços selecionados
  const calcularValores = useCallback(async (servicos: ServicoSelecionado[], empresa_id: number) => {
    const valor_original = servicos.reduce((total, servico) => total + servico.preco, 0);

    // Buscar combos e detectar aplicáveis
    const combosEmpresa = await buscarCombosEmpresa(empresa_id);
    const servicosIds = servicos.map(s => s.servico_id);
    const combosDetectados = detectarCombosAplicaveis(servicosIds, combosEmpresa);
    const melhorCombo = aplicarMelhorCombo(servicosIds, combosEmpresa);

    let valor_desconto = 0;
    let combo_aplicado = undefined;

    if (melhorCombo && melhorCombo.pode_aplicar) {
      valor_desconto = melhorCombo.economia;
      combo_aplicado = {
        combo_id: melhorCombo.combo.combo_id,
        nome_combo: melhorCombo.combo.nome_combo,
        descricao: melhorCombo.combo.descricao,
        economia: melhorCombo.economia,
        tipo_desconto: melhorCombo.combo.desconto_valor_fixo ? 'valor_fixo' as const : 'percentual' as const,
        valor_desconto: melhorCombo.combo.desconto_valor_fixo || melhorCombo.combo.desconto_percentual || 0
      };
    }

    const valor_total = valor_original - valor_desconto;

    return {
      valor_original,
      valor_desconto,
      valor_total,
      combo_aplicado,
      combosDetectados,
      melhorCombo
    };
  }, [buscarCombosEmpresa]);

  // Adicionar serviço ao fluxo
  const adicionarServico = useCallback(async (servico: ServicoSelecionado) => {
    if (!estado.fluxoAgendamento) return;

    const servicosAtuais = estado.fluxoAgendamento.servicos_selecionados;
    const servicoJaSelecionado = servicosAtuais.find(s => s.servico_id === servico.servico_id);

    if (servicoJaSelecionado) return; // Não adicionar duplicados

    const novosServicos = [...servicosAtuais, servico];
    const valores = await calcularValores(novosServicos, estado.fluxoAgendamento.empresa_id);

    setEstado(prev => ({
      ...prev,
      fluxoAgendamento: prev.fluxoAgendamento ? {
        ...prev.fluxoAgendamento,
        servicos_selecionados: novosServicos,
        ...valores,
        // Reset seleções posteriores quando serviços mudam
        colaborador_selecionado: undefined,
        horario_selecionado: undefined
      } : null,
      combosDisponiveis: valores.combosDetectados,
      comboAplicado: valores.melhorCombo
    }));
  }, [calcularValores]);

  // Remover serviço do fluxo
  const removerServico = useCallback(async (servico_id: number) => {
    if (!estado.fluxoAgendamento) return;

    const servicosAtuais = estado.fluxoAgendamento.servicos_selecionados;
    const novosServicos = servicosAtuais.filter(s => s.servico_id !== servico_id);

    if (novosServicos.length === 0) {
      // Se não há mais serviços, resetar valores
      setEstado(prev => ({
        ...prev,
        fluxoAgendamento: prev.fluxoAgendamento ? {
          ...prev.fluxoAgendamento,
          servicos_selecionados: [],
          valor_original: 0,
          valor_desconto: 0,
          valor_total: 0,
          combo_aplicado: undefined,
          colaborador_selecionado: undefined,
          horario_selecionado: undefined
        } : null,
        combosDisponiveis: [],
        comboAplicado: null
      }));
    } else {
      const valores = await calcularValores(novosServicos, estado.fluxoAgendamento.empresa_id);

      setEstado(prev => ({
        ...prev,
        fluxoAgendamento: prev.fluxoAgendamento ? {
          ...prev.fluxoAgendamento,
          servicos_selecionados: novosServicos,
          ...valores,
          colaborador_selecionado: undefined,
          horario_selecionado: undefined
        } : null,
        combosDisponiveis: valores.combosDetectados,
        comboAplicado: valores.melhorCombo
      }));
    }
  }, [calcularValores]);

  // Manter compatibilidade com função antiga (deprecated)
  const selecionarServico = useCallback((servico: ServicoSelecionado) => {
    adicionarServico(servico);
  }, [adicionarServico]);

  // Selecionar colaborador no fluxo
  const selecionarColaborador = useCallback((colaborador?: {
    colaborador_user_id: string;
    name: string;
  }) => {
    setEstado(prev => ({
      ...prev,
      fluxoAgendamento: prev.fluxoAgendamento ? {
        ...prev.fluxoAgendamento,
        colaborador_selecionado: colaborador,
        // Reset seleção de horário
        horario_selecionado: undefined
      } : null
    }));
  }, []);

  // Selecionar horário no fluxo
  const selecionarHorario = useCallback((horario: {
    data_hora_inicio: string;
    data_hora_fim: string;
  }) => {
    setEstado(prev => ({
      ...prev,
      fluxoAgendamento: prev.fluxoAgendamento ? {
        ...prev.fluxoAgendamento,
        horario_selecionado: horario
      } : null
    }));
  }, []);

  // Definir observações no fluxo
  const definirObservacoes = useCallback((observacoes: string) => {
    setEstado(prev => ({
      ...prev,
      fluxoAgendamento: prev.fluxoAgendamento ? {
        ...prev.fluxoAgendamento,
        observacoes
      } : null
    }));
  }, []);

  // Definir forma de pagamento no fluxo
  const definirFormaPagamento = useCallback((forma_pagamento: 'Online' | 'Local') => {
    setEstado(prev => ({
      ...prev,
      fluxoAgendamento: prev.fluxoAgendamento ? {
        ...prev.fluxoAgendamento,
        forma_pagamento
      } : null
    }));
  }, []);

  // Finalizar agendamento
  const finalizarAgendamento = useCallback(async (): Promise<{ sucesso: boolean; agendamento?: any; requiresPagamento?: boolean }> => {
    const fluxo = estado.fluxoAgendamento;

    if (!fluxo || fluxo.servicos_selecionados.length === 0 || !fluxo.horario_selecionado || !fluxo.forma_pagamento) {
      setEstado(prev => ({
        ...prev,
        error: 'Dados incompletos para finalizar o agendamento'
      }));
      return { sucesso: false };
    }

    const dadosAgendamento: CriarAgendamentoData = {
      empresa_id: fluxo.empresa_id,
      servicos_ids: fluxo.servicos_selecionados.map(s => s.servico_id),
      colaborador_user_id: fluxo.colaborador_selecionado?.colaborador_user_id,
      data_hora_inicio: fluxo.horario_selecionado.data_hora_inicio,
      observacoes_cliente: fluxo.observacoes,
      forma_pagamento: fluxo.forma_pagamento,
      combo_id: fluxo.combo_aplicado?.combo_id,
      valor_desconto: fluxo.valor_desconto
    };

    const sucesso = await criarAgendamento(dadosAgendamento);

    if (sucesso) {
      const agendamento = estado.agendamentoAtual;

      // Se é pagamento online, não limpar o fluxo ainda (será limpo após pagamento)
      if (fluxo.forma_pagamento === 'Online') {
        return {
          sucesso: true,
          agendamento,
          requiresPagamento: true
        };
      } else {
        // Para pagamento local, limpar fluxo imediatamente
        setEstado(prev => ({
          ...prev,
          fluxoAgendamento: null,
          combosDisponiveis: [],
          comboAplicado: null
        }));
        return {
          sucesso: true,
          agendamento,
          requiresPagamento: false
        };
      }
    }

    return { sucesso: false };
  }, [estado.fluxoAgendamento, estado.agendamentoAtual, criarAgendamento]);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  // Limpar fluxo
  const limparFluxo = useCallback(() => {
    setEstado(prev => ({ ...prev, fluxoAgendamento: null }));
  }, []);

  // Verificar se o fluxo está completo (memoizado para evitar recálculos)
  const fluxoCompleto = useMemo(() => {
    return estado.fluxoAgendamento &&
      estado.fluxoAgendamento.servicos_selecionados.length > 0 &&
      estado.fluxoAgendamento.horario_selecionado &&
      estado.fluxoAgendamento.forma_pagamento;
  }, [estado.fluxoAgendamento]);

  // Atualizar status do agendamento
  const atualizarStatusAgendamento = useCallback(async (
    agendamentoId: number,
    novoStatus: string
  ): Promise<boolean> => {
    setEstado(prev => ({ ...prev, salvando: true, error: null }));

    try {
      const response = await fetch(`/api/agendamentos/${agendamentoId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status_agendamento: novoStatus }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar agendamento');
      }

      // Atualizar lista local se existir
      setEstado(prev => ({
        ...prev,
        agendamentos: prev.agendamentos.map(agendamento =>
          agendamento.agendamento_id === agendamentoId
            ? { ...agendamento, ...result.data }
            : agendamento
        ),
        salvando: false
      }));

      return true;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        salvando: false
      }));
      return false;
    }
  }, []);

  return {
    // Estado
    agendamentos: estado.agendamentos,
    agendamentoAtual: estado.agendamentoAtual,
    fluxoAgendamento: estado.fluxoAgendamento,
    combosDisponiveis: estado.combosDisponiveis,
    comboAplicado: estado.comboAplicado,
    loading: estado.loading,
    error: estado.error,
    salvando: estado.salvando,
    fluxoCompleto: !!fluxoCompleto,

    // Ações
    buscarAgendamentos,
    criarAgendamento,
    atualizarStatusAgendamento,
    iniciarFluxoAgendamento,
    selecionarServico, // Mantido para compatibilidade
    adicionarServico,
    removerServico,
    selecionarColaborador,
    selecionarHorario,
    definirObservacoes,
    definirFormaPagamento,
    finalizarAgendamento,
    limparErro,
    limparFluxo
  };
}
