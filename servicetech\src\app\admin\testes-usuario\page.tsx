'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { GerenciamentoCenarios } from '@/components/testes-usuario/GerenciamentoCenarios';
import { GerenciamentoSessoes } from '@/components/testes-usuario/GerenciamentoSessoes';
import { RelatoriosTestes } from '@/components/testes-usuario/RelatoriosTestes';
import { TemplatesCenarios } from '@/components/testes-usuario/TemplatesCenarios';

type TabAtiva = 'cenarios' | 'sessoes' | 'relatorios' | 'templates';

export default function TestesUsuarioPage() {
  const { user } = useAuth();
  const [tabAtiva, setTabAtiva] = useState<TabAtiva>('cenarios');

  const tabs = [
    { id: 'cenarios' as TabAtiva, label: 'Cenários', icon: '📝' },
    { id: 'sessoes' as TabAtiva, label: 'Sessõ<PERSON>', icon: '👥' },
    { id: 'relatorios' as TabAtiva, label: 'Relatórios', icon: '📊' },
    { id: 'templates' as TabAtiva, label: 'Templates', icon: '📋' }
  ];

  const renderTabContent = () => {
    switch (tabAtiva) {
      case 'cenarios':
        return <GerenciamentoCenarios />;
      case 'sessoes':
        return <GerenciamentoSessoes />;
      case 'relatorios':
        return <RelatoriosTestes />;
      case 'templates':
        return <TemplatesCenarios />;
      default:
        return <GerenciamentoCenarios />;
    }
  };

  return (
    <ProtectedRoute requiredRole={['Administrador']}>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  🧪 Testes de Usuário
                </h1>
                <p className="text-gray-600 mt-2">
                  Gerencie cenários, sessões e análise de testes de usabilidade
                </p>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-500">
                  Logado como: <span className="font-medium">{user?.email}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Estatísticas Rápidas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <span className="text-blue-600 text-lg">📝</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Cenários Ativos</p>
                    <p className="text-2xl font-bold text-gray-900">-</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <span className="text-green-600 text-lg">👥</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Sessões Realizadas</p>
                    <p className="text-2xl font-bold text-gray-900">-</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <span className="text-purple-600 text-lg">📊</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Taxa de Sucesso</p>
                    <p className="text-2xl font-bold text-gray-900">-%</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                      <span className="text-orange-600 text-lg">⭐</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Satisfação Média</p>
                    <p className="text-2xl font-bold text-gray-900">-/10</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Navegação por Tabs */}
          <Card className="mb-6">
            <CardHeader>
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setTabAtiva(tab.id)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                        tabAtiva === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <span className="mr-2">{tab.icon}</span>
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>
            </CardHeader>
          </Card>

          {/* Conteúdo da Tab Ativa */}
          <div className="space-y-6">
            {renderTabContent()}
          </div>

          {/* Informações de Ajuda */}
          <Card className="mt-8">
            <CardHeader>
              <h3 className="text-lg font-semibold text-gray-900">
                💡 Sobre os Testes de Usuário
              </h3>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Cenários de Teste</h4>
                  <p className="text-sm text-gray-600">
                    Defina tarefas específicas que os usuários devem realizar para validar 
                    a usabilidade e funcionalidade da plataforma. Cada cenário inclui passos 
                    detalhados, critérios de sucesso e métricas de avaliação.
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Sessões de Teste</h4>
                  <p className="text-sm text-gray-600">
                    Organize sessões estruturadas com participantes reais para executar 
                    os cenários definidos. Colete feedback, métricas de performance e 
                    identifique áreas de melhoria na experiência do usuário.
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Relatórios e Análises</h4>
                  <p className="text-sm text-gray-600">
                    Visualize métricas detalhadas, taxas de sucesso, tempo de conclusão 
                    e feedback qualitativo. Use os insights para priorizar melhorias 
                    e validar mudanças na interface.
                  </p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Templates Predefinidos</h4>
                  <p className="text-sm text-gray-600">
                    Utilize templates prontos para cenários comuns como onboarding, 
                    agendamento e navegação. Acelere a criação de testes com base 
                    em melhores práticas de UX.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
