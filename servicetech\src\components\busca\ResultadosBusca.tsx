'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { LogoEmpresaCard } from '@/components/ui/LogoEmpresa';
import { ResultadoBusca, EmpresaBusca } from '@/types/busca';

interface ResultadosBuscaProps {
  resultado: ResultadoBusca | null;
  loading: boolean;
  error: string | null;
  onCarregarMais: () => void;
  podeCarregarMais: boolean;
}

export function ResultadosBusca({
  resultado,
  loading,
  error,
  onCarregarMais,
  podeCarregarMais
}: ResultadosBuscaProps) {
  if (loading && !resultado) {
    return (
      <div className="text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
        <p className="text-[var(--text-secondary)] mt-4">Buscando estabelecimentos...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-[var(--error)] text-lg mb-4">
          <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          Erro na busca
        </div>
        <p className="text-[var(--text-secondary)]">{error}</p>
      </div>
    );
  }

  if (!resultado || resultado.empresas.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="mx-auto h-12 w-12 text-[var(--text-secondary)] mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <p className="text-[var(--text-secondary)] text-lg mb-2">Nenhum estabelecimento encontrado</p>
        <p className="text-[var(--text-secondary)] text-sm">Tente ajustar os filtros de busca ou usar termos diferentes.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cabeçalho dos Resultados */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-[var(--text-primary)]">
            Resultados da Busca
          </h2>
          <p className="text-[var(--text-secondary)] text-sm">
            {resultado.total} estabelecimento{resultado.total !== 1 ? 's' : ''} encontrado{resultado.total !== 1 ? 's' : ''}
            {resultado.total_paginas > 1 && (
              <span> • Página {resultado.pagina} de {resultado.total_paginas}</span>
            )}
          </p>
        </div>
      </div>

      {/* Grid de Empresas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {resultado.empresas.map((empresa) => (
          <CardEmpresa key={empresa.empresa_id} empresa={empresa} />
        ))}
      </div>

      {/* Botão Carregar Mais */}
      {podeCarregarMais && (
        <div className="text-center pt-6">
          <Button
            onClick={onCarregarMais}
            disabled={loading}
            variant="outline"
            size="lg"
          >
            {loading ? (
              <>
                <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-[var(--primary)] mr-2"></div>
                Carregando...
              </>
            ) : (
              'Carregar Mais Estabelecimentos'
            )}
          </Button>
        </div>
      )}

      {/* Indicador de Final */}
      {resultado.total > 0 && !podeCarregarMais && resultado.pagina > 1 && (
        <div className="text-center pt-6 text-[var(--text-secondary)] text-sm">
          Todos os estabelecimentos foram carregados
        </div>
      )}
    </div>
  );
}

// Componente para card individual da empresa
function CardEmpresa({ empresa }: { empresa: EmpresaBusca }) {
  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  return (
    <Link
      href={`/estabelecimento/${empresa.slug || empresa.empresa_id}`}
      className="block group hover:scale-105 transition-transform duration-200"
    >
      <Card className="h-full overflow-hidden hover:shadow-xl transition-shadow duration-300">
        {/* Imagem */}
        <div className="relative h-48 overflow-hidden">
          <LogoEmpresaCard
            nomeEmpresa={empresa.nome_empresa}
            logoUrl={empresa.logo_url}
            segmento={empresa.segmento}
            className="group-hover:scale-110 transition-transform duration-300"
          />
          {/* Badge de Segmento */}
          {empresa.segmento && (
            <div className="absolute top-2 left-2 bg-[var(--primary)] text-white px-2 py-1 rounded-md text-xs font-medium">
              {empresa.segmento}
            </div>
          )}
        </div>

        <CardContent className="p-4">
          {/* Nome da Empresa */}
          <CardTitle className="mb-2 text-xl group-hover:text-[var(--primary)] transition-colors duration-200 line-clamp-1">
            {empresa.nome_empresa}
          </CardTitle>

          {/* Endereço */}
          <div className="flex items-start space-x-2 mb-3">
            <svg className="h-4 w-4 text-[var(--text-secondary)] mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <p className="text-sm text-[var(--text-secondary)] line-clamp-2">
              {empresa.endereco_completo}
            </p>
          </div>

          {/* Descrição */}
          {empresa.descricao && (
            <p className="text-sm text-[var(--text-secondary)] mb-3 line-clamp-2">
              {empresa.descricao}
            </p>
          )}

          {/* Informações dos Serviços */}
          <div className="space-y-2">
            {/* Quantidade de Serviços */}
            <div className="flex items-center justify-between text-sm">
              <span className="text-[var(--text-secondary)]">Serviços:</span>
              <span className="font-medium text-[var(--text-primary)]">
                {empresa.total_servicos}
              </span>
            </div>

            {/* Faixa de Preços */}
            <div className="flex items-center justify-between text-sm">
              <span className="text-[var(--text-secondary)]">Preços:</span>
              <span className="font-medium text-[var(--text-primary)]">
                {empresa.preco_minimo === empresa.preco_maximo 
                  ? formatarPreco(empresa.preco_minimo)
                  : `${formatarPreco(empresa.preco_minimo)} - ${formatarPreco(empresa.preco_maximo)}`
                }
              </span>
            </div>

            {/* Categorias de Serviços */}
            {empresa.categorias_servicos.length > 0 && (
              <div className="pt-2">
                <div className="flex flex-wrap gap-1">
                  {empresa.categorias_servicos.slice(0, 3).map((categoria, index) => (
                    <span
                      key={index}
                      className="inline-block bg-[var(--surface)] text-[var(--text-secondary)] px-2 py-1 rounded-md text-xs"
                    >
                      {categoria}
                    </span>
                  ))}
                  {empresa.categorias_servicos.length > 3 && (
                    <span className="inline-block bg-[var(--surface)] text-[var(--text-secondary)] px-2 py-1 rounded-md text-xs">
                      +{empresa.categorias_servicos.length - 3}
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Call to Action */}
          <div className="pt-3 mt-3 border-t border-[var(--border)]">
            <div className="flex items-center justify-between">
              <span className="text-sm text-[var(--text-secondary)]">
                Ver detalhes
              </span>
              <svg className="h-4 w-4 text-[var(--primary)] group-hover:translate-x-1 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
