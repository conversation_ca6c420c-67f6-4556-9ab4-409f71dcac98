'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

import { useAuth } from '@/contexts/AuthContext';

interface Servico {
  id: string;
  nome: string;
  descricao: string;
  duracao: number;
  preco: number;
}

export default function ConclusaoPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [salvando, setSalvando] = useState(false);
  const [planoSelecionado, setPlanoSelecionado] = useState<string | null>(null);
  const [dadosEstabelecimento, setDadosEstabelecimento] = useState<any>(null);
  const [servicosCadastrados, setServicosCadastrados] = useState<Servico[]>([]);
  const [horariosComerciais, setHorariosComerciais] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // Verificar se o usuário está autenticado
    const checkAuth = async () => {
      if (authLoading) return;

      if (!user) {
        router.push('/login');
        return;
      }

      // Recuperar dados do localStorage
      const plano = localStorage.getItem('planoSelecionado');
      const dadosEmpresa = localStorage.getItem('dadosEstabelecimento');
      const servicos = localStorage.getItem('servicosCadastrados');
      const horarios = localStorage.getItem('horariosComerciais');

      if (!plano || !dadosEmpresa || !servicos || !horarios) {
        // Se não houver dados necessários, redirecionar para a etapa apropriada
        if (!plano) {
          router.push('/onboarding/selecao-plano');
        } else if (!dadosEmpresa) {
          router.push('/onboarding/registro-empresa');
        } else if (!servicos) {
          router.push('/onboarding/cadastro-servico');
        } else {
          router.push('/onboarding/definir-horario-comercial');
        }
      } else {
        setPlanoSelecionado(plano);
        setDadosEstabelecimento(JSON.parse(dadosEmpresa));
        setServicosCadastrados(JSON.parse(servicos));
        setHorariosComerciais(JSON.parse(horarios));
        setLoading(false);
      }
    };

    checkAuth();
  }, [router, user, authLoading]);

  const formatarPreco = (preco: number) => {
    return preco.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
  };

  const validarDados = () => {
    const erros = [];

    if (!user) erros.push('Usuário não autenticado');
    if (!planoSelecionado) erros.push('Plano não selecionado');
    if (!dadosEstabelecimento) erros.push('Dados do estabelecimento não encontrados');
    if (!servicosCadastrados || servicosCadastrados.length === 0) erros.push('Nenhum serviço cadastrado');
    if (!horariosComerciais || horariosComerciais.length === 0) erros.push('Horários comerciais não definidos');

    // Validar dados do estabelecimento
    if (dadosEstabelecimento) {
      if (!dadosEstabelecimento.nomeEstabelecimento) erros.push('Nome do estabelecimento obrigatório');
      if (!dadosEstabelecimento.cnpj) erros.push('CNPJ obrigatório');
      if (!dadosEstabelecimento.telefone) erros.push('Telefone obrigatório');
      if (!dadosEstabelecimento.endereco) erros.push('Endereço obrigatório');
      if (!dadosEstabelecimento.cidade) erros.push('Cidade obrigatória');
      if (!dadosEstabelecimento.estado) erros.push('Estado obrigatório');
      if (!dadosEstabelecimento.cep) erros.push('CEP obrigatório');
      if (!dadosEstabelecimento.segmento) erros.push('Segmento obrigatório');
    }

    // Validar serviços
    servicosCadastrados.forEach((servico, index) => {
      if (!servico.nome) erros.push(`Nome do serviço ${index + 1} obrigatório`);
      if (!servico.duracao || servico.duracao <= 0) erros.push(`Duração do serviço ${index + 1} inválida`);
      if (!servico.preco || servico.preco <= 0) erros.push(`Preço do serviço ${index + 1} inválido`);
    });

    return erros;
  };

  const handleFinalizarOnboarding = async () => {
    if (!user) {
      setError('Usuário não autenticado. Por favor, faça login novamente.');
      return;
    }

    // Validar dados antes de prosseguir
    const erros = validarDados();
    if (erros.length > 0) {
      console.error('❌ Erros de validação:', erros);
      setError(`Dados incompletos: ${erros.join(', ')}`);
      return;
    }

    setSalvando(true);
    setError(null);

    try {
      console.log('🚀 Enviando dados para finalização do onboarding...');

      const response = await fetch('/api/onboarding/finalize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          dadosEstabelecimento,
          servicosCadastrados,
          horariosComerciais,
          planoSelecionado,
          userId: user.id
        })
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('❌ Erro na resposta da API:', data);
        throw new Error(data.error || 'Erro desconhecido');
      }

      if (!data.success) {
        console.error('❌ Falha na finalização:', data);
        throw new Error(data.error || 'Falha na finalização do onboarding');
      }

      console.log('✅ Onboarding finalizado com sucesso:', data);

      // Sucesso! Limpar localStorage e mostrar mensagem de sucesso
      console.log('🧹 Limpando localStorage...');
      localStorage.removeItem('planoSelecionado');
      localStorage.removeItem('dadosEstabelecimento');
      localStorage.removeItem('servicosCadastrados');
      localStorage.removeItem('horariosComerciais');

      console.log('🎉 Onboarding finalizado com sucesso!');
      setSuccess(true);

    } catch (err: any) {
      console.error('❌ Erro ao finalizar onboarding:', err);
      setError(`Ocorreu um erro ao salvar os dados: ${err.message}. Por favor, tente novamente.`);
    } finally {
      setSalvando(false);
    }
  };

  const handleIrParaDashboard = () => {
    router.push('/proprietario/dashboard');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex justify-center items-center">
        <div className="flex items-center space-x-2">
          <svg className="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="text-gray-700 text-lg">Carregando...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          {success ? (
            <div className="text-center py-8">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg className="h-10 w-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-4">
                Cadastro Concluído com Sucesso!
              </h1>
              <p className="text-gray-600 mb-8">
                Seu estabelecimento foi cadastrado e está pronto para uso. Você já pode começar a gerenciar seus serviços e atendimentos.
              </p>
              <button
                onClick={handleIrParaDashboard}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition duration-300"
              >
                Ir para o Dashboard
              </button>
            </div>
          ) : (
            <>
              <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
                Resumo do Cadastro
              </h1>
              <p className="text-gray-600 text-center mb-8">
                Confira os dados do seu estabelecimento antes de finalizar
              </p>

              {error && (
                <div className="p-4 mb-6 bg-red-50 text-red-700 rounded-lg">
                  {error}
                </div>
              )}

              {/* Resumo do Plano */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-3 border-b pb-2">Plano Selecionado</h2>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <p className="text-lg font-medium text-blue-800">
                    Plano {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'}
                  </p>
                  <p className="text-gray-700 mt-1">
                    Valor mensal: {planoSelecionado === 'essencial' ? 'R$ 99,00' : 'R$ 199,00'}
                  </p>
                </div>
              </div>

              {/* Dados do Estabelecimento */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-3 border-b pb-2">Dados do Estabelecimento</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-gray-600 text-sm">Nome do Estabelecimento</p>
                    <p className="font-medium">{dadosEstabelecimento.nomeEstabelecimento}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 text-sm">CNPJ</p>
                    <p className="font-medium">{dadosEstabelecimento.cnpj}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 text-sm">Telefone</p>
                    <p className="font-medium">{dadosEstabelecimento.telefone}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 text-sm">Segmento</p>
                    <p className="font-medium">{dadosEstabelecimento.segmento}</p>
                  </div>
                  <div className="col-span-2">
                    <p className="text-gray-600 text-sm">Endereço</p>
                    <p className="font-medium">
                      {dadosEstabelecimento.endereco}, {dadosEstabelecimento.numero}
                      {dadosEstabelecimento.complemento ? `, ${dadosEstabelecimento.complemento}` : ''} - {dadosEstabelecimento.bairro}, {dadosEstabelecimento.cidade}/{dadosEstabelecimento.estado} - CEP: {dadosEstabelecimento.cep}
                    </p>
                  </div>
                </div>
              </div>

              {/* Serviços Cadastrados */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-3 border-b pb-2">
                  Serviços Cadastrados ({servicosCadastrados.length})
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {servicosCadastrados.map(servico => (
                    <div key={servico.id} className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-lg text-gray-800 mb-1">{servico.nome}</h3>
                      <p className="text-gray-600 text-sm mb-2">{servico.descricao}</p>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-700">Duração: {servico.duracao} min</span>
                        <span className="font-medium text-blue-700">{formatarPreco(servico.preco)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Horários Comerciais */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-800 mb-3 border-b pb-2">
                  Horários de Funcionamento
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {horariosComerciais.map((horario) => (
                    <div key={horario.diaSemana} className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-800 mb-1">
                        {({
                          'domingo': 'Domingo',
                          'segunda': 'Segunda-feira',
                          'terca': 'Terça-feira',
                          'quarta': 'Quarta-feira',
                          'quinta': 'Quinta-feira',
                          'sexta': 'Sexta-feira',
                          'sabado': 'Sábado'
                        } as Record<string, string>)[horario.diaSemana]}
                      </h3>
                      {horario.aberto ? (
                        <div>
                          <p className="text-gray-700 text-sm">
                            <span className="font-medium">Horário:</span> {horario.horarioAbertura} às {horario.horarioFechamento}
                          </p>
                          {horario.intervaloInicio && horario.intervaloFim && (
                            <p className="text-gray-700 text-sm">
                              <span className="font-medium">Intervalo:</span> {horario.intervaloInicio} às {horario.intervaloFim}
                            </p>
                          )}
                        </div>
                      ) : (
                        <p className="text-red-600 text-sm font-medium">Fechado</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-between items-center mt-8">
                <Link href="/onboarding/definir-horario-comercial" className="text-gray-600 hover:text-gray-800 transition duration-300">
                  Voltar para horários comerciais
                </Link>
                <button
                  onClick={handleFinalizarOnboarding}
                  disabled={salvando}
                  className="px-6 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition duration-300"
                >
                  {salvando ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Salvando...
                    </span>
                  ) : 'Finalizar Cadastro'}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}