import { createAdminClient } from './server';

/**
 * Operações administrativas que requerem bypass de RLS
 * Usar apenas para operações de sistema, onboarding, webhooks, etc.
 */
export class SupabaseAdmin {
  private client = createAdminClient();

  /**
   * Finalizar onboarding - operação administrativa
   */
  async finalizeOnboarding(data: {
    userId: string;
    dadosEstabelecimento: any;
    servicosCadastrados: any[];
    horariosComerciais: any[];
    planoSelecionado: string;
  }) {
    const { userId, dadosEstabelecimento, servicosCadastrados, horariosComerciais } = data;

    // 1. Verificar/criar empresa
    const { data: empresaExistente } = await this.client
      .from('empresas')
      .select('*')
      .eq('cnpj', dadosEstabelecimento.cnpj)
      .single();

    let empresaId;

    if (empresaExistente) {
      // Atualizar empresa existente
      empresaId = empresaExistente.empresa_id;
      await this.client
        .from('empresas')
        .update({
          nome_empresa: dadosEstabelecimento.nomeEstabelecimento,
          telefone: dadosEstabelecimento.telefone,
          endereco: dadosEstabelecimento.endereco,
          numero: dadosEstabelecimento.numero,
          complemento: dadosEstabelecimento.complemento,
          bairro: dadosEstabelecimento.bairro,
          cidade: dadosEstabelecimento.cidade,
          estado: dadosEstabelecimento.estado,
          cep: dadosEstabelecimento.cep,
          segmento: dadosEstabelecimento.segmento,
          horario_funcionamento: horariosComerciais,
          updated_at: new Date().toISOString()
        })
        .eq('empresa_id', empresaId);
    } else {
      // Criar nova empresa
      const { data: novaEmpresa } = await this.client
        .from('empresas')
        .insert([{
          nome_empresa: dadosEstabelecimento.nomeEstabelecimento,
          cnpj: dadosEstabelecimento.cnpj,
          telefone: dadosEstabelecimento.telefone,
          endereco: dadosEstabelecimento.endereco,
          numero: dadosEstabelecimento.numero,
          complemento: dadosEstabelecimento.complemento,
          bairro: dadosEstabelecimento.bairro,
          cidade: dadosEstabelecimento.cidade,
          estado: dadosEstabelecimento.estado,
          cep: dadosEstabelecimento.cep,
          segmento: dadosEstabelecimento.segmento,
          proprietario_user_id: userId,
          status: 'ativo',
          horario_funcionamento: horariosComerciais,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      empresaId = novaEmpresa.empresa_id;
    }

    // 2. Inserir serviços
    const servicosParaInserir = servicosCadastrados.map(servico => ({
      empresa_id: empresaId,
      nome_servico: servico.nome,
      descricao: servico.descricao,
      duracao_minutos: servico.duracao,
      preco: servico.preco,
      ativo: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }));

    await this.client
      .from('servicos')
      .insert(servicosParaInserir);

    // 3. Atualizar usuário
    await this.client.auth.admin.updateUserById(userId, {
      user_metadata: {
        role: 'Proprietario',
        onboarding_pendente: false,
        onboarding_concluido: true,
        empresa_id: empresaId
      }
    });

    return { empresaId, servicosCount: servicosCadastrados.length };
  }

  /**
   * Operações de webhook - administrativas
   */
  async processWebhookPayment(data: {
    userId: string;
    plano: string;
    cnpj: string;
    estabelecimento: string;
  }) {
    const { userId, plano, cnpj, estabelecimento } = data;

    // Atualizar usuário
    await this.client.auth.admin.updateUserById(userId, {
      user_metadata: {
        role: 'Proprietario',
        pagamento_confirmado: true,
        onboarding_pendente: true,
        plano_selecionado: plano
      }
    });

    // Verificar/criar empresa básica
    const { data: empresaExistente } = await this.client
      .from('empresas')
      .select('*')
      .eq('cnpj', cnpj)
      .single();

    if (!empresaExistente) {
      const { data: novaEmpresa } = await this.client
        .from('empresas')
        .insert([{
          cnpj,
          nome_empresa: estabelecimento,
          status: 'ativo',
          proprietario_user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      return novaEmpresa.empresa_id;
    }

    return empresaExistente.empresa_id;
  }

  /**
   * Verificar status do usuário - administrativo
   */
  async getUserStatus(userId: string) {
    const { data: userData } = await this.client.auth.admin.getUserById(userId);

    if (!userData.user) {
      return null;
    }

    const user = userData.user;
    return {
      id: user.id,
      email: user.email || '',
      role: user.user_metadata?.role || 'Usuario',
      pagamento_confirmado: user.user_metadata?.pagamento_confirmado || false,
      onboarding_pendente: user.user_metadata?.onboarding_pendente || false,
      plano_selecionado: user.user_metadata?.plano_selecionado || null,
      empresa_id: user.user_metadata?.empresa_id || null
    };
  }
}

export const supabaseAdmin = new SupabaseAdmin();
