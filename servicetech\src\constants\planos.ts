import { PlanoDetalhes } from '../types/onboarding';

// Definição dos planos disponíveis
export const PLANOS: Record<string, PlanoDetalhes> = {
  essencial: {
    tipo: 'essencial',
    nome: 'Plano Essencial',
    preco: 99.90,
    descricao: 'Ideal para pequenos estabelecimentos que estão começando',
    recursos: [
      'Até 6 serviços cadastrados',
      'Até 2 colaboradores extras',
      'Agendamento online',
      'Notificações por e-mail',
      'Relatórios básicos',
      'Combos de serviços',
      'Suporte por e-mail',
    ],
    limiteServicos: 6,
    limiteColaboradores: 2,
  },
  premium: {
    tipo: 'premium',
    nome: 'Plano Premium',
    preco: 199.90,
    descricao: 'Perfeito para estabelecimentos em crescimento com mais necessidades',
    recursos: [
      'Até 12 serviços cadastrados',
      'Até 6 colaboradores extras',
      'Agendamento online',
      'Notificações por e-mail e SMS',
      'Relatórios avançados',
      'Combos de serviços',
      'Módulo de marketing básico',
      'Assinatura de serviços mensal',
      'Integração com redes sociais',
      'Área do cliente personalizada',
      'Suporte prioritário 24/7',
    ],
    limiteServicos: 12,
    limiteColaboradores: 6,
  },
};

// Função para obter detalhes do plano pelo tipo
export function getPlanoDetalhes(tipo: string): PlanoDetalhes {
  return PLANOS[tipo.toLowerCase()] || PLANOS.essencial;
}

// Função para obter o preço do plano em centavos (para o Stripe)
export function getPlanoPrecoEmCentavos(tipo: string): number {
  const plano = getPlanoDetalhes(tipo);
  return Math.round(plano.preco * 100);
}