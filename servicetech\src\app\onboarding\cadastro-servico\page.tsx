'use client';
import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { BaseForm, FormField } from '@/components/forms/BaseForm';

interface Servico {
  id: string;
  nome: string;
  descricao: string;
  duracao: number;
  preco: number;
}

export default function CadastroServicoPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [planoSelecionado, setPlanoSelecionado] = useState<string | null>(null);
  const [dadosEstabelecimento, setDadosEstabelecimento] = useState<any>(null);
  const [servicosCadastrados, setServicosCadastrados] = useState<Servico[]>([]);
  const [showForm, setShowForm] = useState(false);

  // Configuração dos campos do formulário usando BaseForm
  const formFields: FormField[] = useMemo(() => [
    {
      name: 'nome',
      label: 'Nome do Serviço',
      type: 'text',
      required: true,
      placeholder: 'Ex: Corte de Cabelo Masculino',
      validation: (value: string) => {
        if (!value?.trim()) return 'Nome do serviço é obrigatório';
        if (value.length < 2) return 'Nome deve ter pelo menos 2 caracteres';
        if (value.length > 100) return 'Nome deve ter no máximo 100 caracteres';
      }
    },
    {
      name: 'descricao',
      label: 'Descrição',
      type: 'textarea',
      required: true,
      placeholder: 'Descreva o serviço oferecido',
      validation: (value: string) => {
        if (!value?.trim()) return 'Descrição do serviço é obrigatória';
        if (value.length < 10) return 'Descrição deve ter pelo menos 10 caracteres';
        if (value.length > 500) return 'Descrição deve ter no máximo 500 caracteres';
      }
    },
    {
      name: 'duracao',
      label: 'Duração (minutos)',
      type: 'number',
      required: true,
      validation: (value: number) => {
        const num = Number(value);
        if (isNaN(num) || num < 5) return 'Duração deve ser pelo menos 5 minutos';
        if (num > 480) return 'Duração deve ser no máximo 8 horas (480 minutos)';
        if (num % 5 !== 0) return 'Duração deve ser múltiplo de 5 minutos';
      }
    },
    {
      name: 'preco',
      label: 'Preço (R$)',
      type: 'number',
      required: true,
      placeholder: 'Ex: 50.00',
      validation: (value: number) => {
        const num = Number(value);
        if (isNaN(num) || num <= 0) return 'Preço deve ser um valor positivo';
        if (num > 10000) return 'Preço deve ser no máximo R$ 10.000,00';
      }
    }
  ], []);

  useEffect(() => {
    // Recuperar dados do localStorage
    const plano = localStorage.getItem('planoSelecionado');
    const dadosEmpresa = localStorage.getItem('dadosEstabelecimento');
    const servicos = localStorage.getItem('servicosCadastrados');

    if (!plano || !dadosEmpresa) {
      // Se não houver dados necessários, redirecionar para a etapa apropriada
      if (!plano) {
        router.push('/onboarding/selecao-plano');
      } else {
        router.push('/onboarding/registro-empresa');
      }
    } else {
      setPlanoSelecionado(plano);
      setDadosEstabelecimento(JSON.parse(dadosEmpresa));
      
      if (servicos) {
        setServicosCadastrados(JSON.parse(servicos));
      }
    }
  }, [router]);

  // Função para adicionar serviço usando BaseForm
  const handleAddServico = async (formData: Record<string, any>) => {
    // Verificar limite de serviços com base no plano
    const limiteServicos = planoSelecionado === 'essencial' ? 6 : 12;

    if (servicosCadastrados.length >= limiteServicos) {
      throw new Error(`Seu plano ${planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'} permite cadastrar até ${limiteServicos} serviços.`);
    }

    // Adicionar novo serviço à lista
    const novoServico: Servico = {
      id: Date.now().toString(),
      nome: formData.nome,
      descricao: formData.descricao,
      duracao: Number(formData.duracao),
      preco: Number(formData.preco),
    };

    const novosServicos = [...servicosCadastrados, novoServico];
    setServicosCadastrados(novosServicos);

    // Armazenar no localStorage
    localStorage.setItem('servicosCadastrados', JSON.stringify(novosServicos));

    // Esconder formulário
    setShowForm(false);
  };

  const handleRemoveServico = (id: string) => {
    const novosServicos = servicosCadastrados.filter(servico => servico.id !== id);
    setServicosCadastrados(novosServicos);
    localStorage.setItem('servicosCadastrados', JSON.stringify(novosServicos));
  };

  const handleContinuar = () => {
    if (servicosCadastrados.length === 0) {
      alert('Cadastre pelo menos um serviço para continuar.');
      return;
    }
    
    setLoading(true);
    // Redirecionar para a próxima etapa do onboarding
    router.push('/onboarding/definir-horario-comercial');
  };

  const formatarPreco = (preco: number) => {
    return preco.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
            Cadastro de Serviços
          </h1>
          <p className="text-gray-600 text-center mb-8">
            Cadastre os serviços oferecidos pelo seu estabelecimento
          </p>
          
          {planoSelecionado && dadosEstabelecimento && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800 font-medium">
                Plano {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'}: 
                Você pode cadastrar até {planoSelecionado === 'essencial' ? '6' : '12'} serviços.
              </p>
              <p className="text-gray-700 mt-1">
                Estabelecimento: {dadosEstabelecimento.nomeEstabelecimento}
              </p>
            </div>
          )}

          {/* Lista de serviços cadastrados */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Serviços Cadastrados ({servicosCadastrados.length})
              </h2>
              <button
                onClick={() => setShowForm(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition duration-300"
                disabled={showForm}
              >
                Adicionar Serviço
              </button>
            </div>

            {servicosCadastrados.length === 0 ? (
              <div className="p-6 bg-gray-50 rounded-lg text-center">
                <p className="text-gray-500">Nenhum serviço cadastrado. Adicione seu primeiro serviço.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {servicosCadastrados.map(servico => (
                  <div key={servico.id} className="border border-gray-200 rounded-lg p-4 relative">
                    <button
                      onClick={() => handleRemoveServico(servico.id)}
                      className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                      aria-label="Remover serviço"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    <h3 className="font-semibold text-lg text-gray-800 mb-1">{servico.nome}</h3>
                    <p className="text-gray-600 text-sm mb-2">{servico.descricao}</p>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700">Duração: {servico.duracao} min</span>
                      <span className="font-medium text-blue-700">{formatarPreco(servico.preco)}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Formulário para adicionar serviço */}
          {showForm && (
            <div className="mb-8">
              <BaseForm
                title="Adicionar Novo Serviço"
                fields={formFields}
                onSubmit={handleAddServico}
                onCancel={() => setShowForm(false)}
                submitText="Adicionar Serviço"
                cancelText="Cancelar"
                initialData={{
                  nome: '',
                  descricao: '',
                  duracao: 30,
                  preco: ''
                }}
                className="border border-gray-200 rounded-lg"
              />
            </div>
          )}

          <div className="flex justify-between items-center mt-8">
            <Link href="/onboarding/pagamento-assinatura" className="text-gray-600 hover:text-gray-800 transition duration-300">
              Voltar para pagamento
            </Link>
            <button
              onClick={handleContinuar}
              disabled={servicosCadastrados.length === 0 || loading}
              className={`px-6 py-3 rounded-lg font-semibold ${servicosCadastrados.length > 0 ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} transition duration-300`}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processando...
                </span>
              ) : 'Continuar'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}