'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Servico {
  id: string;
  nome: string;
  descricao: string;
  duracao: number;
  preco: number;
}

export default function CadastroServicoPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [planoSelecionado, setPlanoSelecionado] = useState<string | null>(null);
  const [dadosEstabelecimento, setDadosEstabelecimento] = useState<any>(null);
  const [servicosCadastrados, setServicosCadastrados] = useState<Servico[]>([]);
  
  const [formData, setFormData] = useState({
    nome: '',
    descricao: '',
    duracao: 30,
    preco: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    // Recuperar dados do localStorage
    const plano = localStorage.getItem('planoSelecionado');
    const dadosEmpresa = localStorage.getItem('dadosEstabelecimento');
    const servicos = localStorage.getItem('servicosCadastrados');

    if (!plano || !dadosEmpresa) {
      // Se não houver dados necessários, redirecionar para a etapa apropriada
      if (!plano) {
        router.push('/onboarding/selecao-plano');
      } else {
        router.push('/onboarding/registro-empresa');
      }
    } else {
      setPlanoSelecionado(plano);
      setDadosEstabelecimento(JSON.parse(dadosEmpresa));
      
      if (servicos) {
        setServicosCadastrados(JSON.parse(servicos));
      }
    }
  }, [router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Limpar erro do campo quando o usuário começa a digitar
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.nome.trim()) {
      newErrors.nome = 'Nome do serviço é obrigatório';
    }
    
    if (!formData.descricao.trim()) {
      newErrors.descricao = 'Descrição do serviço é obrigatória';
    }
    
    if (!formData.preco.trim()) {
      newErrors.preco = 'Preço é obrigatório';
    } else if (isNaN(parseFloat(formData.preco)) || parseFloat(formData.preco) <= 0) {
      newErrors.preco = 'Preço deve ser um valor positivo';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddServico = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Verificar limite de serviços com base no plano
      const limiteServicos = planoSelecionado === 'essencial' ? 6 : 12;
      
      if (servicosCadastrados.length >= limiteServicos) {
        alert(`Seu plano ${planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'} permite cadastrar até ${limiteServicos} serviços.`);
        return;
      }
      
      // Adicionar novo serviço à lista
      const novoServico: Servico = {
        id: Date.now().toString(),
        nome: formData.nome,
        descricao: formData.descricao,
        duracao: Number(formData.duracao),
        preco: parseFloat(formData.preco),
      };
      
      const novosServicos = [...servicosCadastrados, novoServico];
      setServicosCadastrados(novosServicos);
      
      // Armazenar no localStorage
      localStorage.setItem('servicosCadastrados', JSON.stringify(novosServicos));
      
      // Limpar formulário
      setFormData({
        nome: '',
        descricao: '',
        duracao: 30,
        preco: '',
      });
      
      // Esconder formulário
      setShowForm(false);
    }
  };

  const handleRemoveServico = (id: string) => {
    const novosServicos = servicosCadastrados.filter(servico => servico.id !== id);
    setServicosCadastrados(novosServicos);
    localStorage.setItem('servicosCadastrados', JSON.stringify(novosServicos));
  };

  const handleContinuar = () => {
    if (servicosCadastrados.length === 0) {
      alert('Cadastre pelo menos um serviço para continuar.');
      return;
    }
    
    setLoading(true);
    // Redirecionar para a próxima etapa do onboarding
    router.push('/onboarding/definir-horario-comercial');
  };

  const formatarPreco = (preco: number) => {
    return preco.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
            Cadastro de Serviços
          </h1>
          <p className="text-gray-600 text-center mb-8">
            Cadastre os serviços oferecidos pelo seu estabelecimento
          </p>
          
          {planoSelecionado && dadosEstabelecimento && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-blue-800 font-medium">
                Plano {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'}: 
                Você pode cadastrar até {planoSelecionado === 'essencial' ? '6' : '12'} serviços.
              </p>
              <p className="text-gray-700 mt-1">
                Estabelecimento: {dadosEstabelecimento.nomeEstabelecimento}
              </p>
            </div>
          )}

          {/* Lista de serviços cadastrados */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Serviços Cadastrados ({servicosCadastrados.length})
              </h2>
              <button
                onClick={() => setShowForm(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition duration-300"
                disabled={showForm}
              >
                Adicionar Serviço
              </button>
            </div>

            {servicosCadastrados.length === 0 ? (
              <div className="p-6 bg-gray-50 rounded-lg text-center">
                <p className="text-gray-500">Nenhum serviço cadastrado. Adicione seu primeiro serviço.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {servicosCadastrados.map(servico => (
                  <div key={servico.id} className="border border-gray-200 rounded-lg p-4 relative">
                    <button
                      onClick={() => handleRemoveServico(servico.id)}
                      className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                      aria-label="Remover serviço"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    <h3 className="font-semibold text-lg text-gray-800 mb-1">{servico.nome}</h3>
                    <p className="text-gray-600 text-sm mb-2">{servico.descricao}</p>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-700">Duração: {servico.duracao} min</span>
                      <span className="font-medium text-blue-700">{formatarPreco(servico.preco)}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Formulário para adicionar serviço */}
          {showForm && (
            <div className="mb-8 p-6 border border-gray-200 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Adicionar Novo Serviço</h3>
              <form onSubmit={handleAddServico} className="space-y-4">
                {/* Nome do Serviço */}
                <div>
                  <label htmlFor="nome" className="block text-sm font-medium text-gray-700 mb-1">
                    Nome do Serviço *
                  </label>
                  <input
                    type="text"
                    id="nome"
                    name="nome"
                    value={formData.nome}
                    onChange={handleChange}
                    className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.nome ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Ex: Corte de Cabelo Masculino"
                  />
                  {errors.nome && (
                    <p className="mt-1 text-sm text-red-600">{errors.nome}</p>
                  )}
                </div>

                {/* Descrição */}
                <div>
                  <label htmlFor="descricao" className="block text-sm font-medium text-gray-700 mb-1">
                    Descrição *
                  </label>
                  <textarea
                    id="descricao"
                    name="descricao"
                    value={formData.descricao}
                    onChange={handleChange}
                    rows={3}
                    className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.descricao ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Descreva o serviço oferecido"
                  />
                  {errors.descricao && (
                    <p className="mt-1 text-sm text-red-600">{errors.descricao}</p>
                  )}
                </div>

                {/* Duração e Preço */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Duração */}
                  <div>
                    <label htmlFor="duracao" className="block text-sm font-medium text-gray-700 mb-1">
                      Duração (minutos) *
                    </label>
                    <input
                      type="number"
                      id="duracao"
                      name="duracao"
                      value={formData.duracao}
                      onChange={handleChange}
                      min="5"
                      step="5"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {/* Preço */}
                  <div>
                    <label htmlFor="preco" className="block text-sm font-medium text-gray-700 mb-1">
                      Preço (R$) *
                    </label>
                    <input
                      type="text"
                      id="preco"
                      name="preco"
                      value={formData.preco}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${errors.preco ? 'border-red-500' : 'border-gray-300'}`}
                      placeholder="Ex: 50.00"
                    />
                    {errors.preco && (
                      <p className="mt-1 text-sm text-red-600">{errors.preco}</p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-4">
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-300"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300"
                  >
                    Adicionar Serviço
                  </button>
                </div>
              </form>
            </div>
          )}

          <div className="flex justify-between items-center mt-8">
            <Link href="/onboarding/pagamento-assinatura" className="text-gray-600 hover:text-gray-800 transition duration-300">
              Voltar para pagamento
            </Link>
            <button
              onClick={handleContinuar}
              disabled={servicosCadastrados.length === 0 || loading}
              className={`px-6 py-3 rounded-lg font-semibold ${servicosCadastrados.length > 0 ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'} transition duration-300`}
            >
              {loading ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processando...
                </span>
              ) : 'Continuar'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}