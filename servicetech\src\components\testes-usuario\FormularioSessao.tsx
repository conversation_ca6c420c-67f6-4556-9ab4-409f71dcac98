'use client';

import { useState, useEffect } from 'react';
import { useTestesUsuario } from '@/hooks/useTestesUsuario';
import { SessaoTeste, CenarioTeste } from '@/types/testesUsuario';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

interface FormularioSessaoProps {
  sessao?: SessaoTeste | null;
  onClose: () => void;
  onSuccess: () => void;
}

export function FormularioSessao({ sessao, onClose, onSuccess }: FormularioSessaoProps) {
  const { 
    criarSessao, 
    atualizarSessao, 
    buscarCenarios,
    cenarios,
    loading, 
    error 
  } = useTestesUsuario();

  const [formData, setFormData] = useState({
    nome_sessao: '',
    descricao: '',
    data_inicio: '',
    data_fim: '',
    participantes_alvo: 5,
    observacoes_gerais: ''
  });

  const [cenariosIncluidos, setCenariosIncluidos] = useState<number[]>([]);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    buscarCenarios({ ativo: true });
  }, [buscarCenarios]);

  useEffect(() => {
    if (sessao) {
      setFormData({
        nome_sessao: sessao.nome_sessao,
        descricao: sessao.descricao,
        data_inicio: new Date(sessao.data_inicio).toISOString().slice(0, 16),
        data_fim: new Date(sessao.data_fim).toISOString().slice(0, 16),
        participantes_alvo: sessao.participantes_alvo,
        observacoes_gerais: sessao.observacoes_gerais || ''
      });
      setCenariosIncluidos(sessao.cenarios_incluidos);
    }
  }, [sessao]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleCenarioToggle = (cenarioId: number) => {
    setCenariosIncluidos(prev => {
      if (prev.includes(cenarioId)) {
        return prev.filter(id => id !== cenarioId);
      } else {
        return [...prev, cenarioId];
      }
    });
    
    if (validationErrors.cenarios) {
      setValidationErrors(prev => ({ ...prev, cenarios: '' }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.nome_sessao.trim()) {
      errors.nome_sessao = 'Nome da sessão é obrigatório';
    }

    if (!formData.descricao.trim()) {
      errors.descricao = 'Descrição é obrigatória';
    }

    if (!formData.data_inicio) {
      errors.data_inicio = 'Data de início é obrigatória';
    }

    if (!formData.data_fim) {
      errors.data_fim = 'Data de fim é obrigatória';
    }

    if (formData.data_inicio && formData.data_fim) {
      const dataInicio = new Date(formData.data_inicio);
      const dataFim = new Date(formData.data_fim);
      const agora = new Date();

      if (dataInicio < agora && !sessao) {
        errors.data_inicio = 'Data de início deve ser futura';
      }

      if (dataFim <= dataInicio) {
        errors.data_fim = 'Data de fim deve ser posterior à data de início';
      }
    }

    if (formData.participantes_alvo < 1 || formData.participantes_alvo > 50) {
      errors.participantes_alvo = 'Número de participantes deve estar entre 1 e 50';
    }

    if (cenariosIncluidos.length === 0) {
      errors.cenarios = 'Selecione pelo menos um cenário';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const dadosSessao = {
        ...formData,
        data_inicio: new Date(formData.data_inicio).toISOString(),
        data_fim: new Date(formData.data_fim).toISOString(),
        cenarios_incluidos: cenariosIncluidos,
        status: 'Planejada' as const,
        participantes_confirmados: 0,
        moderador_user_id: 'current-user-id' // TODO: Pegar do contexto de autenticação
      };

      if (sessao) {
        await atualizarSessao(sessao.sessao_id, dadosSessao);
      } else {
        await criarSessao(dadosSessao);
      }

      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar sessão:', error);
    }
  };

  const getCenariosPorCategoria = () => {
    const categorias: Record<string, CenarioTeste[]> = {};
    
    cenarios.forEach(cenario => {
      if (!categorias[cenario.categoria]) {
        categorias[cenario.categoria] = [];
      }
      categorias[cenario.categoria].push(cenario);
    });

    return categorias;
  };

  const getDificuldadeColor = (dificuldade: string) => {
    switch (dificuldade) {
      case 'Facil': return 'text-green-600 bg-green-100';
      case 'Medio': return 'text-yellow-600 bg-yellow-100';
      case 'Dificil': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const cenariosPorCategoria = getCenariosPorCategoria();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          {sessao ? 'Editar Sessão' : 'Nova Sessão'}
        </h2>
        <Button
          onClick={onClose}
          className="bg-gray-300 hover:bg-gray-400 text-gray-700"
        >
          Cancelar
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Informações Básicas */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Informações da Sessão</h3>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nome da Sessão *
              </label>
              <input
                type="text"
                value={formData.nome_sessao}
                onChange={(e) => handleInputChange('nome_sessao', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  validationErrors.nome_sessao ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Ex: Teste de Usabilidade - Sprint 1"
              />
              {validationErrors.nome_sessao && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.nome_sessao}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Descrição *
              </label>
              <textarea
                value={formData.descricao}
                onChange={(e) => handleInputChange('descricao', e.target.value)}
                rows={3}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  validationErrors.descricao ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Descreva os objetivos e contexto desta sessão de teste"
              />
              {validationErrors.descricao && (
                <p className="text-red-500 text-sm mt-1">{validationErrors.descricao}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data e Hora de Início *
                </label>
                <input
                  type="datetime-local"
                  value={formData.data_inicio}
                  onChange={(e) => handleInputChange('data_inicio', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.data_inicio ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {validationErrors.data_inicio && (
                  <p className="text-red-500 text-sm mt-1">{validationErrors.data_inicio}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data e Hora de Fim *
                </label>
                <input
                  type="datetime-local"
                  value={formData.data_fim}
                  onChange={(e) => handleInputChange('data_fim', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.data_fim ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {validationErrors.data_fim && (
                  <p className="text-red-500 text-sm mt-1">{validationErrors.data_fim}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Participantes Alvo *
                </label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={formData.participantes_alvo}
                  onChange={(e) => handleInputChange('participantes_alvo', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    validationErrors.participantes_alvo ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {validationErrors.participantes_alvo && (
                  <p className="text-red-500 text-sm mt-1">{validationErrors.participantes_alvo}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Observações Gerais
              </label>
              <textarea
                value={formData.observacoes_gerais}
                onChange={(e) => handleInputChange('observacoes_gerais', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Observações adicionais sobre a sessão"
              />
            </div>
          </CardContent>
        </Card>

        {/* Seleção de Cenários */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Cenários de Teste</h3>
              <div className="text-sm text-gray-600">
                {cenariosIncluidos.length} cenário(s) selecionado(s)
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {validationErrors.cenarios && (
              <p className="text-red-500 text-sm mb-4">{validationErrors.cenarios}</p>
            )}

            {Object.keys(cenariosPorCategoria).length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-600">Nenhum cenário ativo encontrado</p>
                <p className="text-sm text-gray-500 mt-1">
                  Crie cenários de teste antes de criar uma sessão
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {Object.entries(cenariosPorCategoria).map(([categoria, cenariosCategoria]) => (
                  <div key={categoria}>
                    <h4 className="font-medium text-gray-900 mb-3">
                      {categoria} ({cenariosCategoria.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {cenariosCategoria.map((cenario) => (
                        <div
                          key={cenario.cenario_id}
                          className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                            cenariosIncluidos.includes(cenario.cenario_id)
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleCenarioToggle(cenario.cenario_id)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center mb-2">
                                <input
                                  type="checkbox"
                                  checked={cenariosIncluidos.includes(cenario.cenario_id)}
                                  onChange={() => handleCenarioToggle(cenario.cenario_id)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                                />
                                <h5 className="font-medium text-gray-900 text-sm">
                                  {cenario.nome_cenario}
                                </h5>
                              </div>
                              
                              <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                                {cenario.descricao}
                              </p>
                              
                              <div className="flex flex-wrap gap-1">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDificuldadeColor(cenario.dificuldade)}`}>
                                  {cenario.dificuldade}
                                </span>
                                <span className="px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                                  {cenario.papel_usuario}
                                </span>
                                <span className="px-2 py-1 rounded-full text-xs font-medium text-blue-600 bg-blue-100">
                                  {cenario.tempo_estimado_minutos}min
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Botões de Ação */}
        <div className="flex justify-end space-x-3 pt-6">
          <Button
            type="button"
            onClick={onClose}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700"
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            disabled={loading || Object.keys(cenariosPorCategoria).length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? 'Salvando...' : (sessao ? 'Atualizar' : 'Criar')} Sessão
          </Button>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Erro ao salvar sessão
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  {error}
                </div>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
}
