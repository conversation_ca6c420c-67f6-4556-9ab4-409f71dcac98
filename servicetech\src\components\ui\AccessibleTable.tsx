'use client';

import React, { useState, useRef, useEffect } from 'react';

interface Column<T> {
  key: keyof T;
  header: string;
  sortable?: boolean;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

interface AccessibleTableProps<T> {
  data: T[];
  columns: Column<T>[];
  caption?: string;
  summary?: string;
  className?: string;
  onRowClick?: (row: T, index: number) => void;
  onSort?: (key: keyof T, direction: 'asc' | 'desc') => void;
  sortKey?: keyof T;
  sortDirection?: 'asc' | 'desc';
  loading?: boolean;
  emptyMessage?: string;
  rowKeyField?: keyof T;
  selectable?: boolean;
  selectedRows?: Set<any>;
  onSelectionChange?: (selectedRows: Set<any>) => void;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
}

export function AccessibleTable<T extends Record<string, any>>({
  data,
  columns,
  caption,
  summary,
  className = '',
  onRowClick,
  onSort,
  sortKey,
  sortDirection,
  loading = false,
  emptyMessage = 'Nenhum dado encontrado',
  rowKeyField,
  selectable = false,
  selectedRows = new Set(),
  onSelectionChange,
  ...ariaProps
}: AccessibleTableProps<T>) {
  const [focusedCell, setFocusedCell] = useState<{ row: number; col: number } | null>(null);
  const tableRef = useRef<HTMLTableElement>(null);

  const handleSort = (key: keyof T) => {
    if (!onSort) return;
    
    const newDirection = sortKey === key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(key, newDirection);
  };

  const handleKeyDown = (e: React.KeyboardEvent, rowIndex: number, colIndex: number) => {
    const totalRows = data.length;
    const totalCols = columns.length + (selectable ? 1 : 0);

    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        if (rowIndex > 0) {
          setFocusedCell({ row: rowIndex - 1, col: colIndex });
        }
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (rowIndex < totalRows - 1) {
          setFocusedCell({ row: rowIndex + 1, col: colIndex });
        }
        break;
      case 'ArrowLeft':
        e.preventDefault();
        if (colIndex > 0) {
          setFocusedCell({ row: rowIndex, col: colIndex - 1 });
        }
        break;
      case 'ArrowRight':
        e.preventDefault();
        if (colIndex < totalCols - 1) {
          setFocusedCell({ row: rowIndex, col: colIndex + 1 });
        }
        break;
      case 'Home':
        e.preventDefault();
        setFocusedCell({ row: rowIndex, col: 0 });
        break;
      case 'End':
        e.preventDefault();
        setFocusedCell({ row: rowIndex, col: totalCols - 1 });
        break;
      case 'Enter':
      case ' ':
        if (onRowClick) {
          e.preventDefault();
          onRowClick(data[rowIndex], rowIndex);
        }
        break;
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange || !rowKeyField) return;
    
    const newSelection = new Set(selectedRows);
    if (checked) {
      data.forEach(row => newSelection.add(row[rowKeyField]));
    } else {
      data.forEach(row => newSelection.delete(row[rowKeyField]));
    }
    onSelectionChange(newSelection);
  };

  const handleSelectRow = (row: T, checked: boolean) => {
    if (!onSelectionChange || !rowKeyField) return;
    
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(row[rowKeyField]);
    } else {
      newSelection.delete(row[rowKeyField]);
    }
    onSelectionChange(newSelection);
  };

  const isAllSelected = rowKeyField && data.length > 0 && 
    data.every(row => selectedRows.has(row[rowKeyField]));
  const isIndeterminate = rowKeyField && selectedRows.size > 0 && !isAllSelected;

  useEffect(() => {
    if (focusedCell && tableRef.current) {
      const cell = tableRef.current.querySelector(
        `[data-row="${focusedCell.row}"][data-col="${focusedCell.col}"]`
      ) as HTMLElement;
      if (cell) {
        cell.focus();
      }
    }
  }, [focusedCell]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8" role="status" aria-live="polite">
        <div className="animate-spin h-8 w-8 border-4 border-[var(--primary)] border-t-transparent rounded-full" aria-hidden="true" />
        <span className="ml-2">Carregando dados...</span>
      </div>
    );
  }

  return (
    <div className={`overflow-x-auto ${className}`}>
      <table
        ref={tableRef}
        className="w-full border-collapse border border-[var(--border-color)]"
        role="table"
        aria-label={ariaProps['aria-label']}
        aria-labelledby={ariaProps['aria-labelledby']}
        aria-describedby={ariaProps['aria-describedby']}
        aria-rowcount={data.length + 1} // +1 for header
        aria-colcount={columns.length + (selectable ? 1 : 0)}
      >
        {caption && (
          <caption className="text-lg font-semibold text-[var(--text-primary)] p-4 text-left">
            {caption}
          </caption>
        )}
        
        <thead>
          <tr className="bg-[var(--surface)] border-b border-[var(--border-color)]">
            {selectable && (
              <th 
                className="p-3 text-left border-r border-[var(--border-color)]"
                scope="col"
                aria-label="Selecionar todas as linhas"
              >
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  ref={input => {
                    if (input) input.indeterminate = isIndeterminate ?? false;
                  }}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="w-4 h-4 text-[var(--primary)] border-[var(--border-color)] rounded focus:ring-[var(--primary)] focus:ring-2"
                  aria-label="Selecionar todas as linhas"
                />
              </th>
            )}
            {columns.map((column, index) => (
              <th
                key={String(column.key)}
                className={`p-3 text-left border-r border-[var(--border-color)] font-semibold text-[var(--text-primary)] ${
                  column.sortable ? 'cursor-pointer hover:bg-[var(--background)] focus:bg-[var(--background)] focus:outline-none focus:ring-2 focus:ring-[var(--primary)]' : ''
                }`}
                scope="col"
                style={{ width: column.width }}
                onClick={column.sortable ? () => handleSort(column.key) : undefined}
                onKeyDown={column.sortable ? (e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleSort(column.key);
                  }
                } : undefined}
                tabIndex={column.sortable ? 0 : undefined}
                aria-sort={
                  column.sortable && sortKey === column.key
                    ? sortDirection === 'asc' ? 'ascending' : 'descending'
                    : column.sortable ? 'none' : undefined
                }
              >
                <div className="flex items-center gap-2">
                  {column.header}
                  {column.sortable && (
                    <span aria-hidden="true">
                      {sortKey === column.key ? (
                        sortDirection === 'asc' ? '↑' : '↓'
                      ) : (
                        '↕'
                      )}
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        
        <tbody>
          {data.length === 0 ? (
            <tr>
              <td
                colSpan={columns.length + (selectable ? 1 : 0)}
                className="p-8 text-center text-[var(--text-secondary)]"
              >
                {emptyMessage}
              </td>
            </tr>
          ) : (
            data.map((row, rowIndex) => {
              const rowKey = rowKeyField ? row[rowKeyField] : rowIndex;
              const isSelected = rowKeyField ? selectedRows.has(row[rowKeyField]) : false;
              
              return (
                <tr
                  key={String(rowKey)}
                  className={`border-b border-[var(--border-color)] hover:bg-[var(--background)] ${
                    isSelected ? 'bg-[var(--primary)]/10' : ''
                  } ${onRowClick ? 'cursor-pointer' : ''}`}
                  onClick={onRowClick ? () => onRowClick(row, rowIndex) : undefined}
                  aria-rowindex={rowIndex + 2} // +2 because header is 1
                  aria-selected={selectable ? isSelected : undefined}
                >
                  {selectable && (
                    <td className="p-3 border-r border-[var(--border-color)]">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => {
                          e.stopPropagation();
                          handleSelectRow(row, e.target.checked);
                        }}
                        className="w-4 h-4 text-[var(--primary)] border-[var(--border-color)] rounded focus:ring-[var(--primary)] focus:ring-2"
                        aria-label={`Selecionar linha ${rowIndex + 1}`}
                        data-row={rowIndex}
                        data-col={0}
                        tabIndex={focusedCell?.row === rowIndex && focusedCell?.col === 0 ? 0 : -1}
                        onKeyDown={(e) => handleKeyDown(e, rowIndex, 0)}
                      />
                    </td>
                  )}
                  {columns.map((column, colIndex) => {
                    const cellIndex = colIndex + (selectable ? 1 : 0);
                    const value = row[column.key];
                    const cellContent = column.render ? column.render(value, row, rowIndex) : String(value ?? '');
                    
                    return (
                      <td
                        key={String(column.key)}
                        className={`p-3 border-r border-[var(--border-color)] text-${column.align || 'left'}`}
                        data-row={rowIndex}
                        data-col={cellIndex}
                        tabIndex={focusedCell?.row === rowIndex && focusedCell?.col === cellIndex ? 0 : -1}
                        onKeyDown={(e) => handleKeyDown(e, rowIndex, cellIndex)}
                        aria-describedby={summary ? `table-summary` : undefined}
                      >
                        {cellContent}
                      </td>
                    );
                  })}
                </tr>
              );
            })
          )}
        </tbody>
      </table>
      
      {summary && (
        <div id="table-summary" className="sr-only">
          {summary}
        </div>
      )}
    </div>
  );
}
