import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/utils/supabase/server';
import { FiltrosBusca, ResultadoBusca, EmpresaBusca, LIMITE_PADRAO, LIMITE_MAXIMO } from '@/types/busca';

export async function GET(request: NextRequest) {
  try {
    // Usar cliente administrativo para bypassa RLS e evitar recursão infinita
    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Extrair parâmetros de busca
    const filtros: FiltrosBusca = {
      termo: searchParams.get('termo') || undefined,
      cidade: searchParams.get('cidade') || undefined,
      estado: searchParams.get('estado') || undefined,
      bairro: searchParams.get('bairro') || undefined,
      categorias_servicos: searchParams.get('categorias_servicos')?.split(',').filter(Boolean) || undefined,
      preco_minimo: searchParams.get('preco_minimo') ? Number(searchParams.get('preco_minimo')) : undefined,
      preco_maximo: searchParams.get('preco_maximo') ? Number(searchParams.get('preco_maximo')) : undefined,
      ordenacao: (searchParams.get('ordenacao') as any) || 'relevancia',
      pagina: Number(searchParams.get('pagina')) || 1,
      limite: Math.min(Number(searchParams.get('limite')) || LIMITE_PADRAO, LIMITE_MAXIMO)
    };

    // Calcular offset para paginação
    const offset = (filtros.pagina! - 1) * filtros.limite!;

    // Construir query base com agregação de serviços
    let query = supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        logo_url,
        endereco,
        numero,
        bairro,
        cidade,
        estado,
        descricao,
        slug,
        segmento,
        servicos!inner (
          servico_id,
          nome_servico,
          categoria,
          preco
        )
      `)
      .eq('status', 'ativo')
      .eq('servicos.ativo', true);

    // Aplicar filtros de texto
    if (filtros.termo) {
      query = query.or(`nome_empresa.ilike.%${filtros.termo}%,descricao.ilike.%${filtros.termo}%`);
    }

    // Aplicar filtros de localização
    if (filtros.cidade) {
      query = query.ilike('cidade', `%${filtros.cidade}%`);
    }
    if (filtros.estado) {
      query = query.ilike('estado', `%${filtros.estado}%`);
    }
    if (filtros.bairro) {
      query = query.ilike('bairro', `%${filtros.bairro}%`);
    }

    // Aplicar filtros de categoria de serviços
    if (filtros.categorias_servicos && filtros.categorias_servicos.length > 0) {
      query = query.in('servicos.categoria', filtros.categorias_servicos);
    }

    // Aplicar filtros de preço
    if (filtros.preco_minimo !== undefined) {
      query = query.gte('servicos.preco', filtros.preco_minimo);
    }
    if (filtros.preco_maximo !== undefined) {
      query = query.lte('servicos.preco', filtros.preco_maximo);
    }

    // Executar query
    const { data: empresasRaw, error } = await query;

    if (error) {
      console.error('Erro ao buscar empresas:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao buscar empresas' },
        { status: 500 }
      );
    }



    // Processar dados e agrupar por empresa
    const empresasMap = new Map<number, EmpresaBusca>();

    empresasRaw?.forEach((item: any) => {
      const empresaId = item.empresa_id;

      if (!empresasMap.has(empresaId)) {
        empresasMap.set(empresaId, {
          empresa_id: item.empresa_id,
          nome_empresa: item.nome_empresa,
          logo_url: item.logo_url,
          endereco: item.endereco,
          numero: item.numero,
          bairro: item.bairro,
          cidade: item.cidade,
          estado: item.estado,
          descricao: item.descricao,
          slug: item.slug,
          segmento: item.segmento,
          total_servicos: 0,
          preco_minimo: Infinity,
          preco_maximo: 0,
          categorias_servicos: [],
          endereco_completo: `${item.endereco}, ${item.numero} - ${item.bairro}, ${item.cidade}/${item.estado}`
        });
      }

      const empresa = empresasMap.get(empresaId)!;

      // Processar array de serviços
      if (item.servicos && Array.isArray(item.servicos)) {
        item.servicos.forEach((servico: any) => {
          empresa.total_servicos++;

          // Verificar se o preço é válido antes de agregar
          if (servico.preco !== null && servico.preco !== undefined) {
            empresa.preco_minimo = Math.min(empresa.preco_minimo, servico.preco);
            empresa.preco_maximo = Math.max(empresa.preco_maximo, servico.preco);
          }

          // Verificar se a categoria é válida antes de agregar
          if (servico.categoria && !empresa.categorias_servicos.includes(servico.categoria)) {
            empresa.categorias_servicos.push(servico.categoria);
          }
        });
      }
    });

    // Converter para array e corrigir preços infinitos
    let empresas = Array.from(empresasMap.values()).map(empresa => ({
      ...empresa,
      preco_minimo: empresa.preco_minimo === Infinity ? 0 : empresa.preco_minimo
    }));

    // Aplicar ordenação
    empresas = aplicarOrdenacao(empresas, filtros.ordenacao!);

    // Calcular total antes da paginação
    const total = empresas.length;

    // Aplicar paginação
    empresas = empresas.slice(offset, offset + filtros.limite!);

    // Calcular metadados de paginação
    const totalPaginas = Math.ceil(total / filtros.limite!);
    const temProximaPagina = filtros.pagina! < totalPaginas;
    const temPaginaAnterior = filtros.pagina! > 1;

    const resultado: ResultadoBusca = {
      empresas,
      total,
      pagina: filtros.pagina!,
      limite: filtros.limite!,
      total_paginas: totalPaginas,
      tem_proxima_pagina: temProximaPagina,
      tem_pagina_anterior: temPaginaAnterior
    };

    return NextResponse.json({
      success: true,
      data: resultado
    });

  } catch (error: any) {
    console.error('Erro geral na API de busca de empresas:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Função auxiliar para aplicar ordenação
function aplicarOrdenacao(empresas: EmpresaBusca[], ordenacao: string): EmpresaBusca[] {
  switch (ordenacao) {
    case 'preco_menor':
      return empresas.sort((a, b) => a.preco_minimo - b.preco_minimo);
    case 'preco_maior':
      return empresas.sort((a, b) => b.preco_maximo - a.preco_maximo);
    case 'nome_az':
      return empresas.sort((a, b) => a.nome_empresa.localeCompare(b.nome_empresa));
    case 'nome_za':
      return empresas.sort((a, b) => b.nome_empresa.localeCompare(a.nome_empresa));
    case 'mais_servicos':
      return empresas.sort((a, b) => b.total_servicos - a.total_servicos);
    case 'avaliacao':
      // Para futuro - por enquanto ordena por nome
      return empresas.sort((a, b) => a.nome_empresa.localeCompare(b.nome_empresa));
    case 'relevancia':
    default:
      // Ordenação por relevância: mais serviços + nome
      return empresas.sort((a, b) => {
        if (b.total_servicos !== a.total_servicos) {
          return b.total_servicos - a.total_servicos;
        }
        return a.nome_empresa.localeCompare(b.nome_empresa);
      });
  }
}
