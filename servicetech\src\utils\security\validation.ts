/**
 * Sistema de Validação e Sanitização de Entrada
 * Protege contra XSS, SQL Injection e outros ataques
 */

import DOMPurify from 'isomorphic-dompurify';

// Tipos para validação
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  sanitize?: boolean;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  sanitizedData: Record<string, any>;
}

/**
 * Sanitiza string removendo caracteres perigosos
 */
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') return '';
  
  // Remove caracteres de controle e normaliza
  let sanitized = input
    .replace(/[\x00-\x1F\x7F]/g, '') // Remove caracteres de controle
    .trim()
    .normalize('NFC'); // Normalização Unicode
  
  // Sanitiza HTML usando DOMPurify
  sanitized = DOMPurify.sanitize(sanitized, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
  
  return sanitized;
}

/**
 * Sanitiza objeto recursivamente
 */
export function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) return obj;
  
  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }
  
  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = sanitizeString(key);
      sanitized[sanitizedKey] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

/**
 * Valida email
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
}

/**
 * Valida telefone brasileiro
 */
export function validatePhone(phone: string): boolean {
  const phoneRegex = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
  return phoneRegex.test(phone);
}

/**
 * Valida CNPJ
 */
export function validateCNPJ(cnpj: string): boolean {
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  
  if (cleanCNPJ.length !== 14) return false;
  if (/^(\d)\1+$/.test(cleanCNPJ)) return false; // Todos os dígitos iguais
  
  // Validação dos dígitos verificadores
  let sum = 0;
  let weight = 5;
  
  for (let i = 0; i < 12; i++) {
    sum += parseInt(cleanCNPJ[i]) * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  
  let digit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (parseInt(cleanCNPJ[12]) !== digit) return false;
  
  sum = 0;
  weight = 6;
  
  for (let i = 0; i < 13; i++) {
    sum += parseInt(cleanCNPJ[i]) * weight;
    weight = weight === 2 ? 9 : weight - 1;
  }
  
  digit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  return parseInt(cleanCNPJ[13]) === digit;
}

/**
 * Valida senha forte
 */
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Senha deve ter pelo menos 8 caracteres');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra maiúscula');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Senha deve conter pelo menos uma letra minúscula');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Senha deve conter pelo menos um número');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Senha deve conter pelo menos um caractere especial');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Esquemas de validação predefinidos
 */
export const VALIDATION_SCHEMAS = {
  // Cadastro de usuário
  userRegistration: {
    name: {
      required: true,
      minLength: 2,
      maxLength: 100,
      pattern: /^[a-zA-ZÀ-ÿ\s]+$/,
      sanitize: true
    },
    email: {
      required: true,
      maxLength: 254,
      custom: (value: string) => validateEmail(value) || 'Email inválido',
      sanitize: true
    },
    phone: {
      required: true,
      custom: (value: string) => validatePhone(value) || 'Telefone inválido',
      sanitize: true
    },
    password: {
      required: true,
      custom: (value: string) => {
        const result = validatePassword(value);
        return result.isValid || result.errors.join(', ');
      }
    }
  },
  
  // Cadastro de empresa
  companyRegistration: {
    nome_empresa: {
      required: true,
      minLength: 2,
      maxLength: 100,
      sanitize: true
    },
    cnpj: {
      required: true,
      custom: (value: string) => validateCNPJ(value) || 'CNPJ inválido',
      sanitize: true
    },
    telefone: {
      required: true,
      custom: (value: string) => validatePhone(value) || 'Telefone inválido',
      sanitize: true
    },
    endereco: {
      required: true,
      minLength: 5,
      maxLength: 200,
      sanitize: true
    }
  },
  
  // Cadastro de serviço
  serviceRegistration: {
    nome_servico: {
      required: true,
      minLength: 2,
      maxLength: 100,
      sanitize: true
    },
    descricao: {
      maxLength: 500,
      sanitize: true
    },
    duracao_minutos: {
      required: true,
      custom: (value: number) => (value >= 15 && value <= 480) || 'Duração deve ser entre 15 e 480 minutos'
    },
    preco: {
      required: true,
      custom: (value: number) => (value >= 0 && value <= 10000) || 'Preço deve ser entre R$ 0 e R$ 10.000'
    }
  }
} as const;

/**
 * Valida dados usando um schema
 */
export function validateData(data: any, schema: ValidationSchema): ValidationResult {
  const errors: Record<string, string> = {};
  const sanitizedData: Record<string, any> = {};
  
  // Sanitizar dados primeiro se necessário
  const workingData = sanitizeObject(data);
  
  for (const [field, rule] of Object.entries(schema)) {
    const value = workingData[field];
    
    // Verificar campo obrigatório
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors[field] = `${field} é obrigatório`;
      continue;
    }
    
    // Se campo não é obrigatório e está vazio, pular validação
    if (!rule.required && (value === undefined || value === null || value === '')) {
      sanitizedData[field] = value;
      continue;
    }
    
    // Validar comprimento mínimo
    if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
      errors[field] = `${field} deve ter pelo menos ${rule.minLength} caracteres`;
      continue;
    }
    
    // Validar comprimento máximo
    if (rule.maxLength && typeof value === 'string' && value.length > rule.maxLength) {
      errors[field] = `${field} deve ter no máximo ${rule.maxLength} caracteres`;
      continue;
    }
    
    // Validar padrão regex
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      errors[field] = `${field} tem formato inválido`;
      continue;
    }
    
    // Validação customizada
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (customResult !== true) {
        errors[field] = typeof customResult === 'string' ? customResult : `${field} é inválido`;
        continue;
      }
    }
    
    // Sanitizar se necessário
    sanitizedData[field] = rule.sanitize && typeof value === 'string' 
      ? sanitizeString(value) 
      : value;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    sanitizedData
  };
}

/**
 * Middleware para validação de API
 */
export function withValidation(schema: ValidationSchema) {
  return (data: any) => {
    const result = validateData(data, schema);
    
    if (!result.isValid) {
      const error = new Error('Dados de entrada inválidos');
      (error as any).status = 400;
      (error as any).validationErrors = result.errors;
      throw error;
    }
    
    return result.sanitizedData;
  };
}

/**
 * Detecta tentativas de SQL Injection
 */
export function detectSQLInjection(input: string): boolean {
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(--|\/\*|\*\/)/,
    /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/i,
    /(<script|<\/script>)/i
  ];
  
  return sqlPatterns.some(pattern => pattern.test(input));
}

/**
 * Detecta tentativas de XSS
 */
export function detectXSS(input: string): boolean {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe/gi,
    /<object/gi,
    /<embed/gi
  ];
  
  return xssPatterns.some(pattern => pattern.test(input));
}
