'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { Button } from '@/components/ui/Button';

interface TooltipPosition {
  top: number;
  left: number;
  width: number;
  height: number;
}

export function OnboardingTour() {
  const {
    isActive,
    currentStep,
    currentStepIndex,
    currentFlow,
    nextStep,
    previousStep,
    skipStep,
    finishOnboarding,
  } = useOnboarding();

  const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition | null>(null);
  const [highlightPosition, setHighlightPosition] = useState<TooltipPosition | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Calcular posição do tooltip e highlight
  useEffect(() => {
    if (!isActive || !currentStep?.target) {
      setTooltipPosition(null);
      setHighlightPosition(null);
      return;
    }

    const updatePosition = () => {
      const targetElement = document.querySelector(currentStep.target!);
      if (!targetElement) {
        console.warn(`Elemento alvo não encontrado: ${currentStep.target}`);
        return;
      }

      const rect = targetElement.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      // Posição do highlight (elemento alvo)
      const highlight = {
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        width: rect.width,
        height: rect.height,
      };
      setHighlightPosition(highlight);

      // Posição do tooltip baseada na posição preferida
      const tooltipWidth = 320;
      const tooltipHeight = 200; // Estimativa
      const spacing = 16;

      let tooltipTop = 0;
      let tooltipLeft = 0;

      switch (currentStep.position) {
        case 'top':
          tooltipTop = highlight.top - tooltipHeight - spacing;
          tooltipLeft = highlight.left + (highlight.width / 2) - (tooltipWidth / 2);
          break;
        case 'bottom':
          tooltipTop = highlight.top + highlight.height + spacing;
          tooltipLeft = highlight.left + (highlight.width / 2) - (tooltipWidth / 2);
          break;
        case 'left':
          tooltipTop = highlight.top + (highlight.height / 2) - (tooltipHeight / 2);
          tooltipLeft = highlight.left - tooltipWidth - spacing;
          break;
        case 'right':
          tooltipTop = highlight.top + (highlight.height / 2) - (tooltipHeight / 2);
          tooltipLeft = highlight.left + highlight.width + spacing;
          break;
        default:
          // Centro da tela
          tooltipTop = window.innerHeight / 2 - tooltipHeight / 2 + scrollTop;
          tooltipLeft = window.innerWidth / 2 - tooltipWidth / 2;
      }

      // Ajustar para não sair da tela
      const maxLeft = window.innerWidth - tooltipWidth - spacing;
      const maxTop = window.innerHeight - tooltipHeight - spacing + scrollTop;

      tooltipLeft = Math.max(spacing, Math.min(tooltipLeft, maxLeft));
      tooltipTop = Math.max(spacing + scrollTop, Math.min(tooltipTop, maxTop));

      setTooltipPosition({
        top: tooltipTop,
        left: tooltipLeft,
        width: tooltipWidth,
        height: tooltipHeight,
      });

      // Scroll para o elemento se necessário
      if (rect.top < 0 || rect.bottom > window.innerHeight) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }
    };

    // Delay para garantir que o DOM foi atualizado
    const timer = setTimeout(updatePosition, 100);

    // Atualizar posição quando a janela for redimensionada
    window.addEventListener('resize', updatePosition);
    window.addEventListener('scroll', updatePosition);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [isActive, currentStep]);

  // Não renderizar se não estiver ativo
  if (!isActive || !currentStep || !currentFlow) {
    return null;
  }

  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === currentFlow.steps.length - 1;
  const hasTarget = !!currentStep.target;

  return (
    <>
      {/* Overlay escuro */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        style={{ pointerEvents: hasTarget ? 'auto' : 'none' }}
      />

      {/* Highlight do elemento alvo */}
      {highlightPosition && hasTarget && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            top: highlightPosition.top - 4,
            left: highlightPosition.left - 4,
            width: highlightPosition.width + 8,
            height: highlightPosition.height + 8,
            border: '2px solid var(--primary)',
            borderRadius: '8px',
            boxShadow: '0 0 0 4px rgba(59, 130, 246, 0.3)',
            background: 'transparent',
          }}
        />
      )}

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-50 bg-[var(--surface)] border border-[var(--border-color)] rounded-lg shadow-xl p-6 max-w-sm"
        style={{
          top: tooltipPosition?.top || '50%',
          left: tooltipPosition?.left || '50%',
          transform: tooltipPosition ? 'none' : 'translate(-50%, -50%)',
        }}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-1">
              {currentStep.title}
            </h3>
            <div className="flex items-center space-x-2 text-sm text-[var(--text-secondary)]">
              <span>{currentFlow.name}</span>
              <span>•</span>
              <span>{currentStepIndex + 1} de {currentFlow.steps.length}</span>
            </div>
          </div>
          <button
            onClick={finishOnboarding}
            className="text-[var(--text-secondary)] hover:text-[var(--text-primary)] p-1"
            aria-label="Fechar tour"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Conteúdo */}
        <div className="mb-6">
          <p className="text-[var(--text-primary)] leading-relaxed">
            {currentStep.description}
          </p>
        </div>

        {/* Barra de progresso */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-[var(--text-secondary)]">Progresso</span>
            <span className="text-xs text-[var(--text-secondary)]">
              {Math.round(((currentStepIndex + 1) / currentFlow.steps.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-[var(--border-color)] rounded-full h-2">
            <div
              className="bg-[var(--primary)] h-2 rounded-full transition-all duration-300"
              style={{
                width: `${((currentStepIndex + 1) / currentFlow.steps.length) * 100}%`,
              }}
            />
          </div>
        </div>

        {/* Ações */}
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            {!isFirstStep && (
              <Button
                onClick={previousStep}
                variant="ghost"
                size="sm"
              >
                Anterior
              </Button>
            )}
            <Button
              onClick={skipStep}
              variant="ghost"
              size="sm"
              className="text-[var(--text-secondary)]"
            >
              Pular
            </Button>
          </div>

          <div className="flex space-x-2">
            {isLastStep ? (
              <Button
                onClick={finishOnboarding}
                variant="primary"
                size="sm"
              >
                Concluir
              </Button>
            ) : (
              <Button
                onClick={nextStep}
                variant="primary"
                size="sm"
              >
                Próximo
              </Button>
            )}
          </div>
        </div>

        {/* Seta indicativa (apenas quando há target) */}
        {hasTarget && currentStep.position && (
          <div
            className={`absolute w-3 h-3 bg-[var(--surface)] border-[var(--border-color)] transform rotate-45 ${
              currentStep.position === 'top' ? 'bottom-[-6px] left-1/2 -translate-x-1/2 border-b border-r' :
              currentStep.position === 'bottom' ? 'top-[-6px] left-1/2 -translate-x-1/2 border-t border-l' :
              currentStep.position === 'left' ? 'right-[-6px] top-1/2 -translate-y-1/2 border-t border-r' :
              currentStep.position === 'right' ? 'left-[-6px] top-1/2 -translate-y-1/2 border-b border-l' :
              'hidden'
            }`}
          />
        )}
      </div>
    </>
  );
}
