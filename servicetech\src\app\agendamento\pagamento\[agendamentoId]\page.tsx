'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { PagamentoOnline } from '@/components/agendamento/PagamentoOnline';
import { useAuth } from '@/contexts/AuthContext';

interface DadosAgendamento {
  agendamento_id: number;
  valor_total: number;
  status_agendamento: string;
  status_pagamento: string;
  forma_pagamento: string;
  servicos: {
    nome_servico: string;
  };
  empresas: {
    nome_empresa: string;
  };
}

export default function PagamentoAgendamentoPage() {
  const router = useRouter();
  const params = useParams();
  const { user, loading: authLoading } = useAuth();
  const [agendamento, setAgendamento] = useState<DadosAgendamento | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const agendamentoId = params.agendamentoId as string;

  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/login');
      return;
    }

    const buscarAgendamento = async () => {
      try {
        const response = await fetch(`/api/agendamentos/${agendamentoId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Erro ao buscar agendamento');
        }

        const dadosAgendamento = result.data;

        // Verificar se o agendamento pertence ao usuário
        if (dadosAgendamento.cliente_user_id !== user.id) {
          setError('Agendamento não encontrado ou não autorizado');
          return;
        }

        // Verificar se o agendamento pode ser pago
        if (dadosAgendamento.forma_pagamento !== 'Online') {
          setError('Este agendamento não é para pagamento online');
          return;
        }

        if (dadosAgendamento.status_pagamento === 'Pago') {
          // Já foi pago, redirecionar para confirmação
          router.push('/agendamento/confirmacao');
          return;
        }

        if (dadosAgendamento.status_agendamento === 'Cancelado' || 
            dadosAgendamento.status_agendamento === 'Recusado') {
          setError('Não é possível pagar um agendamento cancelado ou recusado');
          return;
        }

        setAgendamento(dadosAgendamento);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    buscarAgendamento();
  }, [agendamentoId, user, authLoading, router]);

  const handleSucessoPagamento = () => {
    // Redirecionar para página de sucesso
    router.push(`/agendamento/pagamento-sucesso?agendamento_id=${agendamentoId}`);
  };

  const handleErroPagamento = (erro: string) => {
    setError(erro);
  };

  const handleVoltar = () => {
    router.back();
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 border-2 border-[var(--primary)] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-[var(--text-secondary)]">Carregando...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 bg-[var(--error-light)] rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-[var(--error)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
              Erro no Pagamento
            </h2>
            <p className="text-[var(--text-secondary)] mb-6">
              {error}
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="outline" onClick={handleVoltar} className="flex-1">
                Voltar
              </Button>
              <Button onClick={() => router.push('/cliente/dashboard')} className="flex-1">
                Ir para Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!agendamento) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <span className="text-[var(--text-secondary)]">Agendamento não encontrado</span>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[var(--background)]">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-[var(--text-primary)] mb-2">
              Finalizar Pagamento
            </h1>
            <p className="text-[var(--text-secondary)]">
              Complete o pagamento para confirmar seu agendamento
            </p>
          </div>

          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-[var(--text-secondary)] mb-6">
            <span>Agendamento</span>
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <span>Resumo</span>
            <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
            <span className="text-[var(--primary)] font-medium">Pagamento</span>
          </div>

          {/* Componente de pagamento */}
          <PagamentoOnline
            agendamentoId={agendamento.agendamento_id}
            valor={agendamento.valor_total}
            nomeServico={agendamento.servicos.nome_servico}
            nomeEmpresa={agendamento.empresas.nome_empresa}
            onSucesso={handleSucessoPagamento}
            onErro={handleErroPagamento}
            onVoltar={handleVoltar}
          />
        </div>
      </div>
    </div>
  );
}
