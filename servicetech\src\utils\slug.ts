/**
 * Utilitários para geração e manipulação de slugs
 */

/**
 * Gera um slug a partir de um texto
 * @param text - Texto para converter em slug
 * @returns Slug gerado
 */
export function generateSlug(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    .toLowerCase()
    .trim()
    // Remover acentos e caracteres especiais
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    // Substituir espaços e caracteres especiais por hífens
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    // Remover hífens do início e fim
    .replace(/^-+|-+$/g, '');
}

/**
 * Gera um slug único baseado no nome da empresa
 * Esta função simula a lógica que deveria estar no banco de dados
 * @param nomeEmpresa - Nome da empresa
 * @param empresaId - ID da empresa (opcional, para evitar conflitos)
 * @returns Slug único
 */
export function generateUniqueSlug(nomeEmpresa: string, empresaId?: number): string {
  const baseSlug = generateSlug(nomeEmpresa);
  
  if (!baseSlug) {
    return empresaId ? `empresa-${empresaId}` : 'empresa';
  }

  // Por enquanto, retorna o slug base
  // Em uma implementação completa, verificaria duplicatas no banco
  return baseSlug;
}

/**
 * Valida se um slug está no formato correto
 * @param slug - Slug para validar
 * @returns True se o slug é válido
 */
export function isValidSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') {
    return false;
  }

  // Slug deve ter entre 1 e 255 caracteres
  if (slug.length < 1 || slug.length > 255) {
    return false;
  }

  // Slug deve conter apenas letras minúsculas, números e hífens
  // Não pode começar ou terminar com hífen
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

/**
 * Normaliza um slug para garantir que está no formato correto
 * @param slug - Slug para normalizar
 * @returns Slug normalizado
 */
export function normalizeSlug(slug: string): string {
  return generateSlug(slug);
}

/**
 * Gera sugestões de slug baseadas no nome da empresa
 * @param nomeEmpresa - Nome da empresa
 * @returns Array de sugestões de slug
 */
export function generateSlugSuggestions(nomeEmpresa: string): string[] {
  const baseSlug = generateSlug(nomeEmpresa);
  
  if (!baseSlug) {
    return ['empresa', 'estabelecimento', 'negocio'];
  }

  const suggestions = [baseSlug];
  
  // Adicionar variações com números
  for (let i = 2; i <= 5; i++) {
    suggestions.push(`${baseSlug}-${i}`);
  }

  // Adicionar variações com palavras comuns
  const suffixes = ['oficial', 'premium', 'plus', 'pro'];
  suffixes.forEach(suffix => {
    suggestions.push(`${baseSlug}-${suffix}`);
  });

  return suggestions;
}
