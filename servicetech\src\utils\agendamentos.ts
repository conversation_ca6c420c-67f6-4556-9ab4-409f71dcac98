import { AgendamentoCompleto } from '@/types/agendamentos';

/**
 * Utilitários para transformar dados de agendamentos da API
 */

/**
 * Transforma dados brutos da API em AgendamentoCompleto
 */
export function transformarAgendamentoAPI(agendamentoRaw: any): AgendamentoCompleto {
  return {
    agendamento_id: agendamentoRaw.agendamento_id,
    cliente_user_id: agendamentoRaw.cliente_user_id,
    empresa_id: agendamentoRaw.empresa_id,
    colaborador_user_id: agendamentoRaw.colaborador_user_id,
    servico_id: agendamentoRaw.servico_id,
    data_hora_inicio: agendamentoRaw.data_hora_inicio,
    data_hora_fim: agendamentoRaw.data_hora_fim,
    observacoes_cliente: agendamentoRaw.observacoes_cliente,
    status_agendamento: agendamentoRaw.status_agendamento,
    forma_pagamento: agendamentoRaw.forma_pagamento,
    status_pagamento: agendamentoRaw.status_pagamento,
    valor_total: agendamentoRaw.valor_total,
    valor_desconto: agendamentoRaw.valor_desconto,
    stripe_payment_intent_id: agendamentoRaw.stripe_payment_intent_id,
    codigo_confirmacao: agendamentoRaw.codigo_confirmacao,
    prazo_confirmacao: agendamentoRaw.prazo_confirmacao,
    created_at: agendamentoRaw.created_at,
    updated_at: agendamentoRaw.updated_at,
    
    // Dados relacionados
    empresa: {
      nome_empresa: agendamentoRaw.empresas?.nome_empresa || 'Empresa não identificada',
      endereco: agendamentoRaw.empresas?.endereco || '',
      telefone: agendamentoRaw.empresas?.telefone || ''
    },
    
    colaborador: {
      name: extrairNomeUsuario(agendamentoRaw.colaboradores?.user_metadata) || 'Colaborador não identificado',
      email: agendamentoRaw.colaboradores?.email || ''
    },
    
    servico: {
      nome_servico: agendamentoRaw.servicos?.nome_servico || 'Serviço não identificado',
      descricao: agendamentoRaw.servicos?.descricao || '',
      duracao_minutos: agendamentoRaw.servicos?.duracao_minutos || 0,
      preco: agendamentoRaw.servicos?.preco || 0,
      categoria: agendamentoRaw.servicos?.categoria || 'Outros'
    },
    
    cliente: {
      name: extrairNomeUsuario(agendamentoRaw.clientes?.user_metadata) || 'Cliente não identificado',
      email: agendamentoRaw.clientes?.email || '',
      phone: extrairTelefoneUsuario(agendamentoRaw.clientes?.user_metadata) || ''
    }
  };
}

/**
 * Extrai o nome do usuário dos metadados
 */
export function extrairNomeUsuario(userMetadata: any): string {
  if (!userMetadata) return '';
  
  return userMetadata.name || 
         userMetadata.full_name || 
         userMetadata.display_name || 
         '';
}

/**
 * Extrai o telefone do usuário dos metadados
 */
export function extrairTelefoneUsuario(userMetadata: any): string {
  if (!userMetadata) return '';
  
  return userMetadata.phone || 
         userMetadata.telefone || 
         '';
}

/**
 * Transforma array de agendamentos brutos da API
 */
export function transformarAgendamentosAPI(agendamentosRaw: any[]): AgendamentoCompleto[] {
  if (!Array.isArray(agendamentosRaw)) {
    return [];
  }
  
  return agendamentosRaw.map(transformarAgendamentoAPI);
}

/**
 * Formata valor monetário para exibição
 */
export function formatarValor(valor: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(valor);
}

/**
 * Formata data e hora para exibição
 */
export function formatarDataHora(dataHora: string): string {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dataHora));
}

/**
 * Formata apenas a data para exibição
 */
export function formatarData(data: string): string {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(new Date(data));
}

/**
 * Formata apenas a hora para exibição
 */
export function formatarHora(dataHora: string): string {
  return new Intl.DateTimeFormat('pt-BR', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dataHora));
}

/**
 * Calcula a duração entre duas datas em minutos
 */
export function calcularDuracaoMinutos(inicio: string, fim: string): number {
  const dataInicio = new Date(inicio);
  const dataFim = new Date(fim);
  return Math.round((dataFim.getTime() - dataInicio.getTime()) / (1000 * 60));
}

/**
 * Verifica se um agendamento está próximo do prazo de confirmação
 */
export function isProximoPrazo(prazoConfirmacao: string, horasAntecedencia: number = 2): boolean {
  const prazo = new Date(prazoConfirmacao);
  const agora = new Date();
  const tempoRestante = prazo.getTime() - agora.getTime();
  const horasRestantes = tempoRestante / (1000 * 60 * 60);
  
  return horasRestantes <= horasAntecedencia && horasRestantes > 0;
}

/**
 * Verifica se o prazo de confirmação expirou
 */
export function isPrazoExpirado(prazoConfirmacao: string): boolean {
  const prazo = new Date(prazoConfirmacao);
  const agora = new Date();
  return agora > prazo;
}

/**
 * Obtém a cor do status do agendamento
 */
export function getCorStatus(status: string): string {
  switch (status) {
    case 'Pendente':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'Confirmado':
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'Concluido':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'Cancelado':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case 'Recusado':
      return 'text-red-600 bg-red-50 border-red-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

/**
 * Obtém o ícone do status do agendamento
 */
export function getIconeStatus(status: string): string {
  switch (status) {
    case 'Pendente':
      return '⏳';
    case 'Confirmado':
      return '✅';
    case 'Concluido':
      return '✨';
    case 'Cancelado':
      return '🚫';
    case 'Recusado':
      return '❌';
    default:
      return '❓';
  }
}

/**
 * Obtém o label do status do agendamento
 */
export function getLabelStatus(status: string): string {
  switch (status) {
    case 'Pendente':
      return 'Pendente';
    case 'Confirmado':
      return 'Confirmado';
    case 'Concluido':
      return 'Concluído';
    case 'Cancelado':
      return 'Cancelado';
    case 'Recusado':
      return 'Recusado';
    default:
      return status;
  }
}
