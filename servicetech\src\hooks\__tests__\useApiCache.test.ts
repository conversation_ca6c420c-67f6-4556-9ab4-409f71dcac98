/**
 * Testes para o hook useApiCache
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { useApiCache } from '../useApiCache';

// Mock do fetch global
global.fetch = jest.fn();

describe('useApiCache', () => {
  const mockFetcher = jest.fn();
  const cacheKey = 'test-cache-key';

  beforeEach(() => {
    jest.clearAllMocks();
    // Limpar cache global
    const globalCache = (global as any).globalCache;
    if (globalCache) {
      globalCache.clear();
    }
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('deve buscar dados na primeira renderização', async () => {
    const mockData = { id: 1, name: 'Test' };
    mockFetcher.mockResolvedValue(mockData);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(null);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);
    expect(result.current.error).toBe(null);
    expect(mockFetcher).toHaveBeenCalledTimes(1);
  });

  it('deve usar dados do cache em renderizações subsequentes', async () => {
    const mockData = { id: 1, name: 'Test' };
    mockFetcher.mockResolvedValue(mockData);

    // Primeira renderização
    const { result: result1 } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    await waitFor(() => {
      expect(result1.current.loading).toBe(false);
    });

    expect(result1.current.data).toEqual(mockData);

    // Segunda renderização com mesma chave
    const { result: result2 } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    // Deve usar dados do cache imediatamente
    expect(result2.current.data).toEqual(mockData);
    expect(result2.current.loading).toBe(false);
    expect(mockFetcher).toHaveBeenCalledTimes(1); // Não deve chamar novamente
  });

  it('deve tratar erros corretamente', async () => {
    const mockError = new Error('Fetch error');
    mockFetcher.mockRejectedValue(mockError);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toBe(null);
    expect(result.current.error).toBe('Fetch error');
  });

  it('deve permitir refetch manual', async () => {
    const mockData1 = { id: 1, name: 'Test 1' };
    const mockData2 = { id: 2, name: 'Test 2' };
    
    mockFetcher
      .mockResolvedValueOnce(mockData1)
      .mockResolvedValueOnce(mockData2);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData1);

    // Refetch manual
    await act(async () => {
      await result.current.refetch();
    });

    expect(result.current.data).toEqual(mockData2);
    expect(mockFetcher).toHaveBeenCalledTimes(2);
  });

  it('deve permitir invalidar cache', async () => {
    const mockData = { id: 1, name: 'Test' };
    mockFetcher.mockResolvedValue(mockData);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);

    // Invalidar cache
    act(() => {
      result.current.invalidate();
    });

    expect(result.current.isStale).toBe(true);
  });

  it('deve permitir mutação otimística', async () => {
    const mockData = { id: 1, name: 'Test' };
    mockFetcher.mockResolvedValue(mockData);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);

    // Mutação otimística
    const newData = { id: 1, name: 'Updated Test' };
    act(() => {
      result.current.mutate(newData);
    });

    expect(result.current.data).toEqual(newData);
  });

  it('deve permitir mutação com função', async () => {
    const mockData = { id: 1, name: 'Test' };
    mockFetcher.mockResolvedValue(mockData);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);

    // Mutação com função
    act(() => {
      result.current.mutate((current) => ({
        ...current!,
        name: 'Updated via function'
      }));
    });

    expect(result.current.data).toEqual({
      id: 1,
      name: 'Updated via function'
    });
  });

  it('deve respeitar TTL do cache', async () => {
    jest.useFakeTimers();
    
    const mockData = { id: 1, name: 'Test' };
    mockFetcher.mockResolvedValue(mockData);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher, { ttl: 1000 }) // 1 segundo
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData);

    // Avançar tempo além do TTL
    act(() => {
      jest.advanceTimersByTime(1500);
    });

    // Nova renderização deve buscar dados novamente
    const { result: result2 } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher, { ttl: 1000 })
    );

    expect(result2.current.loading).toBe(true);
  });

  it('deve implementar stale-while-revalidate', async () => {
    jest.useFakeTimers();
    
    const mockData1 = { id: 1, name: 'Test 1' };
    const mockData2 = { id: 2, name: 'Test 2' };
    
    mockFetcher
      .mockResolvedValueOnce(mockData1)
      .mockResolvedValueOnce(mockData2);

    const { result } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher, { 
        ttl: 1000,
        staleWhileRevalidate: true 
      })
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData1);

    // Avançar tempo para tornar dados obsoletos (80% do TTL)
    act(() => {
      jest.advanceTimersByTime(800);
    });

    // Nova renderização deve mostrar dados antigos enquanto revalida
    const { result: result2 } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher, { 
        ttl: 1000,
        staleWhileRevalidate: true 
      })
    );

    // Deve mostrar dados antigos imediatamente
    expect(result2.current.data).toEqual(mockData1);
    expect(result2.current.isStale).toBe(true);
    expect(result2.current.loading).toBe(true); // Mas ainda está carregando novos dados
  });

  it('deve cancelar requisições quando componente desmonta', async () => {
    const mockAbortController = {
      abort: jest.fn(),
      signal: {} as AbortSignal
    };
    
    jest.spyOn(global, 'AbortController').mockImplementation(() => mockAbortController as any);

    mockFetcher.mockImplementation(() => 
      new Promise((resolve) => setTimeout(resolve, 1000))
    );

    const { result, unmount } = renderHook(() => 
      useApiCache(cacheKey, mockFetcher)
    );

    expect(result.current.loading).toBe(true);

    // Desmontar componente
    unmount();

    expect(mockAbortController.abort).toHaveBeenCalled();
  });

  it('deve revalidar dados quando fetcher muda', async () => {
    const mockData1 = { id: 1, name: 'Test 1' };
    const mockData2 = { id: 2, name: 'Test 2' };
    
    const fetcher1 = jest.fn().mockResolvedValue(mockData1);
    const fetcher2 = jest.fn().mockResolvedValue(mockData2);

    const { result, rerender } = renderHook(
      ({ fetcher }) => useApiCache(cacheKey, fetcher),
      { initialProps: { fetcher: fetcher1 } }
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData1);
    expect(fetcher1).toHaveBeenCalledTimes(1);

    // Mudar fetcher
    rerender({ fetcher: fetcher2 });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.data).toEqual(mockData2);
    expect(fetcher2).toHaveBeenCalledTimes(1);
  });
});
