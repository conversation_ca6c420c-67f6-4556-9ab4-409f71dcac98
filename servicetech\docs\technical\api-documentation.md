# 🔌 Documentação de APIs - ServiceTech

APIs RESTful completas para todas as funcionalidades da plataforma.

## 🎯 V<PERSON><PERSON> Geral

**Base URL**: `https://servicetech.vercel.app/api`

**Formato de Resposta**:
```json
{
  "success": true,
  "data": {},
  "error": null,
  "timestamp": "2025-01-27T10:00:00Z"
}
```

## 🔐 Autenticação

**Headers obrigatórios**:
```javascript
{
  "Authorization": "Bearer <jwt_token>",
  "Content-Type": "application/json"
}
```

**Papéis**: Administrador, Proprietário, Colaborador, Cliente

## 📅 APIs de Agendamentos

### `GET /api/agendamentos`
Lista agendamentos com filtros.

**Query**: `?empresa_id=123&status=Pendente&data_inicio=2025-01-01&page=1&limit=20`

### `POST /api/agendamentos`
Cria novo agendamento.
```json
{
  "empresa_id": 1,
  "servico_id": 1,
  "data_hora_inicio": "2025-01-27T14:00:00Z",
  "forma_pagamento": "Online"
}
```

### `PATCH /api/agendamentos/[id]`
Atualiza status do agendamento.
```json
{
  "status_agendamento": "Confirmado"
}
```

### `POST /api/agendamentos/[id]/cancelar`
Cancela agendamento com políticas de reembolso.
```json
{
  "motivo_cancelamento": "Imprevisto",
  "cancelado_por": "Cliente"
}
```

### `GET /api/agendamentos/disponibilidade`
Verifica horários disponíveis.
**Query**: `?empresa_id=1&servico_id=1&data=2025-01-27`

### `POST /api/agendamentos/payment-intent`
Cria Payment Intent para pagamento online.
```json
{
  "agendamento_id": 1,
  "valor": 50.00,
  "currency": "brl"
}
```

## 🏢 APIs de Empresas

### `GET /api/empresas/[id]`
Busca dados públicos da empresa.

### `GET /api/empresas`
Busca empresas com filtros (marketplace).
**Query**: `?nome=salao&cidade=São Paulo&categoria=beleza`

### `PUT /api/empresas/[id]/politicas`
Atualiza políticas de cancelamento.
```json
{
  "percentual_reembolso_24h": 50,
  "prazo_minimo_cancelamento": 2
}
```

## 🛠️ APIs de Serviços

### `GET /api/servicos`
Lista serviços da empresa.
**Query**: `?empresa_id=1&categoria=Cabelo&ativo=true`

### `POST /api/servicos`
Cria novo serviço.
```json
{
  "nome_servico": "Corte Feminino",
  "duracao_minutos": 45,
  "preco": 35.00,
  "categoria": "Cabelo"
}
```

### `PUT /api/servicos/[id]` | `DELETE /api/servicos/[id]`
Atualiza ou remove serviço.

## 👥 APIs de Colaboradores

### `GET /api/colaboradores`
Lista colaboradores da empresa.
**Query**: `?empresa_id=1&ativo=true`

### `POST /api/colaboradores/convite`
Gera convite para colaborador.
```json
{
  "email": "<EMAIL>",
  "servicos_ids": [1, 2],
  "percentual_comissao": 50
}
```

### `POST /api/colaboradores/aceitar-convite`
Aceita convite de colaborador.
```json
{
  "token": "abc123def456",
  "aceitar": true
}
```

## 💎 APIs de Planos de Assinatura (Premium)

### `GET /api/planos-assinatura`
Lista planos de assinatura da empresa.

### `POST /api/planos-assinatura`
Cria novo plano de assinatura.
```json
{
  "servico_id": 1,
  "nome_plano_empresa": "Clube do Corte",
  "preco_mensal_assinatura": 80.00,
  "limite_usos_mes": 4
}
```

### `GET|PUT|DELETE /api/planos-assinatura/[id]`
Operações específicas em planos.

## 🧾 APIs de Assinaturas de Clientes

### `GET /api/assinaturas-cliente`
Lista assinaturas do cliente ou empresa.

### `POST /api/assinaturas-cliente`
Cliente assina um plano.
```json
{
  "plano_cliente_id": 1,
  "stripe_payment_method_id": "pm_xxxxxxxx"
}
```

### `PUT /api/assinaturas-cliente/[id]`
Atualiza status da assinatura.
```json
{
  "status": "cancelada",
  "motivo_cancelamento": "Não preciso mais"
}
```

## 🎁 APIs de Combos

### `GET /api/combos`
Lista combos da empresa.

### `POST /api/combos`
Cria novo combo.
```json
{
  "nome_combo": "Combo Relax",
  "desconto_percentual": 15,
  "itens": [
    {"servico_id": 3, "quantidade": 1, "obrigatorio": true}
  ]
}
```

## 📢 APIs de Marketing (Premium)

### Cupons
- `GET|POST /api/cupons` - Lista/cria cupons
- `POST /api/cupons/validar` - Valida código de cupom

### Campanhas
- `GET|POST /api/campanhas` - Lista/cria campanhas
- `POST /api/campanhas/[id]/enviar` - Envia campanha
- `GET /api/campanhas/segmentacao/preview` - Preview de destinatários

## 🕒 APIs de Horários

### `GET|PUT /api/horarios/empresa`
Gerencia horários de funcionamento da empresa.

### `GET|PUT /api/horarios/colaborador/[id]`
Gerencia horários individuais do colaborador.

### `POST|DELETE /api/horarios/bloqueios`
Adiciona/remove bloqueios na agenda.

## 📧 APIs de Notificações

### `POST /api/notifications`
Envia notificação multicanal.
```json
{
  "user_id": "user123",
  "tipo_notificacao": "novo_agendamento",
  "canais": ["email", "sms", "push"]
}
```

### `GET|PUT /api/notifications/preferences`
Gerencia preferências de notificação.

### `POST /api/notifications/device-tokens`
Registra token para push notifications.

## 📊 APIs de Relatórios

### `GET /api/relatorios/agendamentos`
Relatórios de agendamentos (básico/completo por plano).
**Query**: `?empresa_id=1&data_inicio=2025-01-01&data_fim=2025-01-31`

### `GET /api/relatorios/financeiro`
Relatórios financeiros (Premium).

## 🛡️ APIs de Segurança

### `GET /api/security/audit`
Lista eventos de auditoria (Admin).

### `GET /api/security/alerts`
Lista alertas de segurança (Admin).

### `POST /api/security/monitor`
Registra evento de monitoramento.

## 💳 APIs do Stripe

### Stripe Connect
- `GET /api/stripe/connect/oauth` - Inicia OAuth Connect
- `GET /api/stripe/connect/status` - Status da conta conectada
- `POST /api/stripe/connect/disconnect` - Desconecta conta

### Pagamentos
- `POST /api/create-payment-intent` - Cria Payment Intent
- `POST /api/webhook/stripe` - Webhook Stripe

## 👤 APIs de Usuários

### `POST /api/user/reset`
Reseta usuário para estado inicial (Admin).

### `POST /api/onboarding/finalize`
Finaliza processo de onboarding.

## 🧪 APIs de Testes

### `GET|POST /api/test-admin`
Endpoints para testes administrativos.

## 🔗 Webhooks

### Stripe Webhook: `/api/webhook/stripe`
**Eventos**: `payment_intent.succeeded`, `payment_intent.payment_failed`, `invoice.payment_succeeded`, `account.updated`

## ❌ Códigos de Erro

### HTTP Status
- **200**: Sucesso
- **400**: Dados inválidos
- **401**: Não autorizado
- **403**: Permissões insuficientes
- **404**: Não encontrado
- **409**: Conflito
- **429**: Rate limit
- **500**: Erro interno

### Formato de Erro
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Dados inválidos",
    "details": {"field": "email", "reason": "Email inválido"}
  }
}
```

### Códigos Específicos
- `AUTH_REQUIRED` - Autenticação necessária
- `INSUFFICIENT_PERMISSIONS` - Permissões insuficientes
- `VALIDATION_ERROR` - Dados inválidos
- `BUSINESS_RULE_VIOLATION` - Regra de negócio violada
- `SCHEDULE_CONFLICT` - Conflito de horário
- `PAYMENT_ERROR` - Erro de pagamento

## 🔧 Rate Limiting

### Limites
- **Público**: 100 req/min
- **Autenticado**: 1000 req/min
- **Admin**: 5000 req/min

### Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

---

**ServiceTech API** - Integração completa e segura 🔌
