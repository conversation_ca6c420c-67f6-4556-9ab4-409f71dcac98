import Stripe from 'stripe';

// Inicializar o cliente Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: '2025-05-28.basil',
});

/**
 * Utilitários para Stripe Connect
 */
export class StripeConnectUtils {
  
  /**
   * Criar conta conectada no Stripe
   */
  static async criarContaConectada(dados: {
    email: string;
    nome_empresa: string;
    cnpj?: string;
    telefone?: string;
    endereco?: {
      linha1: string;
      cidade: string;
      estado: string;
      cep: string;
      pais: string;
    };
  }): Promise<Stripe.Account> {
    
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'BR',
      email: dados.email,
      business_type: 'company',
      company: {
        name: dados.nome_empresa,
        tax_id: dados.cnpj,
        phone: dados.telefone,
        address: dados.endereco ? {
          line1: dados.endereco.linha1,
          city: dados.endereco.cidade,
          state: dados.endereco.estado,
          postal_code: dados.endereco.cep,
          country: dados.endereco.pais,
        } : undefined,
      },
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      settings: {
        payouts: {
          schedule: {
            interval: 'daily',
          },
        },
      },
    });

    return account;
  }

  /**
   * Criar link de onboarding para conta conectada
   */
  static async criarLinkOnboarding(accountId: string, returnUrl: string, refreshUrl: string): Promise<string> {
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return accountLink.url;
  }

  /**
   * Verificar status da conta conectada
   */
  static async verificarStatusConta(accountId: string): Promise<{
    id: string;
    charges_enabled: boolean;
    payouts_enabled: boolean;
    details_submitted: boolean;
    requirements: {
      currently_due: string[];
      eventually_due: string[];
      past_due: string[];
      pending_verification: string[];
    };
    capabilities: {
      card_payments: string;
      transfers: string;
    };
  }> {
    
    const account = await stripe.accounts.retrieve(accountId);
    
    return {
      id: account.id,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      details_submitted: account.details_submitted,
      requirements: {
        currently_due: account.requirements?.currently_due || [],
        eventually_due: account.requirements?.eventually_due || [],
        past_due: account.requirements?.past_due || [],
        pending_verification: account.requirements?.pending_verification || [],
      },
      capabilities: {
        card_payments: account.capabilities?.card_payments || 'inactive',
        transfers: account.capabilities?.transfers || 'inactive',
      },
    };
  }

  /**
   * Criar Payment Intent com conta conectada
   */
  static async criarPaymentIntentConectado(dados: {
    valor: number;
    conta_conectada_id: string;
    percentual_comissao: number;
    agendamento_id: number;
    cliente_email: string;
    descricao: string;
    metadata: Record<string, string>;
  }): Promise<Stripe.PaymentIntent> {
    
    const valorCentavos = Math.round(dados.valor * 100);
    const comissaoCentavos = Math.round((dados.valor * dados.percentual_comissao / 100) * 100);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: valorCentavos,
      currency: 'brl',
      automatic_payment_methods: {
        enabled: true,
      },
      application_fee_amount: comissaoCentavos,
      transfer_data: {
        destination: dados.conta_conectada_id,
      },
      metadata: {
        ...dados.metadata,
        agendamento_id: dados.agendamento_id.toString(),
        conta_conectada_id: dados.conta_conectada_id,
        percentual_comissao: dados.percentual_comissao.toString(),
        tipo_pagamento: 'agendamento_conectado',
      },
      description: dados.descricao,
      receipt_email: dados.cliente_email,
    });

    return paymentIntent;
  }

  /**
   * Processar reembolso com conta conectada
   */
  static async processarReembolsoConectado(dados: {
    payment_intent_id: string;
    valor_reembolso: number;
    motivo?: string;
    reverter_comissao?: boolean;
  }): Promise<Stripe.Refund> {
    
    const refund = await stripe.refunds.create({
      payment_intent: dados.payment_intent_id,
      amount: Math.round(dados.valor_reembolso * 100),
      reverse_transfer: dados.reverter_comissao || false,
      metadata: {
        motivo: dados.motivo || 'Cancelamento de agendamento',
        tipo: 'agendamento_conectado'
      },
      reason: 'requested_by_customer'
    });

    return refund;
  }

  /**
   * Obter saldo da conta conectada
   */
  static async obterSaldoConta(accountId: string): Promise<{
    disponivel: number;
    pendente: number;
    moeda: string;
  }> {
    
    const balance = await stripe.balance.retrieve({
      stripeAccount: accountId,
    });

    const disponivel = balance.available.find(b => b.currency === 'brl')?.amount || 0;
    const pendente = balance.pending.find(b => b.currency === 'brl')?.amount || 0;

    return {
      disponivel: disponivel / 100, // Converter de centavos para reais
      pendente: pendente / 100,
      moeda: 'BRL',
    };
  }

  /**
   * Listar transações da conta conectada
   */
  static async listarTransacoes(accountId: string, limite: number = 10): Promise<Stripe.BalanceTransaction[]> {
    const transactions = await stripe.balanceTransactions.list(
      { limit: limite },
      { stripeAccount: accountId }
    );

    return transactions.data;
  }

  /**
   * Desconectar conta (desativar)
   */
  static async desconectarConta(accountId: string): Promise<boolean> {
    try {
      // Não deletamos a conta, apenas marcamos como inativa
      // A conta permanece no Stripe para histórico
      await stripe.accounts.update(accountId, {
        settings: {
          payouts: {
            schedule: {
              interval: 'manual',
            },
          },
        },
      });
      
      return true;
    } catch (error) {
      console.error('Erro ao desconectar conta Stripe:', error);
      return false;
    }
  }

  /**
   * Validar webhook de conta conectada
   */
  static async processarWebhookConta(event: Stripe.Event): Promise<{
    account_id: string;
    tipo_evento: string;
    dados: any;
  } | null> {
    
    if (!event.account) {
      return null;
    }

    const accountId = event.account;
    
    switch (event.type) {
      case 'account.updated':
        const account = event.data.object as Stripe.Account;
        return {
          account_id: accountId,
          tipo_evento: 'account_updated',
          dados: {
            charges_enabled: account.charges_enabled,
            payouts_enabled: account.payouts_enabled,
            details_submitted: account.details_submitted,
          },
        };
        
      case 'capability.updated':
        const capability = event.data.object as Stripe.Capability;
        return {
          account_id: accountId,
          tipo_evento: 'capability_updated',
          dados: {
            capability: capability.id,
            status: capability.status,
          },
        };
        
      default:
        return null;
    }
  }

  /**
   * Calcular comissão da plataforma
   */
  static calcularComissao(valor: number, percentual: number): {
    valor_bruto: number;
    comissao_plataforma: number;
    valor_liquido_estabelecimento: number;
    taxa_stripe: number;
    valor_final_estabelecimento: number;
  } {
    
    const comissaoPlataforma = (valor * percentual) / 100;
    const valorLiquidoEstabelecimento = valor - comissaoPlataforma;
    
    // Taxa aproximada do Stripe (3.4% + R$ 0,60)
    const taxaStripe = (valor * 0.034) + 0.60;
    const valorFinalEstabelecimento = valorLiquidoEstabelecimento - taxaStripe;

    return {
      valor_bruto: valor,
      comissao_plataforma: comissaoPlataforma,
      valor_liquido_estabelecimento: valorLiquidoEstabelecimento,
      taxa_stripe: taxaStripe,
      valor_final_estabelecimento: Math.max(0, valorFinalEstabelecimento),
    };
  }

  /**
   * Gerar URL de dashboard da conta conectada
   */
  static async gerarUrlDashboard(accountId: string): Promise<string> {
    const loginLink = await stripe.accounts.createLoginLink(accountId);
    return loginLink.url;
  }

  /**
   * Verificar se conta pode receber pagamentos
   */
  static podeReceberPagamentos(statusConta: {
    charges_enabled: boolean;
    payouts_enabled: boolean;
    details_submitted: boolean;
  }): boolean {
    return statusConta.charges_enabled && statusConta.details_submitted;
  }

  /**
   * Obter status legível da conta
   */
  static obterStatusLegivel(statusConta: {
    charges_enabled: boolean;
    payouts_enabled: boolean;
    details_submitted: boolean;
    requirements: {
      currently_due: string[];
      past_due: string[];
    };
  }): {
    status: 'not_connected' | 'pending' | 'active' | 'restricted';
    mensagem: string;
    cor: 'gray' | 'yellow' | 'green' | 'red';
  } {
    
    if (!statusConta.details_submitted) {
      return {
        status: 'pending',
        mensagem: 'Configuração pendente',
        cor: 'yellow',
      };
    }

    if (statusConta.requirements.past_due.length > 0) {
      return {
        status: 'restricted',
        mensagem: 'Documentação em atraso',
        cor: 'red',
      };
    }

    if (statusConta.requirements.currently_due.length > 0) {
      return {
        status: 'restricted',
        mensagem: 'Documentação pendente',
        cor: 'yellow',
      };
    }

    if (statusConta.charges_enabled && statusConta.payouts_enabled) {
      return {
        status: 'active',
        mensagem: 'Conta ativa',
        cor: 'green',
      };
    }

    return {
      status: 'restricted',
      mensagem: 'Conta com restrições',
      cor: 'red',
    };
  }
}
