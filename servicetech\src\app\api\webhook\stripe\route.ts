import { NextResponse } from 'next/server';
import Stripe from 'stripe';
import { headers } from 'next/headers';
import { createAdminClient } from '@/utils/supabase/server';

// Verificar e inicializar o cliente Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

if (!stripeSecretKey) {
  console.error('❌ STRIPE_SECRET_KEY não está definida');
  throw new Error('STRIPE_SECRET_KEY é obrigatória');
}

if (!stripeWebhookSecret) {
  console.error('❌ STRIPE_WEBHOOK_SECRET não está definida');
  throw new Error('STRIPE_WEBHOOK_SECRET é obrigatória');
}

console.log('✅ Variáveis de ambiente Stripe configuradas');
const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2025-05-28.basil',
});

// Verificar e inicializar o cliente Supabase
console.log('✅ Inicializando cliente Supabase administrativo');
const supabase = createAdminClient();

export async function POST(request: Request) {
  console.log('🔔 Webhook do Stripe recebido');

  const body = await request.text();
  const headersList = await headers();
  const signature = headersList.get('stripe-signature');

  if (!signature) {
    console.error('❌ Assinatura do webhook não encontrada');
    return NextResponse.json(
      { error: 'Assinatura do webhook não encontrada' },
      { status: 400 }
    );
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      stripeWebhookSecret!
    );
    console.log(`✅ Webhook verificado: ${event.type}`);
  } catch (err: any) {
    console.error(`❌ Erro de verificação do webhook: ${err.message}`);
    return NextResponse.json(
      { error: `Webhook Error: ${err.message}` },
      { status: 400 }
    );
  }

  // Processar o evento
  try {
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        await handlePaymentIntentSucceeded(paymentIntent);
        break;
      }

      case 'payment_intent.payment_failed': {
        const failedPaymentIntent = event.data.object as Stripe.PaymentIntent;
        await handlePaymentIntentFailed(failedPaymentIntent);
        break;
      }

      case 'charge.dispute.created': {
        const dispute = event.data.object as Stripe.Dispute;
        await handleChargeDispute(dispute);
        break;
      }

      case 'account.updated': {
        const account = event.data.object as Stripe.Account;
        await handleAccountUpdated(account);
        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(subscription);
        break;
      }

      case 'customer.subscription.deleted': {
        const deletedSubscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionDeleted(deletedSubscription);
        break;
      }

      default:
        console.log(`Evento não tratado: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Erro ao processar webhook:', error);
    return NextResponse.json(
      { error: 'Erro ao processar webhook' },
      { status: 500 }
    );
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  // Extrair metadados do pagamento
  const {
    plano,
    estabelecimento,
    cnpj,
    user_id,
    agendamento_id,
    tipo_pagamento
  } = paymentIntent.metadata;

  console.log('Processando pagamento bem-sucedido:', {
    plano,
    estabelecimento,
    cnpj,
    user_id,
    agendamento_id,
    tipo_pagamento,
    amount: paymentIntent.amount
  });

  // Verificar se é pagamento de agendamento (apenas contas conectadas)
  if (tipo_pagamento === 'agendamento_conectado' && agendamento_id) {
    await handleAgendamentoPaymentSuccess(paymentIntent, parseInt(agendamento_id));
    return;
  }

  // Processar pagamento de assinatura SaaS (código existente)
  if (!plano) {
    console.log('Plano não especificado no PaymentIntent');
    return;
  }

  try {
    // Se temos user_id, atualizar o papel do usuário para Proprietario
    if (user_id) {
      console.log(`Atualizando usuário ${user_id} para Proprietario...`);

      // Primeiro, obter os metadados atuais do usuário
      const { data: currentUser, error: getUserError } = await supabase.auth.admin.getUserById(user_id);

      if (getUserError) {
        console.error('Erro ao obter usuário atual:', getUserError);
        return;
      }

      // Mesclar metadados existentes com novos dados
      const updatedMetadata = {
        ...currentUser.user.user_metadata,
        role: 'Proprietario',
        plano_selecionado: plano,
        pagamento_confirmado: true,
        onboarding_pendente: true,
        stripe_payment_id: paymentIntent.id,
        data_pagamento: new Date().toISOString()
      };

      const { error: userUpdateError } = await supabase.auth.admin.updateUserById(
        user_id,
        {
          user_metadata: updatedMetadata
        }
      );

      if (userUpdateError) {
        console.error('Erro ao atualizar papel do usuário:', userUpdateError);
      } else {
        console.log(`✅ Usuário ${user_id} promovido a Proprietario com plano ${plano}`);
      }
    } else {
      console.log('⚠️ user_id não fornecido nos metadados do pagamento');
    }

    // Buscar o estabelecimento pelo CNPJ (se já existir)
    const { data: estabelecimentoData, error: queryError } = await supabase
      .from('empresas')
      .select('*')
      .eq('cnpj', cnpj)
      .single();

    if (estabelecimentoData) {
      // Atualizar o status do pagamento do estabelecimento
      const { error: updateError } = await supabase
        .from('empresas')
        .update({
          status: 'ativo',
          stripe_customer_id: paymentIntent.id,
          updated_at: new Date().toISOString(),
        })
        .eq('empresa_id', estabelecimentoData.empresa_id);

      if (updateError) {
        console.error('Erro ao atualizar empresa:', updateError);
      }

      // Criar registro de pagamento
      const { error: paymentError } = await supabase
        .from('pagamentos')
        .insert([
          {
            empresa_id: estabelecimentoData.empresa_id,
            valor: paymentIntent.amount / 100, // Converter de centavos para reais
            status: 'sucesso',
            tipo: 'assinatura_saas',
            metodo_pagamento: 'stripe',
            stripe_payment_id: paymentIntent.id,
            created_at: new Date().toISOString(),
          },
        ]);

      if (paymentError) {
        console.error('Erro ao registrar pagamento:', paymentError);
      }
    }
  } catch (error) {
    console.error('Erro ao processar pagamento bem-sucedido:', error);
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  // Extrair o ID do cliente Stripe
  const customerId = subscription.customer as string;

  try {
    // Buscar o cliente no Stripe para obter metadados
    const customer = await stripe.customers.retrieve(customerId);
    
    if (!customer || customer.deleted) {
      console.log('Cliente não encontrado ou excluído');
      return;
    }

    // Buscar o estabelecimento pelo CNPJ nos metadados do cliente
    const cnpj = customer.metadata.cnpj;
    
    if (!cnpj) {
      console.log('CNPJ não encontrado nos metadados do cliente');
      return;
    }

    const { data: estabelecimentoData, error: queryError } = await supabase
      .from('estabelecimentos')
      .select('*')
      .eq('cnpj', cnpj)
      .single();

    if (queryError || !estabelecimentoData) {
      console.error('Erro ao buscar estabelecimento:', queryError);
      return;
    }

    // Atualizar informações da assinatura
    const { error: updateError } = await supabase
      .from('estabelecimentos')
      .update({
        stripe_subscription_id: subscription.id,
        assinatura_status: subscription.status,
        assinatura_periodo_atual_inicio: new Date((subscription as any).current_period_start * 1000).toISOString(),
        assinatura_periodo_atual_fim: new Date((subscription as any).current_period_end * 1000).toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', estabelecimentoData.id);

    if (updateError) {
      console.error('Erro ao atualizar assinatura do estabelecimento:', updateError);
    }
  } catch (error) {
    console.error('Erro ao processar atualização de assinatura:', error);
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  // Extrair o ID do cliente Stripe
  const customerId = subscription.customer as string;

  try {
    // Buscar o cliente no Stripe para obter metadados
    const customer = await stripe.customers.retrieve(customerId);
    
    if (!customer || customer.deleted) {
      console.log('Cliente não encontrado ou excluído');
      return;
    }

    // Buscar o estabelecimento pelo CNPJ nos metadados do cliente
    const cnpj = customer.metadata.cnpj;
    
    if (!cnpj) {
      console.log('CNPJ não encontrado nos metadados do cliente');
      return;
    }

    const { data: estabelecimentoData, error: queryError } = await supabase
      .from('estabelecimentos')
      .select('*')
      .eq('cnpj', cnpj)
      .single();

    if (queryError || !estabelecimentoData) {
      console.error('Erro ao buscar estabelecimento:', queryError);
      return;
    }

    // Atualizar status da assinatura para cancelada
    const { error: updateError } = await supabase
      .from('estabelecimentos')
      .update({
        assinatura_status: 'canceled',
        updated_at: new Date().toISOString(),
      })
      .eq('id', estabelecimentoData.id);

    if (updateError) {
      console.error('Erro ao atualizar status de assinatura cancelada:', updateError);
    }
  } catch (error) {
    console.error('Erro ao processar cancelamento de assinatura:', error);
  }
}

// Função para tratar pagamento de agendamento bem-sucedido
async function handleAgendamentoPaymentSuccess(paymentIntent: Stripe.PaymentIntent, agendamentoId: number) {
  try {
    console.log(`Processando pagamento de agendamento bem-sucedido: ${agendamentoId}`);

    // Atualizar status do agendamento
    const { error: updateError } = await supabase
      .from('agendamentos')
      .update({
        status_pagamento: 'Pago',
        updated_at: new Date().toISOString()
      })
      .eq('agendamento_id', agendamentoId);

    if (updateError) {
      console.error('Erro ao atualizar status de pagamento do agendamento:', updateError);
      return;
    }

    // Registrar pagamento na tabela de pagamentos
    const { error: pagamentoError } = await supabase
      .from('pagamentos')
      .insert([{
        agendamento_id: agendamentoId,
        stripe_payment_intent_id: paymentIntent.id,
        valor: paymentIntent.amount / 100, // Converter de centavos para reais
        status: 'Pago',
        metodo: 'stripe',
        tipo_pagamento: 'Agendamento',
        data_pagamento: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }]);

    if (pagamentoError) {
      console.error('Erro ao registrar pagamento na tabela:', pagamentoError);
    }

    console.log(`✅ Pagamento de agendamento ${agendamentoId} processado com sucesso`);

    // Enviar notificações (não bloquear a operação)
    try {
      const { notificarPagamentoConfirmado } = await import('@/utils/notificationHelpers');
      notificarPagamentoConfirmado(agendamentoId).catch(error => {
        console.error('❌ Erro ao enviar notificações de pagamento confirmado:', error);
      });
    } catch (error) {
      console.error('❌ Erro ao importar helper de notificações:', error);
    }

  } catch (error) {
    console.error('Erro ao processar pagamento de agendamento:', error);
  }
}

// Função para tratar falha de pagamento
async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  const { agendamento_id, tipo_pagamento } = paymentIntent.metadata;

  console.log('Processando falha de pagamento:', {
    payment_intent_id: paymentIntent.id,
    agendamento_id,
    tipo_pagamento,
    amount: paymentIntent.amount
  });

  // Se for pagamento de agendamento (apenas contas conectadas)
  if (tipo_pagamento === 'agendamento_conectado' && agendamento_id) {
    try {
      // Atualizar status do agendamento
      const { error: updateError } = await supabase
        .from('agendamentos')
        .update({
          status_pagamento: 'Falhou',
          updated_at: new Date().toISOString()
        })
        .eq('agendamento_id', parseInt(agendamento_id));

      if (updateError) {
        console.error('Erro ao atualizar status de falha de pagamento:', updateError);
      }

      console.log(`❌ Falha no pagamento do agendamento ${agendamento_id}`);
    } catch (error) {
      console.error('Erro ao processar falha de pagamento de agendamento:', error);
    }
  }
}

// Função para tratar disputas de cobrança
async function handleChargeDispute(dispute: Stripe.Dispute) {
  console.log('Processando disputa de cobrança:', {
    dispute_id: dispute.id,
    charge_id: dispute.charge,
    amount: dispute.amount,
    reason: dispute.reason
  });

  // Aqui você pode implementar lógica para lidar com disputas
  // Por exemplo, notificar administradores, marcar agendamentos como em disputa, etc.
}

// Função para tratar atualizações de conta conectada
async function handleAccountUpdated(account: Stripe.Account) {
  try {
    console.log('Processando atualização de conta conectada:', {
      account_id: account.id,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      details_submitted: account.details_submitted
    });

    // Atualizar status da conta na empresa
    const { error: updateError } = await supabase
      .from('empresas')
      .update({
        stripe_charges_enabled: account.charges_enabled,
        stripe_payouts_enabled: account.payouts_enabled,
        stripe_account_status: account.details_submitted ?
          (account.charges_enabled && account.payouts_enabled ? 'active' : 'restricted') :
          'pending',
        updated_at: new Date().toISOString()
      })
      .eq('stripe_account_id', account.id);

    if (updateError) {
      console.error('Erro ao atualizar status da conta conectada:', updateError);
    } else {
      console.log(`✅ Status da conta conectada ${account.id} atualizado`);
    }

  } catch (error) {
    console.error('Erro ao processar atualização de conta conectada:', error);
  }
}