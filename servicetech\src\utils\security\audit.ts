/**
 * Sistema de Auditoria de Segurança
 * Registra eventos de segurança e operações sensíveis
 */

export interface AuditEvent {
  id?: string;
  timestamp: string;
  userId?: string;
  userRole?: string;
  action: string;
  resource: string;
  resourceId?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  details?: Record<string, any>;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  category: 'AUTH' | 'DATA' | 'ADMIN' | 'PAYMENT' | 'SECURITY';
}

export interface SecurityAlert {
  id: string;
  timestamp: string;
  type: 'BRUTE_FORCE' | 'SUSPICIOUS_ACTIVITY' | 'DATA_BREACH' | 'UNAUTHORIZED_ACCESS' | 'RATE_LIMIT_EXCEEDED';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  userId?: string;
  ipAddress?: string;
  metadata?: Record<string, any>;
  resolved: boolean;
}

// Cache em memória para eventos de auditoria (em produção, usar banco de dados)
const auditLog: AuditEvent[] = [];
const securityAlerts: SecurityAlert[] = [];

/**
 * Registra evento de auditoria
 */
export function logAuditEvent(event: Omit<AuditEvent, 'id' | 'timestamp'>): void {
  const auditEvent: AuditEvent = {
    id: generateEventId(),
    timestamp: new Date().toISOString(),
    ...event
  };
  
  auditLog.push(auditEvent);
  
  // Manter apenas os últimos 10000 eventos em memória
  if (auditLog.length > 10000) {
    auditLog.splice(0, auditLog.length - 10000);
  }
  
  // Verificar se precisa gerar alerta
  checkForSecurityAlerts(auditEvent);
  
  // Log no console para desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    console.log(`[AUDIT] ${auditEvent.action} - ${auditEvent.success ? 'SUCCESS' : 'FAILED'} - Risk: ${auditEvent.riskLevel}`);
  }
}

/**
 * Gera ID único para evento
 */
function generateEventId(): string {
  return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Verifica se um evento deve gerar alerta de segurança
 */
function checkForSecurityAlerts(event: AuditEvent): void {
  // Detectar tentativas de força bruta
  if (!event.success && event.category === 'AUTH') {
    const recentFailures = auditLog.filter(e => 
      e.ipAddress === event.ipAddress &&
      e.category === 'AUTH' &&
      !e.success &&
      new Date(e.timestamp).getTime() > Date.now() - 15 * 60 * 1000 // Últimos 15 minutos
    );
    
    if (recentFailures.length >= 5) {
      createSecurityAlert({
        type: 'BRUTE_FORCE',
        severity: 'HIGH',
        description: `Múltiplas tentativas de login falharam para IP ${event.ipAddress}`,
        ipAddress: event.ipAddress,
        metadata: { failureCount: recentFailures.length }
      });
    }
  }
  
  // Detectar atividade suspeita
  if (event.riskLevel === 'CRITICAL') {
    createSecurityAlert({
      type: 'SUSPICIOUS_ACTIVITY',
      severity: 'CRITICAL',
      description: `Atividade crítica detectada: ${event.action}`,
      userId: event.userId,
      ipAddress: event.ipAddress,
      metadata: { event }
    });
  }
  
  // Detectar acesso não autorizado
  if (!event.success && event.category === 'ADMIN') {
    createSecurityAlert({
      type: 'UNAUTHORIZED_ACCESS',
      severity: 'HIGH',
      description: `Tentativa de acesso não autorizado a recurso administrativo`,
      userId: event.userId,
      ipAddress: event.ipAddress,
      metadata: { resource: event.resource }
    });
  }
}

/**
 * Cria alerta de segurança
 */
function createSecurityAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'resolved'>): void {
  const securityAlert: SecurityAlert = {
    id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date().toISOString(),
    resolved: false,
    ...alert
  };
  
  securityAlerts.push(securityAlert);
  
  // Log crítico
  console.error(`[SECURITY ALERT] ${securityAlert.type} - ${securityAlert.severity} - ${securityAlert.description}`);
  
  // Em produção, enviar para sistema de monitoramento
  if (process.env.NODE_ENV === 'production') {
    // TODO: Integrar com Sentry, DataDog, etc.
  }
}

/**
 * Funções de conveniência para diferentes tipos de eventos
 */
export const AuditLogger = {
  // Eventos de autenticação
  loginSuccess: (userId: string, ipAddress?: string, userAgent?: string) => {
    logAuditEvent({
      userId,
      action: 'LOGIN_SUCCESS',
      resource: 'auth',
      ipAddress,
      userAgent,
      success: true,
      riskLevel: 'LOW',
      category: 'AUTH'
    });
  },
  
  loginFailure: (email: string, ipAddress?: string, userAgent?: string, reason?: string) => {
    logAuditEvent({
      action: 'LOGIN_FAILURE',
      resource: 'auth',
      ipAddress,
      userAgent,
      success: false,
      riskLevel: 'MEDIUM',
      category: 'AUTH',
      details: { email, reason }
    });
  },
  
  logout: (userId: string, ipAddress?: string) => {
    logAuditEvent({
      userId,
      action: 'LOGOUT',
      resource: 'auth',
      ipAddress,
      success: true,
      riskLevel: 'LOW',
      category: 'AUTH'
    });
  },
  
  // Eventos de dados
  dataAccess: (userId: string, resource: string, resourceId?: string, success: boolean = true) => {
    logAuditEvent({
      userId,
      action: 'DATA_ACCESS',
      resource,
      resourceId,
      success,
      riskLevel: 'LOW',
      category: 'DATA'
    });
  },
  
  dataModification: (userId: string, resource: string, resourceId: string, action: 'CREATE' | 'UPDATE' | 'DELETE') => {
    logAuditEvent({
      userId,
      action: `DATA_${action}`,
      resource,
      resourceId,
      success: true,
      riskLevel: action === 'DELETE' ? 'MEDIUM' : 'LOW',
      category: 'DATA'
    });
  },
  
  // Eventos administrativos
  adminAction: (userId: string, action: string, resource: string, resourceId?: string, success: boolean = true) => {
    logAuditEvent({
      userId,
      action: `ADMIN_${action}`,
      resource,
      resourceId,
      success,
      riskLevel: 'HIGH',
      category: 'ADMIN'
    });
  },
  
  // Eventos de pagamento
  paymentProcessed: (userId: string, amount: number, paymentId: string, success: boolean) => {
    logAuditEvent({
      userId,
      action: 'PAYMENT_PROCESSED',
      resource: 'payment',
      resourceId: paymentId,
      success,
      riskLevel: success ? 'MEDIUM' : 'HIGH',
      category: 'PAYMENT',
      details: { amount }
    });
  },
  
  // Eventos de segurança
  securityViolation: (userId: string | undefined, violation: string, ipAddress?: string, details?: any) => {
    logAuditEvent({
      userId,
      action: 'SECURITY_VIOLATION',
      resource: 'security',
      ipAddress,
      success: false,
      riskLevel: 'CRITICAL',
      category: 'SECURITY',
      details: { violation, ...details }
    });
  },
  
  rateLimitExceeded: (identifier: string, endpoint: string, ipAddress?: string) => {
    logAuditEvent({
      action: 'RATE_LIMIT_EXCEEDED',
      resource: endpoint,
      ipAddress,
      success: false,
      riskLevel: 'MEDIUM',
      category: 'SECURITY',
      details: { identifier }
    });
    
    createSecurityAlert({
      type: 'RATE_LIMIT_EXCEEDED',
      severity: 'MEDIUM',
      description: `Rate limit excedido para ${identifier} no endpoint ${endpoint}`,
      ipAddress,
      metadata: { identifier, endpoint }
    });
  }
};

/**
 * Busca eventos de auditoria
 */
export function getAuditEvents(filters?: {
  userId?: string;
  action?: string;
  resource?: string;
  category?: string;
  riskLevel?: string;
  startDate?: string;
  endDate?: string;
  limit?: number;
}): AuditEvent[] {
  let events = [...auditLog];
  
  if (filters) {
    if (filters.userId) {
      events = events.filter(e => e.userId === filters.userId);
    }
    if (filters.action) {
      events = events.filter(e => e.action.includes(filters.action!));
    }
    if (filters.resource) {
      events = events.filter(e => e.resource === filters.resource);
    }
    if (filters.category) {
      events = events.filter(e => e.category === filters.category);
    }
    if (filters.riskLevel) {
      events = events.filter(e => e.riskLevel === filters.riskLevel);
    }
    if (filters.startDate) {
      events = events.filter(e => e.timestamp >= filters.startDate!);
    }
    if (filters.endDate) {
      events = events.filter(e => e.timestamp <= filters.endDate!);
    }
  }
  
  // Ordenar por timestamp (mais recente primeiro)
  events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  
  // Aplicar limite
  if (filters?.limit) {
    events = events.slice(0, filters.limit);
  }
  
  return events;
}

/**
 * Busca alertas de segurança
 */
export function getSecurityAlerts(filters?: {
  type?: string;
  severity?: string;
  resolved?: boolean;
  limit?: number;
}): SecurityAlert[] {
  let alerts = [...securityAlerts];
  
  if (filters) {
    if (filters.type) {
      alerts = alerts.filter(a => a.type === filters.type);
    }
    if (filters.severity) {
      alerts = alerts.filter(a => a.severity === filters.severity);
    }
    if (filters.resolved !== undefined) {
      alerts = alerts.filter(a => a.resolved === filters.resolved);
    }
  }
  
  // Ordenar por timestamp (mais recente primeiro)
  alerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  
  // Aplicar limite
  if (filters?.limit) {
    alerts = alerts.slice(0, filters.limit);
  }
  
  return alerts;
}

/**
 * Marca alerta como resolvido
 */
export function resolveSecurityAlert(alertId: string): boolean {
  const alert = securityAlerts.find(a => a.id === alertId);
  if (alert) {
    alert.resolved = true;
    return true;
  }
  return false;
}

/**
 * Estatísticas de auditoria
 */
export function getAuditStats(): {
  totalEvents: number;
  eventsByCategory: Record<string, number>;
  eventsByRiskLevel: Record<string, number>;
  recentFailures: number;
  activeAlerts: number;
} {
  const eventsByCategory: Record<string, number> = {};
  const eventsByRiskLevel: Record<string, number> = {};
  
  auditLog.forEach(event => {
    eventsByCategory[event.category] = (eventsByCategory[event.category] || 0) + 1;
    eventsByRiskLevel[event.riskLevel] = (eventsByRiskLevel[event.riskLevel] || 0) + 1;
  });
  
  const recentFailures = auditLog.filter(e => 
    !e.success && 
    new Date(e.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
  ).length;
  
  const activeAlerts = securityAlerts.filter(a => !a.resolved).length;
  
  return {
    totalEvents: auditLog.length,
    eventsByCategory,
    eventsByRiskLevel,
    recentFailures,
    activeAlerts
  };
}
