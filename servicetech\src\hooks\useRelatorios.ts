/**
 * Hook para gerenciar relatórios
 * Implementação da Tarefa #20 - Desenvolver Módu<PERSON> de Relatórios
 */

import { useState, useCallback, useEffect } from 'react';
import { 
  EstadoRelatorios, 
  FiltrosRelatorio, 
  RelatorioAgendamentosBasico,
  RelatorioAgendamentosCompleto,
  RelatorioFinanceiro,
  RelatorioColaboradores,
  RelatorioServicos,
  RelatorioApiResponse 
} from '@/types/relatorios';

export function useRelatorios() {
  const [estado, setEstado] = useState<EstadoRelatorios>({
    relatorioBasico: null,
    relatorioCompleto: null,
    relatorioFinanceiro: null,
    relatorioColaboradores: null,
    relatorioServicos: null,
    loading: false,
    error: null,
    ultimaAtualizacao: null
  });

  const [filtros, setFiltros] = useState<FiltrosRelatorio>({
    periodo: 'mes'
  });

  // Buscar relatório de agendamentos
  const buscarRelatorioAgendamentos = useCallback(async (filtrosPersonalizados?: Partial<FiltrosRelatorio>) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const filtrosAtivos = { ...filtros, ...filtrosPersonalizados };
      const searchParams = new URLSearchParams();

      // Adicionar parâmetros de filtro
      Object.entries(filtrosAtivos).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/relatorios/agendamentos?${searchParams}`);
      const result: RelatorioApiResponse<RelatorioAgendamentosBasico | RelatorioAgendamentosCompleto> = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar relatório de agendamentos');
      }

      setEstado(prev => ({
        ...prev,
        loading: false,
        ultimaAtualizacao: result.gerado_em,
        ...(result.plano_empresa === 'essencial' 
          ? { relatorioBasico: result.data as RelatorioAgendamentosBasico }
          : { relatorioCompleto: result.data as RelatorioAgendamentosCompleto }
        )
      }));

      return result;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      throw error;
    }
  }, [filtros]);

  // Buscar relatório financeiro (Premium)
  const buscarRelatorioFinanceiro = useCallback(async (filtrosPersonalizados?: Partial<FiltrosRelatorio>) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const filtrosAtivos = { ...filtros, ...filtrosPersonalizados };
      const searchParams = new URLSearchParams();

      Object.entries(filtrosAtivos).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/relatorios/financeiro?${searchParams}`);
      const result: RelatorioApiResponse<RelatorioFinanceiro> = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar relatório financeiro');
      }

      setEstado(prev => ({
        ...prev,
        loading: false,
        relatorioFinanceiro: result.data,
        ultimaAtualizacao: result.gerado_em
      }));

      return result;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      throw error;
    }
  }, [filtros]);

  // Buscar relatório de colaboradores (Premium)
  const buscarRelatorioColaboradores = useCallback(async (filtrosPersonalizados?: Partial<FiltrosRelatorio>) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const filtrosAtivos = { ...filtros, ...filtrosPersonalizados };
      const searchParams = new URLSearchParams();

      Object.entries(filtrosAtivos).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/relatorios/colaboradores?${searchParams}`);
      const result: RelatorioApiResponse<RelatorioColaboradores> = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar relatório de colaboradores');
      }

      setEstado(prev => ({
        ...prev,
        loading: false,
        relatorioColaboradores: result.data,
        ultimaAtualizacao: result.gerado_em
      }));

      return result;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      throw error;
    }
  }, [filtros]);

  // Buscar relatório de serviços (Premium)
  const buscarRelatorioServicos = useCallback(async (filtrosPersonalizados?: Partial<FiltrosRelatorio>) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const filtrosAtivos = { ...filtros, ...filtrosPersonalizados };
      const searchParams = new URLSearchParams();

      Object.entries(filtrosAtivos).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/relatorios/servicos?${searchParams}`);
      const result: RelatorioApiResponse<RelatorioServicos> = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar relatório de serviços');
      }

      setEstado(prev => ({
        ...prev,
        loading: false,
        relatorioServicos: result.data,
        ultimaAtualizacao: result.gerado_em
      }));

      return result;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      throw error;
    }
  }, [filtros]);

  // Aplicar filtros
  const aplicarFiltros = useCallback((novosFiltros: Partial<FiltrosRelatorio>) => {
    setFiltros(prev => ({ ...prev, ...novosFiltros }));
  }, []);

  // Limpar filtros
  const limparFiltros = useCallback(() => {
    setFiltros({ periodo: 'mes' });
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  // Atualizar todos os relatórios
  const atualizarTodosRelatorios = useCallback(async () => {
    try {
      await Promise.all([
        buscarRelatorioAgendamentos(),
        // Os outros relatórios só são buscados se necessário (Premium)
      ]);
    } catch (error) {
      console.error('Erro ao atualizar relatórios:', error);
    }
  }, [buscarRelatorioAgendamentos]);

  // Exportar relatório
  const exportarRelatorio = useCallback(async (tipo: 'pdf' | 'excel' | 'csv', relatorio: string) => {
    try {
      const searchParams = new URLSearchParams();
      searchParams.append('tipo', relatorio);
      searchParams.append('formato', tipo);
      
      Object.entries(filtros).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/relatorios/exportar?${searchParams}`);
      
      if (!response.ok) {
        throw new Error('Erro ao exportar relatório');
      }

      // Fazer download do arquivo
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `relatorio_${relatorio}_${new Date().toISOString().split('T')[0]}.${tipo}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (error: any) {
      setEstado(prev => ({ ...prev, error: error.message }));
      throw error;
    }
  }, [filtros]);

  // Carregar relatório inicial
  useEffect(() => {
    buscarRelatorioAgendamentos();
  }, []);

  return {
    // Estado
    ...estado,
    filtros,

    // Ações
    buscarRelatorioAgendamentos,
    buscarRelatorioFinanceiro,
    buscarRelatorioColaboradores,
    buscarRelatorioServicos,
    aplicarFiltros,
    limparFiltros,
    limparErro,
    atualizarTodosRelatorios,
    exportarRelatorio,

    // Utilitários
    temDados: !!(estado.relatorioBasico || estado.relatorioCompleto),
    isPlanoEssencial: !!estado.relatorioBasico && !estado.relatorioCompleto,
    isPlanoPremium: !!estado.relatorioCompleto
  };
}
