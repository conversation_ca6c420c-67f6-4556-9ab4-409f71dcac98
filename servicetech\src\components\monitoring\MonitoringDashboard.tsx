'use client';

import React, { useState } from 'react';
import { useMonitoring, LogLevel, LogFilters } from '@/hooks/useMonitoring';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Download,
  RefreshCw,
  Search,
  Filter,
  TrendingUp,
  Server,
  Database,
  Cpu,
  MemoryStick
} from 'lucide-react';

export default function MonitoringDashboard() {
  const { data, loading, error, fetchLogs, createManualLog, testLogging, exportLogs, refresh } = useMonitoring();
  const [filters, setFilters] = useState<LogFilters>({
    limit: 50,
    offset: 0
  });
  const [showFilters, setShowFilters] = useState(false);

  // Handlers
  const handleFilterChange = (key: keyof LogFilters, value: any) => {
    const newFilters = { ...filters, [key]: value, offset: 0 };
    setFilters(newFilters);
    fetchLogs(newFilters);
  };

  const handleTestLogging = async () => {
    try {
      await testLogging();
      alert('Teste de logging executado com sucesso!');
    } catch (err) {
      alert('Erro ao executar teste de logging');
    }
  };

  const handleExportLogs = async () => {
    try {
      const exportData = await exportLogs('json', filters);
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `logs_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      alert('Erro ao exportar logs');
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('pt-BR');
  };

  const getLevelColor = (level: LogLevel) => {
    switch (level) {
      case LogLevel.DEBUG: return 'text-gray-600';
      case LogLevel.INFO: return 'text-blue-600';
      case LogLevel.WARN: return 'text-yellow-600';
      case LogLevel.ERROR: return 'text-red-600';
      case LogLevel.CRITICAL: return 'text-red-800';
      default: return 'text-gray-600';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className="p-6">
        <Card className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <XCircle className="h-5 w-5" />
            <span>Erro ao carregar dados de monitoramento: {error}</span>
          </div>
          <Button onClick={refresh} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar Novamente
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Monitoramento do Sistema</h1>
          <p className="text-gray-600">Logs, métricas e performance em tempo real</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleTestLogging} variant="outline">
            <Activity className="h-4 w-4 mr-2" />
            Testar Logging
          </Button>
          <Button onClick={handleExportLogs} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar Logs
          </Button>
          <Button onClick={refresh}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Métricas do Sistema */}
      {data?.metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Memória</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.metrics.memory.percentage.toFixed(1)}%
                </p>
                <p className="text-xs text-gray-500">
                  {formatBytes(data.metrics.memory.used)} / {formatBytes(data.metrics.memory.total)}
                </p>
              </div>
              <MemoryStick className="h-8 w-8 text-blue-600" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">CPU</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.metrics.cpu.usage.toFixed(1)}ms
                </p>
                <p className="text-xs text-gray-500">Tempo de CPU</p>
              </div>
              <Cpu className="h-8 w-8 text-green-600" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Requisições</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.metrics.requests.total}
                </p>
                <p className="text-xs text-gray-500">
                  {data.metrics.requests.errors} erros
                </p>
              </div>
              <Server className="h-8 w-8 text-purple-600" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Tempo Médio</p>
                <p className="text-2xl font-bold text-gray-900">
                  {data.metrics.requests.averageResponseTime.toFixed(0)}ms
                </p>
                <p className="text-xs text-gray-500">Resposta da API</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </Card>
        </div>
      )}

      {/* Estatísticas dos Logs */}
      {data?.stats && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Estatísticas dos Logs</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{data.stats.total}</p>
              <p className="text-sm text-gray-600">Total</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{data.stats.byLevel.info || 0}</p>
              <p className="text-sm text-gray-600">Info</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{data.stats.byLevel.warn || 0}</p>
              <p className="text-sm text-gray-600">Avisos</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{data.stats.byLevel.error || 0}</p>
              <p className="text-sm text-gray-600">Erros</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-800">{data.stats.byLevel.critical || 0}</p>
              <p className="text-sm text-gray-600">Críticos</p>
            </div>
          </div>
        </Card>
      )}

      {/* Filtros */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Logs do Sistema</h2>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </Button>
        </div>

        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nível
              </label>
              <select
                value={filters.level || ''}
                onChange={(e) => handleFilterChange('level', e.target.value || undefined)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Todos</option>
                <option value={LogLevel.DEBUG}>Debug</option>
                <option value={LogLevel.INFO}>Info</option>
                <option value={LogLevel.WARN}>Aviso</option>
                <option value={LogLevel.ERROR}>Erro</option>
                <option value={LogLevel.CRITICAL}>Crítico</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Início
              </label>
              <Input
                type="datetime-local"
                value={filters.startDate || ''}
                onChange={(e) => handleFilterChange('startDate', e.target.value || undefined)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Data Fim
              </label>
              <Input
                type="datetime-local"
                value={filters.endDate || ''}
                onChange={(e) => handleFilterChange('endDate', e.target.value || undefined)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Buscar
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Buscar logs..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        )}

        {/* Lista de Logs */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-gray-400" />
              <p className="text-gray-600 mt-2">Carregando logs...</p>
            </div>
          ) : data?.logs && data.logs.length > 0 ? (
            data.logs.map((log) => (
              <div
                key={log.id}
                className="flex items-start space-x-3 p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${getLevelColor(log.level).replace('text-', 'bg-')}`} />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className={`text-sm font-medium ${getLevelColor(log.level)}`}>
                      {log.level.toUpperCase()}
                    </p>
                    <div className="flex items-center space-x-2">
                      {log.riskLevel && (
                        <span className={`px-2 py-1 text-xs rounded-full ${getRiskLevelColor(log.riskLevel)}`}>
                          {log.riskLevel}
                        </span>
                      )}
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(log.timestamp)}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-900 mt-1">{log.message}</p>
                  {log.context && Object.keys(log.context).length > 0 && (
                    <details className="mt-2">
                      <summary className="text-xs text-gray-500 cursor-pointer">
                        Ver contexto
                      </summary>
                      <pre className="text-xs text-gray-600 mt-1 bg-gray-100 p-2 rounded overflow-x-auto">
                        {JSON.stringify(log.context, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="h-8 w-8 mx-auto text-gray-400" />
              <p className="text-gray-600 mt-2">Nenhum log encontrado</p>
            </div>
          )}
        </div>

        {/* Paginação */}
        {data?.pagination && data.pagination.hasMore && (
          <div className="mt-4 text-center">
            <Button
              variant="outline"
              onClick={() => {
                const newFilters = { ...filters, offset: (filters.offset || 0) + (filters.limit || 50) };
                setFilters(newFilters);
                fetchLogs(newFilters);
              }}
            >
              Carregar Mais
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
}
