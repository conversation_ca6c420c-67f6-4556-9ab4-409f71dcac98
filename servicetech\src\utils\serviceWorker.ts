// Utilitários para Service Worker e notificações push

/**
 * Registrar Service Worker
 */
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.warn('Service Worker não suportado neste ambiente');
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    });

    console.log('Service Worker registrado com sucesso:', registration);

    // Escutar atualizações do Service Worker
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // Novo Service Worker disponível
            console.log('Nova versão do Service Worker disponível');
            
            // Notificar o usuário sobre a atualização
            if (confirm('Uma nova versão está disponível. Deseja atualizar?')) {
              newWorker.postMessage({ type: 'SKIP_WAITING' });
              window.location.reload();
            }
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('Erro ao registrar Service Worker:', error);
    return null;
  }
}

/**
 * Desregistrar Service Worker
 */
export async function unregisterServiceWorker(): Promise<boolean> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      console.log('Service Worker desregistrado:', result);
      return result;
    }
    return false;
  } catch (error) {
    console.error('Erro ao desregistrar Service Worker:', error);
    return false;
  }
}

/**
 * Verificar se notificações push são suportadas
 */
export function isPushNotificationSupported(): boolean {
  return (
    typeof window !== 'undefined' &&
    'serviceWorker' in navigator &&
    'PushManager' in window &&
    'Notification' in window
  );
}

/**
 * Solicitar permissão para notificações
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!isPushNotificationSupported()) {
    throw new Error('Push notifications não suportadas');
  }

  if (Notification.permission === 'granted') {
    return 'granted';
  }

  if (Notification.permission === 'denied') {
    throw new Error('Permissão para notificações foi negada');
  }

  const permission = await Notification.requestPermission();
  return permission;
}

/**
 * Obter token de registro push (simulado - seria implementado com Firebase)
 */
export async function getPushSubscription(): Promise<PushSubscription | null> {
  if (!isPushNotificationSupported()) {
    return null;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (!registration) {
      throw new Error('Service Worker não registrado');
    }

    // Verificar se já existe uma subscription
    let subscription = await registration.pushManager.getSubscription();
    
    if (!subscription) {
      // Criar nova subscription
      const vapidKey = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;
      if (!vapidKey) {
        throw new Error('VAPID key não configurada');
      }

      subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidKey)
      });
    }

    return subscription;
  } catch (error) {
    console.error('Erro ao obter push subscription:', error);
    return null;
  }
}

/**
 * Converter VAPID key de base64 para Uint8Array
 */
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Enviar notificação de teste local
 */
export function sendTestNotification(title: string, body: string, options?: NotificationOptions): Notification | null {
  if (!isPushNotificationSupported() || Notification.permission !== 'granted') {
    console.warn('Notificações não permitidas');
    return null;
  }

  const notification = new Notification(title, {
    body,
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    tag: 'test-notification',
    requireInteraction: true,
    ...options
  });

  // Auto-fechar após 5 segundos
  setTimeout(() => {
    notification.close();
  }, 5000);

  return notification;
}

/**
 * Verificar status das notificações
 */
export function getNotificationStatus() {
  return {
    supported: isPushNotificationSupported(),
    permission: typeof window !== 'undefined' && 'Notification' in window 
      ? Notification.permission 
      : 'default',
    serviceWorkerRegistered: typeof window !== 'undefined' && 'serviceWorker' in navigator
      ? navigator.serviceWorker.controller !== null
      : false
  };
}

/**
 * Hook para inicializar notificações push
 */
export async function initializePushNotifications(): Promise<{
  success: boolean;
  subscription?: PushSubscription;
  error?: string;
}> {
  try {
    // 1. Verificar suporte
    if (!isPushNotificationSupported()) {
      throw new Error('Push notifications não suportadas');
    }

    // 2. Registrar Service Worker
    const registration = await registerServiceWorker();
    if (!registration) {
      throw new Error('Falha ao registrar Service Worker');
    }

    // 3. Solicitar permissão
    const permission = await requestNotificationPermission();
    if (permission !== 'granted') {
      throw new Error('Permissão para notificações negada');
    }

    // 4. Obter subscription
    const subscription = await getPushSubscription();
    if (!subscription) {
      throw new Error('Falha ao obter push subscription');
    }

    return {
      success: true,
      subscription
    };

  } catch (error) {
    console.error('Erro ao inicializar push notifications:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
}
