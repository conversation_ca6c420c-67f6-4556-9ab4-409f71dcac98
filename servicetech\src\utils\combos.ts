// Utilitários para combos de serviços

import { ComboCompleto, ComboDetectado, TipoDesconto, StatusCombo } from '@/types/combos';

/**
 * Calcula o valor original de um combo (soma dos preços dos serviços)
 */
export function calcularValorOriginal(combo: ComboCompleto): number {
  return combo.itens.reduce((total, item) => {
    return total + (item.servico.preco * item.quantidade);
  }, 0);
}

/**
 * Calcula o valor com desconto de um combo
 */
export function calcularValorComDesconto(combo: ComboCompleto): number {
  const valorOriginal = calcularValorOriginal(combo);
  
  if (combo.desconto_valor_fixo && combo.desconto_valor_fixo > 0) {
    return Math.max(0, valorOriginal - combo.desconto_valor_fixo);
  }
  
  if (combo.desconto_percentual && combo.desconto_percentual > 0) {
    const desconto = (valorOriginal * combo.desconto_percentual) / 100;
    return Math.max(0, valorOriginal - desconto);
  }
  
  return valorOriginal;
}

/**
 * Calcula a economia de um combo
 */
export function calcularEconomia(combo: ComboCompleto): number {
  const valorOriginal = calcularValorOriginal(combo);
  const valorComDesconto = calcularValorComDesconto(combo);
  return valorOriginal - valorComDesconto;
}

/**
 * Determina o tipo de desconto de um combo
 */
export function obterTipoDesconto(combo: ComboCompleto): TipoDesconto | null {
  if (combo.desconto_valor_fixo && combo.desconto_valor_fixo > 0) {
    return 'valor_fixo';
  }
  
  if (combo.desconto_percentual && combo.desconto_percentual > 0) {
    return 'percentual';
  }
  
  return null;
}

/**
 * Verifica se um combo está ativo e válido
 */
export function verificarStatusCombo(combo: ComboCompleto): StatusCombo {
  if (!combo.ativo) {
    return 'inativo';
  }
  
  const agora = new Date();
  
  // Verificar data de início
  if (combo.data_inicio) {
    const dataInicio = new Date(combo.data_inicio);
    if (agora < dataInicio) {
      return 'inativo';
    }
  }
  
  // Verificar data de fim
  if (combo.data_fim) {
    const dataFim = new Date(combo.data_fim);
    if (agora > dataFim) {
      return 'expirado';
    }
  }
  
  // Verificar limite de usos
  if (combo.limite_usos && combo.usos_realizados >= combo.limite_usos) {
    return 'esgotado';
  }
  
  return 'ativo';
}

/**
 * Detecta combos aplicáveis baseado nos serviços selecionados
 */
export function detectarCombosAplicaveis(
  servicosSelecionados: number[],
  combosDisponiveis: ComboCompleto[]
): ComboDetectado[] {
  const combosDetectados: ComboDetectado[] = [];
  
  for (const combo of combosDisponiveis) {
    const status = verificarStatusCombo(combo);
    if (status !== 'ativo') continue;
    
    const servicosDoCombo = combo.itens.map(item => item.servico_id);
    const servicosObrigatorios = combo.itens
      .filter(item => item.obrigatorio)
      .map(item => item.servico_id);
    
    // Verificar se todos os serviços obrigatórios estão selecionados
    const temTodosObrigatorios = servicosObrigatorios.every(servicoId =>
      servicosSelecionados.includes(servicoId)
    );
    
    // Verificar quantos serviços do combo estão selecionados
    const servicosComboSelecionados = servicosDoCombo.filter(servicoId =>
      servicosSelecionados.includes(servicoId)
    );
    
    // Verificar serviços faltantes
    const servicosFaltantes = servicosDoCombo.filter(servicoId =>
      !servicosSelecionados.includes(servicoId)
    );
    
    // Pode aplicar se tem todos obrigatórios e pelo menos 2 serviços do combo
    const podeAplicar = temTodosObrigatorios && servicosComboSelecionados.length >= 2;
    
    if (servicosComboSelecionados.length > 0) {
      const valorOriginal = calcularValorOriginal(combo);
      const valorComDesconto = calcularValorComDesconto(combo);
      const economia = calcularEconomia(combo);
      
      combosDetectados.push({
        combo,
        servicos_selecionados: servicosComboSelecionados,
        servicos_faltantes: servicosFaltantes,
        pode_aplicar: podeAplicar,
        valor_original: valorOriginal,
        valor_com_desconto: valorComDesconto,
        economia
      });
    }
  }
  
  // Ordenar por economia (maior primeiro)
  return combosDetectados.sort((a, b) => b.economia - a.economia);
}

/**
 * Aplica o melhor combo disponível aos serviços selecionados
 */
export function aplicarMelhorCombo(
  servicosSelecionados: number[],
  combosDisponiveis: ComboCompleto[]
): ComboDetectado | null {
  const combosAplicaveis = detectarCombosAplicaveis(servicosSelecionados, combosDisponiveis);
  const combosValidos = combosAplicaveis.filter(combo => combo.pode_aplicar);
  
  return combosValidos.length > 0 ? combosValidos[0] : null;
}

/**
 * Formata o desconto para exibição
 */
export function formatarDesconto(combo: ComboCompleto): string {
  const tipo = obterTipoDesconto(combo);
  
  if (tipo === 'valor_fixo' && combo.desconto_valor_fixo) {
    return `R$ ${combo.desconto_valor_fixo.toFixed(2)}`;
  }
  
  if (tipo === 'percentual' && combo.desconto_percentual) {
    return `${combo.desconto_percentual}%`;
  }
  
  return 'Sem desconto';
}

/**
 * Valida os dados de um combo
 */
export function validarCombo(dados: any): { valido: boolean; erros: string[] } {
  const erros: string[] = [];
  
  if (!dados.nome_combo || dados.nome_combo.trim().length < 3) {
    erros.push('Nome do combo deve ter pelo menos 3 caracteres');
  }
  
  if (!dados.descricao || dados.descricao.trim().length < 10) {
    erros.push('Descrição deve ter pelo menos 10 caracteres');
  }
  
  if (!dados.itens || dados.itens.length < 2) {
    erros.push('Combo deve ter pelo menos 2 serviços');
  }
  
  const temDescontoFixo = dados.desconto_valor_fixo && dados.desconto_valor_fixo > 0;
  const temDescontoPercentual = dados.desconto_percentual && dados.desconto_percentual > 0;
  
  if (!temDescontoFixo && !temDescontoPercentual) {
    erros.push('Combo deve ter desconto fixo ou percentual');
  }
  
  if (temDescontoFixo && temDescontoPercentual) {
    erros.push('Combo não pode ter desconto fixo e percentual ao mesmo tempo');
  }
  
  if (dados.desconto_percentual && (dados.desconto_percentual < 1 || dados.desconto_percentual > 90)) {
    erros.push('Desconto percentual deve estar entre 1% e 90%');
  }
  
  if (dados.limite_usos && dados.limite_usos < 1) {
    erros.push('Limite de usos deve ser maior que zero');
  }
  
  return {
    valido: erros.length === 0,
    erros
  };
}

/**
 * Calcula a duração total de um combo
 */
export function calcularDuracaoTotal(combo: ComboCompleto): number {
  return combo.itens.reduce((total, item) => {
    return total + (item.servico.duracao_minutos * item.quantidade);
  }, 0);
}
