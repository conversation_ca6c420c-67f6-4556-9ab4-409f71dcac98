'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  Combo, 
  ComboCompleto, 
  CriarComboData, 
  AtualizarComboData, 
  FiltrosCombos, 
  ComboApiResponse,
  EstatisticasCombos,
  ComboDetectado
} from '@/types/combos';
import { detectarCombosAplicaveis, aplicarMelhorCombo } from '@/utils/combos';

export function useCombos() {
  const [combos, setCombos] = useState<ComboCompleto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [estatisticas, setEstatisticas] = useState<EstatisticasCombos | undefined>(undefined);

  // Buscar combos
  const buscarCombos = useCallback(async (filtros?: FiltrosCombos) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (filtros?.ativo !== undefined) {
        params.append('ativo', filtros.ativo.toString());
      }
      if (filtros?.busca) {
        params.append('busca', filtros.busca);
      }
      if (filtros?.data_inicio) {
        params.append('data_inicio', filtros.data_inicio);
      }
      if (filtros?.data_fim) {
        params.append('data_fim', filtros.data_fim);
      }

      const url = `/api/combos${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await fetch(url);
      const data: ComboApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar combos');
      }

      if (data.success && Array.isArray(data.data)) {
        setCombos(data.data as ComboCompleto[]);
        calcularEstatisticas(data.data as ComboCompleto[]);
      } else {
        throw new Error('Formato de resposta inválido');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao buscar combos:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Buscar combo específico
  const buscarCombo = useCallback(async (comboId: number): Promise<ComboCompleto | null> => {
    try {
      const response = await fetch(`/api/combos/${comboId}`);
      const data: ComboApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao buscar combo');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        return data.data as ComboCompleto;
      }

      return null;
    } catch (err) {
      console.error('Erro ao buscar combo:', err);
      return null;
    }
  }, []);

  // Criar combo
  const criarCombo = useCallback(async (dadosCombo: CriarComboData): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/combos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosCombo),
      });

      const data: ComboApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao criar combo');
      }

      if (data.success) {
        // Recarregar lista de combos
        await buscarCombos();
        return true;
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao criar combo:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarCombos]);

  // Atualizar combo
  const atualizarCombo = useCallback(async (
    comboId: number, 
    dadosAtualizacao: AtualizarComboData
  ): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/combos/${comboId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizacao),
      });

      const data: ComboApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao atualizar combo');
      }

      if (data.success) {
        // Recarregar lista de combos
        await buscarCombos();
        return true;
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao atualizar combo:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarCombos]);

  // Excluir combo
  const excluirCombo = useCallback(async (comboId: number): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/combos/${comboId}`, {
        method: 'DELETE',
      });

      const data: ComboApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao excluir combo');
      }

      if (data.success) {
        // Recarregar lista de combos
        await buscarCombos();
        return true;
      }

      return false;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      console.error('Erro ao excluir combo:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [buscarCombos]);

  // Alternar status ativo/inativo
  const alternarStatusCombo = useCallback(async (comboId: number, ativo: boolean): Promise<boolean> => {
    return await atualizarCombo(comboId, { ativo });
  }, [atualizarCombo]);

  // Detectar combos aplicáveis
  const detectarCombos = useCallback((servicosSelecionados: number[]): ComboDetectado[] => {
    const combosAtivos = combos.filter(combo => combo.ativo);
    return detectarCombosAplicaveis(servicosSelecionados, combosAtivos);
  }, [combos]);

  // Aplicar melhor combo
  const aplicarMelhorComboDisponivel = useCallback((servicosSelecionados: number[]): ComboDetectado | null => {
    const combosAtivos = combos.filter(combo => combo.ativo);
    return aplicarMelhorCombo(servicosSelecionados, combosAtivos);
  }, [combos]);

  // Calcular estatísticas
  const calcularEstatisticas = useCallback((combosData: ComboCompleto[]) => {
    const stats: EstatisticasCombos = {
      total: combosData.length,
      ativos: combosData.filter(combo => combo.ativo).length,
      inativos: combosData.filter(combo => !combo.ativo).length,
      total_usos: combosData.reduce((total, combo) => total + combo.usos_realizados, 0),
      economia_total: combosData.reduce((total, combo) => total + (combo.economia * combo.usos_realizados), 0)
    };
    setEstatisticas(stats);
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setError(null);
  }, []);

  // Carregar combos na inicialização
  useEffect(() => {
    buscarCombos();
  }, [buscarCombos]);

  return {
    // Estado
    combos,
    loading,
    error,
    estatisticas,

    // Ações
    buscarCombos,
    buscarCombo,
    criarCombo,
    atualizarCombo,
    excluirCombo,
    alternarStatusCombo,
    detectarCombos,
    aplicarMelhorComboDisponivel,
    limparErro
  };
}
