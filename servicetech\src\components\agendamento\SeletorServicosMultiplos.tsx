'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ServicoSelecionado } from '@/types/agendamentos';
import { ComboDetectado } from '@/types/combos';

interface Servico {
  servico_id: number;
  nome_servico: string;
  descricao: string;
  duracao_minutos: number;
  preco: number;
  categoria: string;
}

interface SeletorServicosMultiplosProps {
  servicos: Servico[];
  servicosPorCategoria: Record<string, Servico[]>;
  servicosSelecionados: ServicoSelecionado[];
  combosDisponiveis: ComboDetectado[];
  comboAplicado: ComboDetectado | null;
  onAdicionarServico: (servico: ServicoSelecionado) => void;
  onRemoverServico: (servico_id: number) => void;
  loading?: boolean;
}

export function SeletorServicosMultiplos({
  servicos,
  servicosPorCategoria,
  servicosSelecionados,
  combosDisponiveis,
  comboAplicado,
  onAdicionarServico,
  onRemoverServico,
  loading = false
}: SeletorServicosMultiplosProps) {
  const [categoriaExpandida, setCategoriaExpandida] = useState<string | null>(null);
  const [visualizacao, setVisualizacao] = useState<'lista' | 'categoria'>('categoria');

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarDuracao = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos}min`;
    }
    const horas = Math.floor(minutos / 60);
    const minutosRestantes = minutos % 60;
    return minutosRestantes > 0 ? `${horas}h ${minutosRestantes}min` : `${horas}h`;
  };

  const isServicoSelecionado = (servico_id: number) => {
    return servicosSelecionados.some(s => s.servico_id === servico_id);
  };

  const handleToggleServico = (servico: Servico) => {
    if (isServicoSelecionado(servico.servico_id)) {
      onRemoverServico(servico.servico_id);
    } else {
      onAdicionarServico({
        servico_id: servico.servico_id,
        nome_servico: servico.nome_servico,
        duracao_minutos: servico.duracao_minutos,
        preco: servico.preco,
        categoria: servico.categoria
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary)]"></div>
          <p className="text-[var(--text-secondary)] mt-4">Carregando serviços...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header com controles */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-[var(--text-primary)]">
            Selecione os Serviços
          </h3>
          <p className="text-sm text-[var(--text-secondary)]">
            Você pode selecionar múltiplos serviços. Combos serão aplicados automaticamente.
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant={visualizacao === 'categoria' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setVisualizacao('categoria')}
          >
            Por Categoria
          </Button>
          <Button
            variant={visualizacao === 'lista' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setVisualizacao('lista')}
          >
            Lista
          </Button>
        </div>
      </div>

      {/* Serviços selecionados */}
      {servicosSelecionados.length > 0 && (
        <Card className="border-[var(--primary)] bg-[var(--primary-light)]">
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Serviços Selecionados</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {servicosSelecionados.map((servico) => (
              <div
                key={servico.servico_id}
                className="flex items-center justify-between p-3 bg-white rounded-lg border"
              >
                <div className="flex-1">
                  <h4 className="font-medium text-[var(--text-primary)]">
                    {servico.nome_servico}
                  </h4>
                  <div className="flex items-center gap-4 text-sm text-[var(--text-secondary)]">
                    <span>{formatarDuracao(servico.duracao_minutos)}</span>
                    <span>{formatarPreco(servico.preco)}</span>
                    {servico.categoria && <span>• {servico.categoria}</span>}
                  </div>
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => onRemoverServico(servico.servico_id)}
                  className="text-red-600 hover:bg-red-50"
                >
                  Remover
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Combo aplicado */}
      {comboAplicado && (
        <Card className="border-green-500 bg-green-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base text-green-700 flex items-center gap-2">
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Combo Aplicado!
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <h4 className="font-medium text-green-800">{comboAplicado.combo.nome_combo}</h4>
              <p className="text-sm text-green-700">{comboAplicado.combo.descricao}</p>
              <div className="flex items-center gap-4 text-sm">
                <span className="text-green-700">
                  Economia: <strong>{formatarPreco(comboAplicado.economia)}</strong>
                </span>
                <span className="text-green-600">
                  {comboAplicado.combo.desconto_valor_fixo 
                    ? `Desconto: ${formatarPreco(comboAplicado.combo.desconto_valor_fixo)}`
                    : `Desconto: ${comboAplicado.combo.desconto_percentual}%`
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sugestões de combos */}
      {combosDisponiveis.length > 0 && !comboAplicado && (
        <Card className="border-yellow-500 bg-yellow-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-base text-yellow-700 flex items-center gap-2">
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Combos Disponíveis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {combosDisponiveis.slice(0, 2).map((combo) => (
              <div key={combo.combo.combo_id} className="p-3 bg-white rounded-lg border">
                <h4 className="font-medium text-yellow-800">{combo.combo.nome_combo}</h4>
                <p className="text-sm text-yellow-700 mb-2">{combo.combo.descricao}</p>
                {combo.servicos_faltantes.length > 0 && (
                  <p className="text-xs text-yellow-600">
                    Adicione mais {combo.servicos_faltantes.length} serviço(s) para ativar este combo
                  </p>
                )}
                <div className="text-sm text-yellow-700 mt-1">
                  Economia potencial: <strong>{formatarPreco(combo.economia)}</strong>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Lista de serviços */}
      {visualizacao === 'categoria' && (
        <div className="space-y-4">
          {Object.entries(servicosPorCategoria).map(([categoria, servicosCategoria]) => (
            <Card key={categoria}>
              <div
                className="cursor-pointer p-4 hover:bg-[var(--surface-hover)] transition-colors"
                onClick={() => setCategoriaExpandida(
                  categoriaExpandida === categoria ? null : categoria
                )}
              >
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-[var(--text-primary)]">{categoria}</h4>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-[var(--text-secondary)]">
                      {servicosCategoria.length} serviços
                    </span>
                    <svg
                      className={`w-5 h-5 text-[var(--text-secondary)] transition-transform ${
                        categoriaExpandida === categoria ? 'rotate-180' : ''
                      }`}
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {categoriaExpandida === categoria && (
                <CardContent className="p-0">
                  <div className="space-y-2 p-4">
                    {servicosCategoria.map((servico) => (
                      <div
                        key={servico.servico_id}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                          isServicoSelecionado(servico.servico_id)
                            ? 'border-[var(--primary)] bg-[var(--primary-light)]'
                            : 'border-[var(--border)] hover:border-[var(--primary-light)] hover:bg-[var(--surface-hover)]'
                        }`}
                        onClick={() => handleToggleServico(servico)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h5 className="font-medium text-[var(--text-primary)]">
                              {servico.nome_servico}
                            </h5>
                            <p className="text-sm text-[var(--text-secondary)] mt-1">
                              {servico.descricao}
                            </p>
                            <div className="flex items-center gap-4 mt-2 text-sm text-[var(--text-secondary)]">
                              <span>{formatarDuracao(servico.duracao_minutos)}</span>
                              <span className="font-medium text-[var(--primary)]">
                                {formatarPreco(servico.preco)}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            {isServicoSelecionado(servico.servico_id) ? (
                              <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                                <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              </div>
                            ) : (
                              <div className="w-6 h-6 border-2 border-[var(--border)] rounded-full"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Visualização em lista */}
      {visualizacao === 'lista' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {servicos.map((servico) => (
            <Card
              key={servico.servico_id}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                isServicoSelecionado(servico.servico_id)
                  ? 'ring-2 ring-[var(--primary)] bg-[var(--primary-light)]'
                  : 'hover:ring-1 hover:ring-[var(--primary-light)]'
              }`}
              onClick={() => handleToggleServico(servico)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h5 className="font-medium text-[var(--text-primary)]">
                      {servico.nome_servico}
                    </h5>
                    <p className="text-sm text-[var(--text-secondary)] mt-1">
                      {servico.descricao}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-sm">
                      <span className="text-[var(--text-secondary)]">
                        {formatarDuracao(servico.duracao_minutos)}
                      </span>
                      <span className="font-medium text-[var(--primary)]">
                        {formatarPreco(servico.preco)}
                      </span>
                      <span className="text-[var(--text-secondary)]">
                        {servico.categoria}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4">
                    {isServicoSelecionado(servico.servico_id) ? (
                      <div className="w-6 h-6 bg-[var(--primary)] rounded-full flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    ) : (
                      <div className="w-6 h-6 border-2 border-[var(--border)] rounded-full"></div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
