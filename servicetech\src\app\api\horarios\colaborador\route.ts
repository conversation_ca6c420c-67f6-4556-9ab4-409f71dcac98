import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { HorarioColaborador } from '@/types/horarios';

// GET - Buscar horários de um colaborador
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const colaboradorId = searchParams.get('colaboradorId');

    if (!colaboradorId) {
      return NextResponse.json(
        { success: false, error: 'ID do colaborador é obrigatório' },
        { status: 400 }
      );
    }

    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar permissões
    const userRole = user.user_metadata?.role;
    let podeAcessar = false;

    if (userRole === 'Proprietario') {
      // Proprietário pode acessar horários de seus colaboradores
      const { data: empresa } = await supabase
        .from('empresas')
        .select('empresa_id')
        .eq('proprietario_user_id', user.id)
        .single();

      if (empresa) {
        const { data: colaborador } = await supabase
          .from('colaboradores_empresa')
          .select('colaborador_user_id')
          .eq('empresa_id', empresa.empresa_id)
          .eq('colaborador_user_id', colaboradorId)
          .eq('ativo', true)
          .single();

        podeAcessar = !!colaborador;
      }
    } else if (userRole === 'Colaborador' && user.id === colaboradorId) {
      // Colaborador pode acessar seus próprios horários
      podeAcessar = true;
    }

    if (!podeAcessar) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Buscar horários do colaborador
    const { data: colaborador, error: colaboradorError } = await supabase
      .from('colaboradores_empresa')
      .select(`
        colaborador_user_id,
        empresa_id,
        horarios_trabalho_individual,
        empresas!inner(horario_funcionamento)
      `)
      .eq('colaborador_user_id', colaboradorId)
      .eq('ativo', true)
      .single();

    if (colaboradorError) {
      console.error('Erro ao buscar colaborador:', colaboradorError);
      return NextResponse.json(
        { success: false, error: 'Colaborador não encontrado' },
        { status: 404 }
      );
    }

    // Se não tem horários individuais, herdar da empresa
    let horariosColaborador = colaborador.horarios_trabalho_individual;
    
    if (!horariosColaborador) {
      horariosColaborador = {
        colaborador_user_id: colaboradorId,
        empresa_id: colaborador.empresa_id,
        horarios: (Array.isArray(colaborador.empresas) ? colaborador.empresas[0] : colaborador.empresas)?.horario_funcionamento,
        configuracoes: {
          herdar_empresa: true,
          notificar_mudancas: true
        },
        bloqueios_recorrentes: [],
        bloqueios_especificos: []
      };
    }

    return NextResponse.json({
      success: true,
      data: horariosColaborador
    });

  } catch (error: any) {
    console.error('Erro geral na API de horários do colaborador:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// PUT - Atualizar horários de um colaborador
export async function PUT(request: Request) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { colaborador_user_id, horarios_trabalho_individual } = body;

    if (!colaborador_user_id || !horarios_trabalho_individual) {
      return NextResponse.json(
        { success: false, error: 'Dados do colaborador e horários são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar permissões
    const userRole = user.user_metadata?.role;
    let podeEditar = false;

    if (userRole === 'Proprietario') {
      // Proprietário pode editar horários de seus colaboradores
      const { data: empresa } = await supabase
        .from('empresas')
        .select('empresa_id')
        .eq('proprietario_user_id', user.id)
        .single();

      if (empresa) {
        const { data: colaborador } = await supabase
          .from('colaboradores_empresa')
          .select('colaborador_user_id')
          .eq('empresa_id', empresa.empresa_id)
          .eq('colaborador_user_id', colaborador_user_id)
          .eq('ativo', true)
          .single();

        podeEditar = !!colaborador;
      }
    } else if (userRole === 'Colaborador' && user.id === colaborador_user_id) {
      // Colaborador pode editar seus próprios horários
      podeEditar = true;
    }

    if (!podeEditar) {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    // Validar horários
    const validacao = validarHorarioColaborador(horarios_trabalho_individual);
    if (!validacao.valido) {
      return NextResponse.json(
        { success: false, error: `Horários inválidos: ${validacao.erros.join(', ')}` },
        { status: 400 }
      );
    }

    // Atualizar horários do colaborador
    const { error: updateError } = await supabase
      .from('colaboradores_empresa')
      .update({ 
        horarios_trabalho_individual: horarios_trabalho_individual,
        updated_at: new Date().toISOString()
      })
      .eq('colaborador_user_id', colaborador_user_id)
      .eq('ativo', true);

    if (updateError) {
      console.error('Erro ao atualizar horários do colaborador:', updateError);
      return NextResponse.json(
        { success: false, error: 'Erro ao atualizar horários do colaborador' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Horários do colaborador atualizados com sucesso',
      data: horarios_trabalho_individual
    });

  } catch (error: any) {
    console.error('Erro geral na atualização de horários do colaborador:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// Função para validar horários do colaborador
function validarHorarioColaborador(horarios: HorarioColaborador): { valido: boolean; erros: string[] } {
  const erros: string[] = [];

  if (!horarios.colaborador_user_id) {
    erros.push('ID do colaborador é obrigatório');
  }

  if (!horarios.empresa_id) {
    erros.push('ID da empresa é obrigatório');
  }

  if (!horarios.horarios) {
    erros.push('Horários são obrigatórios');
    return { valido: false, erros };
  }

  // Validar estrutura dos horários (similar à validação da empresa)
  const dias = ['segunda', 'terca', 'quarta', 'quinta', 'sexta', 'sabado', 'domingo'] as const;
  
  for (const dia of dias) {
    const horarioDia = horarios.horarios[dia];
    
    if (horarioDia.ativo) {
      if (!horarioDia.abertura || !horarioDia.fechamento) {
        erros.push(`${dia}: Horário de abertura e fechamento são obrigatórios`);
        continue;
      }

      const regexHorario = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!regexHorario.test(horarioDia.abertura) || !regexHorario.test(horarioDia.fechamento)) {
        erros.push(`${dia}: Formato de horário inválido (use HH:mm)`);
        continue;
      }

      const abertura = new Date(`2000-01-01T${horarioDia.abertura}:00`);
      const fechamento = new Date(`2000-01-01T${horarioDia.fechamento}:00`);

      if (abertura >= fechamento) {
        erros.push(`${dia}: Horário de fechamento deve ser posterior ao de abertura`);
      }
    }
  }

  // Validar bloqueios recorrentes
  if (horarios.bloqueios_recorrentes) {
    for (let i = 0; i < horarios.bloqueios_recorrentes.length; i++) {
      const bloqueio = horarios.bloqueios_recorrentes[i];
      
      if (!bloqueio.dia_semana || !dias.includes(bloqueio.dia_semana)) {
        erros.push(`Bloqueio recorrente ${i + 1}: Dia da semana inválido`);
      }

      if (!bloqueio.descricao || bloqueio.descricao.trim().length === 0) {
        erros.push(`Bloqueio recorrente ${i + 1}: Descrição é obrigatória`);
      }

      if (bloqueio.horario_inicio && bloqueio.horario_fim) {
        const regexHorario = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!regexHorario.test(bloqueio.horario_inicio) || !regexHorario.test(bloqueio.horario_fim)) {
          erros.push(`Bloqueio recorrente ${i + 1}: Formato de horário inválido`);
        }
      }
    }
  }

  // Validar bloqueios específicos
  if (horarios.bloqueios_especificos) {
    for (let i = 0; i < horarios.bloqueios_especificos.length; i++) {
      const bloqueio = horarios.bloqueios_especificos[i];
      
      if (!bloqueio.data || !/^\d{4}-\d{2}-\d{2}$/.test(bloqueio.data)) {
        erros.push(`Bloqueio específico ${i + 1}: Data inválida (use YYYY-MM-DD)`);
      }

      if (!bloqueio.descricao || bloqueio.descricao.trim().length === 0) {
        erros.push(`Bloqueio específico ${i + 1}: Descrição é obrigatória`);
      }

      if (!bloqueio.tipo || !['folga', 'compromisso', 'manutencao', 'outro'].includes(bloqueio.tipo)) {
        erros.push(`Bloqueio específico ${i + 1}: Tipo inválido`);
      }
    }
  }

  return {
    valido: erros.length === 0,
    erros
  };
}
