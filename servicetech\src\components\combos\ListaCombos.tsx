'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { ComboCompleto, FiltrosCombos, EstatisticasCombos } from '@/types/combos';
import { formatarDesconto, verificarStatusCombo, calcularDuracaoTotal } from '@/utils/combos';

interface ListaCombosProps {
  combos: ComboCompleto[];
  estatisticas?: EstatisticasCombos;
  loading?: boolean;
  onEditar: (combo: ComboCompleto) => void;
  onExcluir: (comboId: number) => void;
  onAlternarStatus: (comboId: number, ativo: boolean) => void;
  onFiltrar: (filtros: FiltrosCombos) => void;
}

export function ListaCombos({
  combos,
  estatisticas,
  loading = false,
  onEditar,
  onExcluir,
  onAlternarStatus,
  onFiltrar
}: ListaCombosProps) {
  const [filtros, setFiltros] = useState<FiltrosCombos>({});
  const [comboParaExcluir, setComboParaExcluir] = useState<ComboCompleto | null>(null);

  // Aplicar filtros
  const aplicarFiltros = () => {
    onFiltrar(filtros);
  };

  // Limpar filtros
  const limparFiltros = () => {
    const filtrosVazios: FiltrosCombos = {};
    setFiltros(filtrosVazios);
    onFiltrar(filtrosVazios);
  };

  // Confirmar exclusão
  const confirmarExclusao = (combo: ComboCompleto) => {
    setComboParaExcluir(combo);
  };

  // Executar exclusão
  const executarExclusao = () => {
    if (comboParaExcluir) {
      onExcluir(comboParaExcluir.combo_id);
      setComboParaExcluir(null);
    }
  };

  // Cancelar exclusão
  const cancelarExclusao = () => {
    setComboParaExcluir(null);
  };

  // Obter cor do status
  const obterCorStatus = (combo: ComboCompleto) => {
    const status = verificarStatusCombo(combo);
    switch (status) {
      case 'ativo':
        return 'text-green-600 bg-green-100';
      case 'inativo':
        return 'text-gray-600 bg-gray-100';
      case 'expirado':
        return 'text-orange-600 bg-orange-100';
      case 'esgotado':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Obter texto do status
  const obterTextoStatus = (combo: ComboCompleto) => {
    const status = verificarStatusCombo(combo);
    switch (status) {
      case 'ativo':
        return 'Ativo';
      case 'inativo':
        return 'Inativo';
      case 'expirado':
        return 'Expirado';
      case 'esgotado':
        return 'Esgotado';
      default:
        return 'Inativo';
    }
  };

  return (
    <div className="space-y-6">
      {/* Estatísticas */}
      {estatisticas && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-[var(--primary)]">
                {estatisticas.total}
              </div>
              <div className="text-sm text-[var(--text-secondary)]">
                Total de Combos
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">
                {estatisticas.ativos}
              </div>
              <div className="text-sm text-[var(--text-secondary)]">
                Combos Ativos
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-gray-600">
                {estatisticas.inativos}
              </div>
              <div className="text-sm text-[var(--text-secondary)]">
                Combos Inativos
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">
                {estatisticas.total_usos}
              </div>
              <div className="text-sm text-[var(--text-secondary)]">
                Total de Usos
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">
                R$ {estatisticas.economia_total.toFixed(2)}
              </div>
              <div className="text-sm text-[var(--text-secondary)]">
                Economia Total
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle>Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Buscar
              </label>
              <Input
                type="text"
                value={filtros.busca || ''}
                onChange={(e) => setFiltros({ ...filtros, busca: e.target.value })}
                placeholder="Nome ou descrição..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Status
              </label>
              <select
                value={filtros.ativo?.toString() || ''}
                onChange={(e) => setFiltros({ 
                  ...filtros, 
                  ativo: e.target.value === '' ? undefined : e.target.value === 'true' 
                })}
                className="w-full px-3 py-2 border border-[var(--border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary)]"
              >
                <option value="">Todos</option>
                <option value="true">Ativos</option>
                <option value="false">Inativos</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Data Início
              </label>
              <Input
                type="date"
                value={filtros.data_inicio || ''}
                onChange={(e) => setFiltros({ ...filtros, data_inicio: e.target.value })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-primary)] mb-2">
                Data Fim
              </label>
              <Input
                type="date"
                value={filtros.data_fim || ''}
                onChange={(e) => setFiltros({ ...filtros, data_fim: e.target.value })}
              />
            </div>
          </div>

          <div className="flex gap-4 mt-4">
            <Button onClick={aplicarFiltros} disabled={loading}>
              Aplicar Filtros
            </Button>
            <Button variant="outline" onClick={limparFiltros} disabled={loading}>
              Limpar Filtros
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Lista de combos */}
      <div className="space-y-4">
        {loading ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-[var(--text-secondary)]">Carregando combos...</div>
            </CardContent>
          </Card>
        ) : combos.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-[var(--text-secondary)]">
                Nenhum combo encontrado.
              </div>
            </CardContent>
          </Card>
        ) : (
          combos.map((combo) => (
            <Card key={combo.combo_id} className="overflow-hidden">
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-[var(--text-primary)]">
                        {combo.nome_combo}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${obterCorStatus(combo)}`}>
                        {obterTextoStatus(combo)}
                      </span>
                    </div>
                    <p className="text-[var(--text-secondary)] mb-3">
                      {combo.descricao}
                    </p>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditar(combo)}
                    >
                      Editar
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onAlternarStatus(combo.combo_id, !combo.ativo)}
                      className={combo.ativo ? 'text-orange-600' : 'text-green-600'}
                    >
                      {combo.ativo ? 'Desativar' : 'Ativar'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => confirmarExclusao(combo)}
                      className="text-red-600 hover:text-red-700"
                    >
                      Excluir
                    </Button>
                  </div>
                </div>

                {/* Informações do combo */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-[var(--text-secondary)]">Desconto</div>
                    <div className="font-medium text-[var(--text-primary)]">
                      {formatarDesconto(combo)}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-[var(--text-secondary)]">Valor Original</div>
                    <div className="font-medium text-[var(--text-primary)]">
                      R$ {combo.valor_original.toFixed(2)}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-[var(--text-secondary)]">Valor com Desconto</div>
                    <div className="font-medium text-green-600">
                      R$ {combo.valor_com_desconto.toFixed(2)}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm text-[var(--text-secondary)]">Economia</div>
                    <div className="font-medium text-purple-600">
                      R$ {combo.economia.toFixed(2)}
                    </div>
                  </div>
                </div>

                {/* Serviços do combo */}
                <div className="mb-4">
                  <div className="text-sm font-medium text-[var(--text-primary)] mb-2">
                    Serviços ({combo.itens.length})
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {combo.itens.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-[var(--surface)] rounded">
                        <div>
                          <span className="font-medium">{item.servico.nome_servico}</span>
                          {item.quantidade > 1 && (
                            <span className="text-sm text-[var(--text-secondary)]"> x{item.quantidade}</span>
                          )}
                          {item.obrigatorio && (
                            <span className="text-xs text-orange-600 ml-2">(Obrigatório)</span>
                          )}
                        </div>
                        <div className="text-sm text-[var(--text-secondary)]">
                          R$ {(item.servico.preco * item.quantidade).toFixed(2)}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Informações adicionais */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-[var(--text-secondary)]">
                  <div>
                    <span className="font-medium">Duração Total:</span> {calcularDuracaoTotal(combo)} min
                  </div>
                  <div>
                    <span className="font-medium">Usos:</span> {combo.usos_realizados}
                    {combo.limite_usos && ` / ${combo.limite_usos}`}
                  </div>
                  <div>
                    <span className="font-medium">Criado em:</span> {new Date(combo.created_at).toLocaleDateString('pt-BR')}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Modal de confirmação de exclusão */}
      {comboParaExcluir && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>Confirmar Exclusão</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-[var(--text-secondary)] mb-6">
                Tem certeza que deseja excluir o combo "{comboParaExcluir.nome_combo}"?
                Esta ação não pode ser desfeita.
              </p>
              <div className="flex gap-4">
                <Button
                  variant="outline"
                  onClick={cancelarExclusao}
                  className="flex-1"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={executarExclusao}
                  className="flex-1 bg-red-600 hover:bg-red-700 text-white"
                >
                  Excluir
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
