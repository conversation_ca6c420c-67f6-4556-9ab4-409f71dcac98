/**
 * Instrumentação do Next.js para Sentry
 * Este arquivo é executado quando o servidor inicia
 */

export async function register() {
  // Temporariamente desabilitado para resolver problema do OpenTelemetry
  console.log('🔧 Instrumentação desabilitada temporariamente');

  // TODO: Reabilitar após resolver problema do OpenTelemetry
  /*
  // Registrar instrumentação do Sentry apenas no servidor
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    await import('./sentry.server.config');
  }

  // Registrar instrumentação para Edge Runtime
  if (process.env.NEXT_RUNTIME === 'edge') {
    await import('./sentry.edge.config');
  }
  */
}
