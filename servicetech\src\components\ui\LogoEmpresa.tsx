'use client';

import React, { useState } from 'react';
import { obterImagemEmpresa, obterAltImagemEmpresa } from '@/utils/imagensGenericas';

interface LogoEmpresaProps {
  nomeEmpresa: string;
  logoUrl?: string | null;
  segmento?: string | null;
  className?: string;
  width?: number;
  height?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  showFallback?: boolean;
}

/**
 * Componente para exibir logo de empresa com fallback para imagens genéricas
 */
export function LogoEmpresa({
  nomeEmpresa,
  logoUrl,
  segmento,
  className = '',
  width,
  height,
  objectFit = 'cover',
  showFallback = true
}: LogoEmpresaProps) {
  const [imagemError, setImagemError] = useState(false);
  const [carregando, setCarregando] = useState(true);

  // Determina qual imagem usar
  const imagemSrc = obterImagemEmpresa(logoUrl, segmento);
  const altText = obterAltImagemEmpresa(nomeEmpresa, logoUrl, segmento);

  // Se houve erro na imagem e não deve mostrar fallback, mostra ícone
  if (imagemError && !showFallback) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-200 ${className}`}
        style={{ width, height }}
      >
        <svg 
          className="w-1/2 h-1/2 text-gray-400" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h4m6 0h2M7 15h10" 
          />
        </svg>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {/* Indicador de carregamento */}
      {carregando && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-gray-100 animate-pulse"
          style={{ width, height }}
        >
          <div className="w-6 h-6 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
        </div>
      )}

      {/* Imagem */}
      <img
        src={imagemSrc}
        alt={altText}
        className={`${carregando ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        style={{ 
          width: '100%', 
          height: '100%', 
          objectFit 
        }}
        onLoad={() => setCarregando(false)}
        onError={() => {
          setCarregando(false);
          setImagemError(true);
        }}
      />
    </div>
  );
}

/**
 * Variante específica para cards de resultados de busca
 */
export function LogoEmpresaCard({
  nomeEmpresa,
  logoUrl,
  segmento,
  className = ''
}: Omit<LogoEmpresaProps, 'width' | 'height' | 'objectFit'>) {
  return (
    <LogoEmpresa
      nomeEmpresa={nomeEmpresa}
      logoUrl={logoUrl}
      segmento={segmento}
      className={`w-full h-full object-cover ${className}`}
      objectFit="cover"
    />
  );
}

/**
 * Variante específica para header de estabelecimento
 */
export function LogoEmpresaHeader({
  nomeEmpresa,
  logoUrl,
  segmento,
  className = ''
}: Omit<LogoEmpresaProps, 'width' | 'height' | 'objectFit'>) {
  return (
    <LogoEmpresa
      nomeEmpresa={nomeEmpresa}
      logoUrl={logoUrl}
      segmento={segmento}
      className={`w-24 h-24 rounded-lg ${className}`}
      objectFit="cover"
    />
  );
}
