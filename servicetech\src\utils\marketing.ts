import { Cupom, CupomCompleto, ResultadoAplicacaoCupom, SegmentacaoCampanha } from '@/types/marketing';

// ===== VALIDAÇÕES DE CUPONS =====

/**
 * Valida se um cupom está válido para uso
 */
export function validarCupom(cupom: Cupom): { valido: boolean; motivo?: string } {
  const agora = new Date();
  const dataInicio = new Date(cupom.data_inicio);
  const dataFim = new Date(cupom.data_fim);

  // Verificar se está ativo
  if (!cupom.ativo) {
    return { valido: false, motivo: 'Cupom inativo' };
  }

  // Verificar período de validade
  if (agora < dataInicio) {
    return { valido: false, motivo: 'Cupom ainda não está válido' };
  }

  if (agora > dataFim) {
    return { valido: false, motivo: 'Cupom expirado' };
  }

  // Verificar limite de usos
  if (cupom.limite_usos_total && cupom.usos_realizados >= cupom.limite_usos_total) {
    return { valido: false, motivo: 'Cupom esgotado' };
  }

  return { valido: true };
}

/**
 * Calcula o desconto de um cupom
 */
export function calcularDesconto(
  cupom: Cupom, 
  valorTotal: number
): { valorDesconto: number; valorFinal: number; economia: number } {
  let valorDesconto = 0;

  if (cupom.tipo_desconto === 'valor_fixo') {
    valorDesconto = Math.min(cupom.valor_desconto, valorTotal);
  } else if (cupom.tipo_desconto === 'percentual') {
    valorDesconto = (valorTotal * cupom.valor_desconto) / 100;
  }

  const valorFinal = Math.max(0, valorTotal - valorDesconto);
  const economia = valorTotal - valorFinal;

  return {
    valorDesconto,
    valorFinal,
    economia
  };
}

/**
 * Verifica se um cupom pode ser aplicado a determinados serviços
 */
export function cupomAplicavelServicos(cupom: Cupom, servicosIds: number[]): boolean {
  // Se não há restrição de serviços, aplica a todos
  if (!cupom.aplicavel_servicos || cupom.aplicavel_servicos.length === 0) {
    return true;
  }

  // Verifica se pelo menos um serviço está na lista de aplicáveis
  return servicosIds.some(servicoId => cupom.aplicavel_servicos!.includes(servicoId));
}

/**
 * Cria um cupom completo com informações calculadas
 */
export function criarCupomCompleto(cupom: Cupom): CupomCompleto {
  const validacao = validarCupom(cupom);
  
  return {
    ...cupom,
    valido: validacao.valido,
    motivo_invalido: validacao.motivo,
    pode_usar: validacao.valido,
    usos_restantes: cupom.limite_usos_total 
      ? Math.max(0, cupom.limite_usos_total - cupom.usos_realizados)
      : undefined
  };
}

// ===== FORMATAÇÃO =====

/**
 * Formata o valor do desconto para exibição
 */
export function formatarDesconto(cupom: Cupom): string {
  if (cupom.tipo_desconto === 'valor_fixo') {
    return `R$ ${cupom.valor_desconto.toFixed(2)}`;
  } else {
    return `${cupom.valor_desconto}%`;
  }
}

/**
 * Formata o período de validade do cupom
 */
export function formatarPeriodoValidade(cupom: Cupom): string {
  const dataInicio = new Date(cupom.data_inicio).toLocaleDateString('pt-BR');
  const dataFim = new Date(cupom.data_fim).toLocaleDateString('pt-BR');
  return `${dataInicio} - ${dataFim}`;
}

/**
 * Formata o status do cupom
 */
export function formatarStatusCupom(cupom: Cupom): string {
  const agora = new Date();
  const dataFim = new Date(cupom.data_fim);
  
  if (!cupom.ativo) return 'Inativo';
  if (agora > dataFim) return 'Expirado';
  if (cupom.limite_usos_total && cupom.usos_realizados >= cupom.limite_usos_total) return 'Esgotado';
  return 'Ativo';
}

// ===== GERAÇÃO DE CÓDIGOS =====

/**
 * Gera um código de cupom aleatório
 */
export function gerarCodigoCupom(prefixo?: string, tamanho: number = 8): string {
  const caracteres = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let codigo = prefixo || '';
  
  const tamanhoRestante = tamanho - codigo.length;
  for (let i = 0; i < tamanhoRestante; i++) {
    codigo += caracteres.charAt(Math.floor(Math.random() * caracteres.length));
  }
  
  return codigo;
}

/**
 * Gera sugestões de códigos baseadas no nome do cupom
 */
export function gerarSugestoesCodigos(nomeCupom: string): string[] {
  const palavras = nomeCupom.toUpperCase()
    .replace(/[^A-Z0-9\s]/g, '')
    .split(' ')
    .filter(palavra => palavra.length > 0);

  const sugestoes: string[] = [];

  // Primeira letra de cada palavra + número
  if (palavras.length > 1) {
    const iniciais = palavras.map(p => p[0]).join('');
    sugestoes.push(`${iniciais}10`);
    sugestoes.push(`${iniciais}20`);
  }

  // Primeira palavra + número
  if (palavras[0]) {
    const primeira = palavras[0].substring(0, 6);
    sugestoes.push(`${primeira}10`);
    sugestoes.push(`${primeira}OFF`);
  }

  // Códigos genéricos
  sugestoes.push(gerarCodigoCupom('DESC', 8));
  sugestoes.push(gerarCodigoCupom('PROMO', 8));

  return sugestoes.slice(0, 4);
}

// ===== SEGMENTAÇÃO DE CAMPANHAS =====

/**
 * Valida critérios de segmentação
 */
export function validarSegmentacao(segmentacao: SegmentacaoCampanha): { valido: boolean; erros: string[] } {
  const erros: string[] = [];

  if (segmentacao.min_agendamentos !== undefined && segmentacao.min_agendamentos < 0) {
    erros.push('Mínimo de agendamentos não pode ser negativo');
  }

  if (segmentacao.max_agendamentos !== undefined && segmentacao.max_agendamentos < 0) {
    erros.push('Máximo de agendamentos não pode ser negativo');
  }

  if (
    segmentacao.min_agendamentos !== undefined && 
    segmentacao.max_agendamentos !== undefined && 
    segmentacao.min_agendamentos > segmentacao.max_agendamentos
  ) {
    erros.push('Mínimo de agendamentos não pode ser maior que o máximo');
  }

  if (segmentacao.valor_gasto_min !== undefined && segmentacao.valor_gasto_min < 0) {
    erros.push('Valor mínimo gasto não pode ser negativo');
  }

  if (segmentacao.valor_gasto_max !== undefined && segmentacao.valor_gasto_max < 0) {
    erros.push('Valor máximo gasto não pode ser negativo');
  }

  if (
    segmentacao.valor_gasto_min !== undefined && 
    segmentacao.valor_gasto_max !== undefined && 
    segmentacao.valor_gasto_min > segmentacao.valor_gasto_max
  ) {
    erros.push('Valor mínimo gasto não pode ser maior que o máximo');
  }

  if (segmentacao.clientes_inativos_dias !== undefined && segmentacao.clientes_inativos_dias < 1) {
    erros.push('Dias de inatividade deve ser pelo menos 1');
  }

  if (segmentacao.periodo_analise_dias !== undefined && segmentacao.periodo_analise_dias < 1) {
    erros.push('Período de análise deve ser pelo menos 1 dia');
  }

  return {
    valido: erros.length === 0,
    erros
  };
}

/**
 * Cria uma descrição textual dos critérios de segmentação
 */
export function descreverSegmentacao(segmentacao: SegmentacaoCampanha): string {
  const criterios: string[] = [];

  if (segmentacao.clientes_ativos !== undefined) {
    if (segmentacao.clientes_ativos) {
      const dias = segmentacao.clientes_inativos_dias || 30;
      criterios.push(`clientes ativos (últimos ${dias} dias)`);
    } else {
      const dias = segmentacao.clientes_inativos_dias || 30;
      criterios.push(`clientes inativos (mais de ${dias} dias)`);
    }
  }

  if (segmentacao.min_agendamentos !== undefined) {
    criterios.push(`mínimo ${segmentacao.min_agendamentos} agendamentos`);
  }

  if (segmentacao.max_agendamentos !== undefined) {
    criterios.push(`máximo ${segmentacao.max_agendamentos} agendamentos`);
  }

  if (segmentacao.valor_gasto_min !== undefined) {
    criterios.push(`valor mínimo gasto R$ ${segmentacao.valor_gasto_min.toFixed(2)}`);
  }

  if (segmentacao.valor_gasto_max !== undefined) {
    criterios.push(`valor máximo gasto R$ ${segmentacao.valor_gasto_max.toFixed(2)}`);
  }

  if (segmentacao.servicos_utilizados && segmentacao.servicos_utilizados.length > 0) {
    criterios.push(`utilizaram serviços específicos (${segmentacao.servicos_utilizados.length} serviços)`);
  }

  if (criterios.length === 0) {
    return 'Todos os clientes';
  }

  return `Clientes com: ${criterios.join(', ')}`;
}

// ===== ESTATÍSTICAS =====

/**
 * Calcula estatísticas de uso de cupons
 */
export function calcularEstatisticasCupons(cupons: Cupom[]) {
  const total = cupons.length;
  const ativos = cupons.filter(c => formatarStatusCupom(c) === 'Ativo').length;
  const expirados = cupons.filter(c => formatarStatusCupom(c) === 'Expirado').length;
  const esgotados = cupons.filter(c => formatarStatusCupom(c) === 'Esgotado').length;
  const totalUsos = cupons.reduce((sum, c) => sum + c.usos_realizados, 0);

  return {
    total,
    ativos,
    expirados,
    esgotados,
    totalUsos,
    taxaUso: total > 0 ? (totalUsos / total) : 0
  };
}

/**
 * Calcula ROI estimado de campanhas de marketing
 */
export function calcularROICampanha(
  custoEnvio: number,
  agendamentosGerados: number,
  ticketMedio: number
): { roi: number; receitaGerada: number; lucro: number } {
  const receitaGerada = agendamentosGerados * ticketMedio;
  const lucro = receitaGerada - custoEnvio;
  const roi = custoEnvio > 0 ? (lucro / custoEnvio) * 100 : 0;

  return {
    roi,
    receitaGerada,
    lucro
  };
}
