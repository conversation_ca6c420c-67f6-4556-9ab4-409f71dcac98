/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase";
exports.ids = ["vendor-chunks/@supabase"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/cookies.js":
/*!***********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/cookies.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyServerStorage: () => (/* binding */ applyServerStorage),\n/* harmony export */   createStorageFromOptions: () => (/* binding */ createStorageFromOptions)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/cookie/dist/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\nconst BASE64_PREFIX = \"base64-\";\n/**\n * Creates a storage client that handles cookies correctly for browser and\n * server clients with or without properly provided cookie methods.\n *\n * @param options The options passed to createBrowserClient or createServer client.\n *\n * @param isServerClient Whether it's called from createServerClient.\n */\nfunction createStorageFromOptions(options, isServerClient) {\n    const cookies = options.cookies ?? null;\n    const cookieEncoding = options.cookieEncoding;\n    const setItems = {};\n    const removedItems = {};\n    let getAll;\n    let setAll;\n    if (cookies) {\n        if (\"get\" in cookies) {\n            // Just get is not enough, because the client needs to see what cookies\n            // are already set and unset them if necessary. To attempt to fix this\n            // behavior for most use cases, we pass \"hints\" which is the keys of the\n            // storage items. They are then converted to their corresponding cookie\n            // chunk names and are fetched with get. Only 5 chunks are fetched, which\n            // should be enough for the majority of use cases, but does not solve\n            // those with very large sessions.\n            const getWithHints = async (keyHints) => {\n                // optimistically find the first 5 potential chunks for the specified key\n                const chunkNames = keyHints.flatMap((keyHint) => [\n                    keyHint,\n                    ...Array.from({ length: 5 }).map((_, i) => `${keyHint}.${i}`),\n                ]);\n                const chunks = [];\n                for (let i = 0; i < chunkNames.length; i += 1) {\n                    const value = await cookies.get(chunkNames[i]);\n                    if (!value && typeof value !== \"string\") {\n                        continue;\n                    }\n                    chunks.push({ name: chunkNames[i], value });\n                }\n                // TODO: detect and log stale chunks error\n                return chunks;\n            };\n            getAll = async (keyHints) => await getWithHints(keyHints);\n            if (\"set\" in cookies && \"remove\" in cookies) {\n                setAll = async (setCookies) => {\n                    for (let i = 0; i < setCookies.length; i += 1) {\n                        const { name, value, options } = setCookies[i];\n                        if (value) {\n                            await cookies.set(name, value, options);\n                        }\n                        else {\n                            await cookies.remove(name, options);\n                        }\n                    }\n                };\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else if (\"getAll\" in cookies) {\n            getAll = async () => await cookies.getAll();\n            if (\"setAll\" in cookies) {\n                setAll = cookies.setAll;\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else {\n            // neither get nor getAll is present on cookies, only will occur if pure JavaScript is used, but cookies is an object\n            throw new Error(`@supabase/ssr: ${isServerClient ? \"createServerClient\" : \"createBrowserClient\"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)() ? \" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.\" : \"\"}`);\n        }\n    }\n    else if (!isServerClient && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)()) {\n        // The environment is browser, so use the document.cookie API to implement getAll and setAll.\n        const noHintGetAll = () => {\n            const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n            return Object.keys(parsed).map((name) => ({\n                name,\n                value: parsed[name] ?? \"\",\n            }));\n        };\n        getAll = () => noHintGetAll();\n        setAll = (setCookies) => {\n            setCookies.forEach(({ name, value, options }) => {\n                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n            });\n        };\n    }\n    else if (isServerClient) {\n        throw new Error(\"@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)\");\n    }\n    else {\n        // getting cookies when there's no window but we're in browser mode can be OK, because the developer probably is not using auth functions\n        getAll = () => {\n            return [];\n        };\n        // this is NOT OK because the developer is using auth functions that require setting some state, so that must error out\n        setAll = () => {\n            throw new Error(\"@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed\");\n        };\n    }\n    if (!isServerClient) {\n        // This is the storage client to be used in browsers. It only\n        // works on the cookies abstraction, unlike the server client\n        // which only uses cookies to read the initial state. When an\n        // item is set, cookies are both cleared and set to values so\n        // that stale chunks are not left remaining.\n        return {\n            getAll, // for type consistency\n            setAll, // for type consistency\n            setItems, // for type consistency\n            removedItems, // for type consistency\n            storage: {\n                isServer: false,\n                getItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                        const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                        if (!cookie) {\n                            return null;\n                        }\n                        return cookie.value;\n                    });\n                    if (!chunkedCookie) {\n                        return null;\n                    }\n                    let decoded = chunkedCookie;\n                    if (chunkedCookie.startsWith(BASE64_PREFIX)) {\n                        decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                    }\n                    return decoded;\n                },\n                setItem: async (key, value) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key)));\n                    let encoded = value;\n                    if (cookieEncoding === \"base64url\") {\n                        encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(value);\n                    }\n                    const setCookies = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(key, encoded);\n                    setCookies.forEach(({ name }) => {\n                        removeCookies.delete(name);\n                    });\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    const setCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    delete setCookieOptions.name;\n                    const allToSet = [\n                        ...[...removeCookies].map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })),\n                        ...setCookies.map(({ name, value }) => ({\n                            name,\n                            value,\n                            options: setCookieOptions,\n                        })),\n                    ];\n                    if (allToSet.length > 0) {\n                        await setAll(allToSet);\n                    }\n                },\n                removeItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key));\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    if (removeCookies.length > 0) {\n                        await setAll(removeCookies.map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })));\n                    }\n                },\n            },\n        };\n    }\n    // This is the server client. It only uses getAll to read the initial\n    // state. Any subsequent changes to the items is persisted in the\n    // setItems and removedItems objects. createServerClient *must* use\n    // getAll, setAll and the values in setItems and removedItems to\n    // persist the changes *at once* when appropriate (usually only when\n    // the TOKEN_REFRESHED, USER_UPDATED or SIGNED_OUT events are fired by\n    // the Supabase Auth client).\n    return {\n        getAll,\n        setAll,\n        setItems,\n        removedItems,\n        storage: {\n            // to signal to the libraries that these cookies are\n            // coming from a server environment and their value\n            // should not be trusted\n            isServer: true,\n            getItem: async (key) => {\n                if (typeof setItems[key] === \"string\") {\n                    return setItems[key];\n                }\n                if (removedItems[key]) {\n                    return null;\n                }\n                const allCookies = await getAll([key]);\n                const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                    const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                    if (!cookie) {\n                        return null;\n                    }\n                    return cookie.value;\n                });\n                if (!chunkedCookie) {\n                    return null;\n                }\n                let decoded = chunkedCookie;\n                if (typeof chunkedCookie === \"string\" &&\n                    chunkedCookie.startsWith(BASE64_PREFIX)) {\n                    decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                }\n                return decoded;\n            },\n            setItem: async (key, value) => {\n                // We don't have an `onAuthStateChange` event that can let us know that\n                // the PKCE code verifier is being set. Therefore, if we see it being\n                // set, we need to apply the storage (call `setAll` so the cookie is\n                // set properly).\n                if (key.endsWith(\"-code-verifier\")) {\n                    await applyServerStorage({\n                        getAll,\n                        setAll,\n                        // pretend only that the code verifier was set\n                        setItems: { [key]: value },\n                        // pretend that nothing was removed\n                        removedItems: {},\n                    }, {\n                        cookieOptions: options?.cookieOptions ?? null,\n                        cookieEncoding,\n                    });\n                }\n                setItems[key] = value;\n                delete removedItems[key];\n            },\n            removeItem: async (key) => {\n                // Intentionally not applying the storage when the key is the PKCE code\n                // verifier, as usually right after it's removed other items are set,\n                // so application of the storage will be handled by the\n                // `onAuthStateChange` callback that follows removal -- usually as part\n                // of the `exchangeCodeForSession` call.\n                delete setItems[key];\n                removedItems[key] = true;\n            },\n        },\n    };\n}\n/**\n * When createServerClient needs to apply the created storage to cookies, it\n * should call this function which handles correcly setting cookies for stored\n * and removed items in the storage.\n */\nasync function applyServerStorage({ getAll, setAll, setItems, removedItems, }, options) {\n    const cookieEncoding = options.cookieEncoding;\n    const cookieOptions = options.cookieOptions ?? null;\n    const allCookies = await getAll([\n        ...(setItems ? Object.keys(setItems) : []),\n        ...(removedItems ? Object.keys(removedItems) : []),\n    ]);\n    const cookieNames = allCookies?.map(({ name }) => name) || [];\n    const removeCookies = Object.keys(removedItems).flatMap((itemName) => {\n        return cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName));\n    });\n    const setCookies = Object.keys(setItems).flatMap((itemName) => {\n        const removeExistingCookiesForItem = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName)));\n        let encoded = setItems[itemName];\n        if (cookieEncoding === \"base64url\") {\n            encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(encoded);\n        }\n        const chunks = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(itemName, encoded);\n        chunks.forEach((chunk) => {\n            removeExistingCookiesForItem.delete(chunk.name);\n        });\n        removeCookies.push(...removeExistingCookiesForItem);\n        return chunks;\n    });\n    const removeCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: 0,\n    };\n    const setCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n    };\n    // the NextJS cookieStore API can get confused if the `name` from\n    // options.cookieOptions leaks\n    delete removeCookieOptions.name;\n    delete setCookieOptions.name;\n    await setAll([\n        ...removeCookies.map((name) => ({\n            name,\n            value: \"\",\n            options: removeCookieOptions,\n        })),\n        ...setCookies.map(({ name, value }) => ({\n            name,\n            value,\n            options: setCookieOptions,\n        })),\n    ]);\n}\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/cookies.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/createBrowserClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./version */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/index.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cookies */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\n\nlet cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n    // singleton client is created only if isSingleton is set to true, or if isSingleton is not defined and we detect a browser\n    const shouldUseSingleton = options?.isSingleton === true ||\n        ((!options || !(\"isSingleton\" in options)) && (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isBrowser)());\n    if (shouldUseSingleton && cachedBrowserClient) {\n        return cachedBrowserClient;\n    }\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage } = (0,_cookies__WEBPACK_IMPORTED_MODULE_3__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, false);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_1__.VERSION} createBrowserClient`,\n            },\n        },\n        auth: {\n            ...options?.auth,\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            flowType: \"pkce\",\n            autoRefreshToken: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isBrowser)(),\n            detectSessionInUrl: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isBrowser)(),\n            persistSession: true,\n            storage,\n        },\n    });\n    if (shouldUseSingleton) {\n        cachedBrowserClient = client;\n    }\n    return client;\n}\n//# sourceMappingURL=createBrowserClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/createServerClient.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/createServerClient.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./version */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cookies */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage, getAll, setAll, setItems, removedItems } = (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, true);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_1__.VERSION} createServerClient`,\n            },\n        },\n        auth: {\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            ...options?.auth,\n            flowType: \"pkce\",\n            autoRefreshToken: false,\n            detectSessionInUrl: false,\n            persistSession: true,\n            storage,\n        },\n    });\n    client.auth.onAuthStateChange(async (event) => {\n        // The SIGNED_IN event is fired very often, but we don't need to\n        // apply the storage each time it fires, only if there are changes\n        // that need to be set -- which is if setItems / removeItems have\n        // data.\n        const hasStorageChanges = Object.keys(setItems).length > 0 || Object.keys(removedItems).length > 0;\n        if (hasStorageChanges &&\n            (event === \"SIGNED_IN\" ||\n                event === \"TOKEN_REFRESHED\" ||\n                event === \"USER_UPDATED\" ||\n                event === \"PASSWORD_RECOVERY\" ||\n                event === \"SIGNED_OUT\" ||\n                event === \"MFA_CHALLENGE_VERIFIED\")) {\n            await (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.applyServerStorage)({ getAll, setAll, setItems, removedItems }, {\n                cookieOptions: options?.cookieOptions ?? null,\n                cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n            });\n        }\n    });\n    return client;\n}\n//# sourceMappingURL=createServerClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS9jcmVhdGVTZXJ2ZXJDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0Q7QUFDbEI7QUFDcUM7QUFDbEU7QUFDUDtBQUNBO0FBQ0E7QUFDQSxZQUFZLGtEQUFrRCxFQUFFLGtFQUF3QjtBQUN4RjtBQUNBO0FBQ0EsS0FBSztBQUNMLG1CQUFtQixtRUFBWTtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELDZDQUFPLEVBQUU7QUFDMUQsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiw0REFBa0IsR0FBRyx3Q0FBd0M7QUFDL0U7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXGNyZWF0ZVNlcnZlckNsaWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQsIH0gZnJvbSBcIkBzdXBhYmFzZS9zdXBhYmFzZS1qc1wiO1xuaW1wb3J0IHsgVkVSU0lPTiB9IGZyb20gXCIuL3ZlcnNpb25cIjtcbmltcG9ydCB7IGNyZWF0ZVN0b3JhZ2VGcm9tT3B0aW9ucywgYXBwbHlTZXJ2ZXJTdG9yYWdlIH0gZnJvbSBcIi4vY29va2llc1wiO1xuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVNlcnZlckNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIG9wdGlvbnMpIHtcbiAgICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUtleSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFlvdXIgcHJvamVjdCdzIFVSTCBhbmQgS2V5IGFyZSByZXF1aXJlZCB0byBjcmVhdGUgYSBTdXBhYmFzZSBjbGllbnQhXFxuXFxuQ2hlY2sgeW91ciBTdXBhYmFzZSBwcm9qZWN0J3MgQVBJIHNldHRpbmdzIHRvIGZpbmQgdGhlc2UgdmFsdWVzXFxuXFxuaHR0cHM6Ly9zdXBhYmFzZS5jb20vZGFzaGJvYXJkL3Byb2plY3QvXy9zZXR0aW5ncy9hcGlgKTtcbiAgICB9XG4gICAgY29uc3QgeyBzdG9yYWdlLCBnZXRBbGwsIHNldEFsbCwgc2V0SXRlbXMsIHJlbW92ZWRJdGVtcyB9ID0gY3JlYXRlU3RvcmFnZUZyb21PcHRpb25zKHtcbiAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgY29va2llRW5jb2Rpbmc6IG9wdGlvbnM/LmNvb2tpZUVuY29kaW5nID8/IFwiYmFzZTY0dXJsXCIsXG4gICAgfSwgdHJ1ZSk7XG4gICAgY29uc3QgY2xpZW50ID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwge1xuICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICBnbG9iYWw6IHtcbiAgICAgICAgICAgIC4uLm9wdGlvbnM/Lmdsb2JhbCxcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICAuLi5vcHRpb25zPy5nbG9iYWw/LmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGBzdXBhYmFzZS1zc3IvJHtWRVJTSU9OfSBjcmVhdGVTZXJ2ZXJDbGllbnRgLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgYXV0aDoge1xuICAgICAgICAgICAgLi4uKG9wdGlvbnM/LmNvb2tpZU9wdGlvbnM/Lm5hbWVcbiAgICAgICAgICAgICAgICA/IHsgc3RvcmFnZUtleTogb3B0aW9ucy5jb29raWVPcHRpb25zLm5hbWUgfVxuICAgICAgICAgICAgICAgIDogbnVsbCksXG4gICAgICAgICAgICAuLi5vcHRpb25zPy5hdXRoLFxuICAgICAgICAgICAgZmxvd1R5cGU6IFwicGtjZVwiLFxuICAgICAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgICAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IGZhbHNlLFxuICAgICAgICAgICAgcGVyc2lzdFNlc3Npb246IHRydWUsXG4gICAgICAgICAgICBzdG9yYWdlLFxuICAgICAgICB9LFxuICAgIH0pO1xuICAgIGNsaWVudC5hdXRoLm9uQXV0aFN0YXRlQ2hhbmdlKGFzeW5jIChldmVudCkgPT4ge1xuICAgICAgICAvLyBUaGUgU0lHTkVEX0lOIGV2ZW50IGlzIGZpcmVkIHZlcnkgb2Z0ZW4sIGJ1dCB3ZSBkb24ndCBuZWVkIHRvXG4gICAgICAgIC8vIGFwcGx5IHRoZSBzdG9yYWdlIGVhY2ggdGltZSBpdCBmaXJlcywgb25seSBpZiB0aGVyZSBhcmUgY2hhbmdlc1xuICAgICAgICAvLyB0aGF0IG5lZWQgdG8gYmUgc2V0IC0tIHdoaWNoIGlzIGlmIHNldEl0ZW1zIC8gcmVtb3ZlSXRlbXMgaGF2ZVxuICAgICAgICAvLyBkYXRhLlxuICAgICAgICBjb25zdCBoYXNTdG9yYWdlQ2hhbmdlcyA9IE9iamVjdC5rZXlzKHNldEl0ZW1zKS5sZW5ndGggPiAwIHx8IE9iamVjdC5rZXlzKHJlbW92ZWRJdGVtcykubGVuZ3RoID4gMDtcbiAgICAgICAgaWYgKGhhc1N0b3JhZ2VDaGFuZ2VzICYmXG4gICAgICAgICAgICAoZXZlbnQgPT09IFwiU0lHTkVEX0lOXCIgfHxcbiAgICAgICAgICAgICAgICBldmVudCA9PT0gXCJUT0tFTl9SRUZSRVNIRURcIiB8fFxuICAgICAgICAgICAgICAgIGV2ZW50ID09PSBcIlVTRVJfVVBEQVRFRFwiIHx8XG4gICAgICAgICAgICAgICAgZXZlbnQgPT09IFwiUEFTU1dPUkRfUkVDT1ZFUllcIiB8fFxuICAgICAgICAgICAgICAgIGV2ZW50ID09PSBcIlNJR05FRF9PVVRcIiB8fFxuICAgICAgICAgICAgICAgIGV2ZW50ID09PSBcIk1GQV9DSEFMTEVOR0VfVkVSSUZJRURcIikpIHtcbiAgICAgICAgICAgIGF3YWl0IGFwcGx5U2VydmVyU3RvcmFnZSh7IGdldEFsbCwgc2V0QWxsLCBzZXRJdGVtcywgcmVtb3ZlZEl0ZW1zIH0sIHtcbiAgICAgICAgICAgICAgICBjb29raWVPcHRpb25zOiBvcHRpb25zPy5jb29raWVPcHRpb25zID8/IG51bGwsXG4gICAgICAgICAgICAgICAgY29va2llRW5jb2Rpbmc6IG9wdGlvbnM/LmNvb2tpZUVuY29kaW5nID8/IFwiYmFzZTY0dXJsXCIsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBjbGllbnQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jcmVhdGVTZXJ2ZXJDbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/createServerClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.combineChunks),\n/* harmony export */   createBrowserClient: () => (/* reexport safe */ _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient),\n/* harmony export */   createChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.createChunks),\n/* harmony export */   createServerClient: () => (/* reexport safe */ _createServerClient__WEBPACK_IMPORTED_MODULE_1__.createServerClient),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createBrowserClient */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js\");\n/* harmony import */ var _createServerClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createServerClient */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/createServerClient.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"createBrowserClient\",\"createServerClient\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNEO0FBQ2I7QUFDQTtBQUN4QiIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY3JlYXRlQnJvd3NlckNsaWVudFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vY3JlYXRlU2VydmVyQ2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi90eXBlc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdXRpbHNcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/types.js":
/*!*********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/types.js ***!
  \*********************************************************/
/***/ (() => {

eval("//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/base64url.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\".split(\"\");\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = \" \\t\\n\\r=\".split(\"\");\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    let queue = 0;\n    let queuedBits = 0;\n    const emitter = (byte) => {\n        queue = (queue << 8) | byte;\n        queuedBits += 8;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    };\n    stringToUTF8(str, emitter);\n    if (queuedBits > 0) {\n        queue = queue << (6 - queuedBits);\n        queuedBits = 6;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    }\n    return base64.join(\"\");\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const state = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    let queue = 0;\n    let queuedBits = 0;\n    for (let i = 0; i < str.length; i += 1) {\n        const codepoint = str.charCodeAt(i);\n        const bits = FROM_BASE64URL[codepoint];\n        if (bits > -1) {\n            // valid Base64-URL character\n            queue = (queue << 6) | bits;\n            queuedBits += 6;\n            while (queuedBits >= 8) {\n                stringFromUTF8((queue >> (queuedBits - 8)) & 0xff, state, emit);\n                queuedBits -= 8;\n            }\n        }\n        else if (bits === -2) {\n            // ignore spaces, tabs, newlines, =\n            continue;\n        }\n        else {\n            throw new Error(`Invalid Base64-URL character \"${str.at(i)}\" at position ${i}`);\n        }\n    }\n    return conv.join(\"\");\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0JBQW9CO0FBQ3hDO0FBQ0E7QUFDQSxvQkFBb0IsNkJBQTZCO0FBQ2pEO0FBQ0E7QUFDQSxvQkFBb0IseUJBQXlCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2REFBNkQsVUFBVSxnQkFBZ0IsRUFBRTtBQUN6RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsdUJBQXVCO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxvQkFBb0IsZ0JBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCwwQkFBMEI7QUFDaEY7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLGdCQUFnQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdXRpbHNcXGJhc2U2NHVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEF2b2lkIG1vZGlmeWluZyB0aGlzIGZpbGUuIEl0J3MgcGFydCBvZlxuICogaHR0cHM6Ly9naXRodWIuY29tL3N1cGFiYXNlLWNvbW11bml0eS9iYXNlNjR1cmwtanMuICBTdWJtaXQgYWxsIGZpeGVzIG9uXG4gKiB0aGF0IHJlcG8hXG4gKi9cbi8qKlxuICogQW4gYXJyYXkgb2YgY2hhcmFjdGVycyB0aGF0IGVuY29kZSA2IGJpdHMgaW50byBhIEJhc2U2NC1VUkwgYWxwaGFiZXRcbiAqIGNoYXJhY3Rlci5cbiAqL1xuY29uc3QgVE9fQkFTRTY0VVJMID0gXCJBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OS1fXCIuc3BsaXQoXCJcIik7XG4vKipcbiAqIEFuIGFycmF5IG9mIGNoYXJhY3RlcnMgdGhhdCBjYW4gYXBwZWFyIGluIGEgQmFzZTY0LVVSTCBlbmNvZGVkIHN0cmluZyBidXRcbiAqIHNob3VsZCBiZSBpZ25vcmVkLlxuICovXG5jb25zdCBJR05PUkVfQkFTRTY0VVJMID0gXCIgXFx0XFxuXFxyPVwiLnNwbGl0KFwiXCIpO1xuLyoqXG4gKiBBbiBhcnJheSBvZiAxMjggbnVtYmVycyB0aGF0IG1hcCBhIEJhc2U2NC1VUkwgY2hhcmFjdGVyIHRvIDYgYml0cywgb3IgaWYgLTJcbiAqIHVzZWQgdG8gc2tpcCB0aGUgY2hhcmFjdGVyLCBvciBpZiAtMSB1c2VkIHRvIGVycm9yIG91dC5cbiAqL1xuY29uc3QgRlJPTV9CQVNFNjRVUkwgPSAoKCkgPT4ge1xuICAgIGNvbnN0IGNoYXJNYXAgPSBuZXcgQXJyYXkoMTI4KTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGNoYXJNYXAubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgY2hhck1hcFtpXSA9IC0xO1xuICAgIH1cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IElHTk9SRV9CQVNFNjRVUkwubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgY2hhck1hcFtJR05PUkVfQkFTRTY0VVJMW2ldLmNoYXJDb2RlQXQoMCldID0gLTI7XG4gICAgfVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgVE9fQkFTRTY0VVJMLmxlbmd0aDsgaSArPSAxKSB7XG4gICAgICAgIGNoYXJNYXBbVE9fQkFTRTY0VVJMW2ldLmNoYXJDb2RlQXQoMCldID0gaTtcbiAgICB9XG4gICAgcmV0dXJuIGNoYXJNYXA7XG59KSgpO1xuLyoqXG4gKiBDb252ZXJ0cyBhIEphdmFTY3JpcHQgc3RyaW5nICh3aGljaCBtYXkgaW5jbHVkZSBhbnkgdmFsaWQgY2hhcmFjdGVyKSBpbnRvIGFcbiAqIEJhc2U2NC1VUkwgZW5jb2RlZCBzdHJpbmcuIFRoZSBzdHJpbmcgaXMgZmlyc3QgZW5jb2RlZCBpbiBVVEYtOCB3aGljaCBpc1xuICogdGhlbiBlbmNvZGVkIGFzIEJhc2U2NC1VUkwuXG4gKlxuICogQHBhcmFtIHN0ciBUaGUgc3RyaW5nIHRvIGNvbnZlcnQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpbmdUb0Jhc2U2NFVSTChzdHIpIHtcbiAgICBjb25zdCBiYXNlNjQgPSBbXTtcbiAgICBsZXQgcXVldWUgPSAwO1xuICAgIGxldCBxdWV1ZWRCaXRzID0gMDtcbiAgICBjb25zdCBlbWl0dGVyID0gKGJ5dGUpID0+IHtcbiAgICAgICAgcXVldWUgPSAocXVldWUgPDwgOCkgfCBieXRlO1xuICAgICAgICBxdWV1ZWRCaXRzICs9IDg7XG4gICAgICAgIHdoaWxlIChxdWV1ZWRCaXRzID49IDYpIHtcbiAgICAgICAgICAgIGNvbnN0IHBvcyA9IChxdWV1ZSA+PiAocXVldWVkQml0cyAtIDYpKSAmIDYzO1xuICAgICAgICAgICAgYmFzZTY0LnB1c2goVE9fQkFTRTY0VVJMW3Bvc10pO1xuICAgICAgICAgICAgcXVldWVkQml0cyAtPSA2O1xuICAgICAgICB9XG4gICAgfTtcbiAgICBzdHJpbmdUb1VURjgoc3RyLCBlbWl0dGVyKTtcbiAgICBpZiAocXVldWVkQml0cyA+IDApIHtcbiAgICAgICAgcXVldWUgPSBxdWV1ZSA8PCAoNiAtIHF1ZXVlZEJpdHMpO1xuICAgICAgICBxdWV1ZWRCaXRzID0gNjtcbiAgICAgICAgd2hpbGUgKHF1ZXVlZEJpdHMgPj0gNikge1xuICAgICAgICAgICAgY29uc3QgcG9zID0gKHF1ZXVlID4+IChxdWV1ZWRCaXRzIC0gNikpICYgNjM7XG4gICAgICAgICAgICBiYXNlNjQucHVzaChUT19CQVNFNjRVUkxbcG9zXSk7XG4gICAgICAgICAgICBxdWV1ZWRCaXRzIC09IDY7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGJhc2U2NC5qb2luKFwiXCIpO1xufVxuLyoqXG4gKiBDb252ZXJ0cyBhIEJhc2U2NC1VUkwgZW5jb2RlZCBzdHJpbmcgaW50byBhIEphdmFTY3JpcHQgc3RyaW5nLiBJdCBpcyBhc3N1bWVkXG4gKiB0aGF0IHRoZSB1bmRlcmx5aW5nIHN0cmluZyBoYXMgYmVlbiBlbmNvZGVkIGFzIFVURi04LlxuICpcbiAqIEBwYXJhbSBzdHIgVGhlIEJhc2U2NC1VUkwgZW5jb2RlZCBzdHJpbmcuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBzdHJpbmdGcm9tQmFzZTY0VVJMKHN0cikge1xuICAgIGNvbnN0IGNvbnYgPSBbXTtcbiAgICBjb25zdCBlbWl0ID0gKGNvZGVwb2ludCkgPT4ge1xuICAgICAgICBjb252LnB1c2goU3RyaW5nLmZyb21Db2RlUG9pbnQoY29kZXBvaW50KSk7XG4gICAgfTtcbiAgICBjb25zdCBzdGF0ZSA9IHtcbiAgICAgICAgdXRmOHNlcTogMCxcbiAgICAgICAgY29kZXBvaW50OiAwLFxuICAgIH07XG4gICAgbGV0IHF1ZXVlID0gMDtcbiAgICBsZXQgcXVldWVkQml0cyA9IDA7XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBzdHIubGVuZ3RoOyBpICs9IDEpIHtcbiAgICAgICAgY29uc3QgY29kZXBvaW50ID0gc3RyLmNoYXJDb2RlQXQoaSk7XG4gICAgICAgIGNvbnN0IGJpdHMgPSBGUk9NX0JBU0U2NFVSTFtjb2RlcG9pbnRdO1xuICAgICAgICBpZiAoYml0cyA+IC0xKSB7XG4gICAgICAgICAgICAvLyB2YWxpZCBCYXNlNjQtVVJMIGNoYXJhY3RlclxuICAgICAgICAgICAgcXVldWUgPSAocXVldWUgPDwgNikgfCBiaXRzO1xuICAgICAgICAgICAgcXVldWVkQml0cyArPSA2O1xuICAgICAgICAgICAgd2hpbGUgKHF1ZXVlZEJpdHMgPj0gOCkge1xuICAgICAgICAgICAgICAgIHN0cmluZ0Zyb21VVEY4KChxdWV1ZSA+PiAocXVldWVkQml0cyAtIDgpKSAmIDB4ZmYsIHN0YXRlLCBlbWl0KTtcbiAgICAgICAgICAgICAgICBxdWV1ZWRCaXRzIC09IDg7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoYml0cyA9PT0gLTIpIHtcbiAgICAgICAgICAgIC8vIGlnbm9yZSBzcGFjZXMsIHRhYnMsIG5ld2xpbmVzLCA9XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW52YWxpZCBCYXNlNjQtVVJMIGNoYXJhY3RlciBcIiR7c3RyLmF0KGkpfVwiIGF0IHBvc2l0aW9uICR7aX1gKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gY29udi5qb2luKFwiXCIpO1xufVxuLyoqXG4gKiBDb252ZXJ0cyBhIFVuaWNvZGUgY29kZXBvaW50IHRvIGEgbXVsdGktYnl0ZSBVVEYtOCBzZXF1ZW5jZS5cbiAqXG4gKiBAcGFyYW0gY29kZXBvaW50IFRoZSBVbmljb2RlIGNvZGVwb2ludC5cbiAqIEBwYXJhbSBlbWl0ICAgICAgRnVuY3Rpb24gd2hpY2ggd2lsbCBiZSBjYWxsZWQgZm9yIGVhY2ggVVRGLTggYnl0ZSB0aGF0IHJlcHJlc2VudHMgdGhlIGNvZGVwb2ludC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvZGVwb2ludFRvVVRGOChjb2RlcG9pbnQsIGVtaXQpIHtcbiAgICBpZiAoY29kZXBvaW50IDw9IDB4N2YpIHtcbiAgICAgICAgZW1pdChjb2RlcG9pbnQpO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGVsc2UgaWYgKGNvZGVwb2ludCA8PSAweDdmZikge1xuICAgICAgICBlbWl0KDB4YzAgfCAoY29kZXBvaW50ID4+IDYpKTtcbiAgICAgICAgZW1pdCgweDgwIHwgKGNvZGVwb2ludCAmIDB4M2YpKTtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBlbHNlIGlmIChjb2RlcG9pbnQgPD0gMHhmZmZmKSB7XG4gICAgICAgIGVtaXQoMHhlMCB8IChjb2RlcG9pbnQgPj4gMTIpKTtcbiAgICAgICAgZW1pdCgweDgwIHwgKChjb2RlcG9pbnQgPj4gNikgJiAweDNmKSk7XG4gICAgICAgIGVtaXQoMHg4MCB8IChjb2RlcG9pbnQgJiAweDNmKSk7XG4gICAgICAgIHJldHVybjtcbiAgICB9XG4gICAgZWxzZSBpZiAoY29kZXBvaW50IDw9IDB4MTBmZmZmKSB7XG4gICAgICAgIGVtaXQoMHhmMCB8IChjb2RlcG9pbnQgPj4gMTgpKTtcbiAgICAgICAgZW1pdCgweDgwIHwgKChjb2RlcG9pbnQgPj4gMTIpICYgMHgzZikpO1xuICAgICAgICBlbWl0KDB4ODAgfCAoKGNvZGVwb2ludCA+PiA2KSAmIDB4M2YpKTtcbiAgICAgICAgZW1pdCgweDgwIHwgKGNvZGVwb2ludCAmIDB4M2YpKTtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0aHJvdyBuZXcgRXJyb3IoYFVucmVjb2duaXplZCBVbmljb2RlIGNvZGVwb2ludDogJHtjb2RlcG9pbnQudG9TdHJpbmcoMTYpfWApO1xufVxuLyoqXG4gKiBDb252ZXJ0cyBhIEphdmFTY3JpcHQgc3RyaW5nIHRvIGEgc2VxdWVuY2Ugb2YgVVRGLTggYnl0ZXMuXG4gKlxuICogQHBhcmFtIHN0ciAgVGhlIHN0cmluZyB0byBjb252ZXJ0IHRvIFVURi04LlxuICogQHBhcmFtIGVtaXQgRnVuY3Rpb24gd2hpY2ggd2lsbCBiZSBjYWxsZWQgZm9yIGVhY2ggVVRGLTggYnl0ZSBvZiB0aGUgc3RyaW5nLlxuICovXG5leHBvcnQgZnVuY3Rpb24gc3RyaW5nVG9VVEY4KHN0ciwgZW1pdCkge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc3RyLmxlbmd0aDsgaSArPSAxKSB7XG4gICAgICAgIGxldCBjb2RlcG9pbnQgPSBzdHIuY2hhckNvZGVBdChpKTtcbiAgICAgICAgaWYgKGNvZGVwb2ludCA+IDB4ZDdmZiAmJiBjb2RlcG9pbnQgPD0gMHhkYmZmKSB7XG4gICAgICAgICAgICAvLyBtb3N0IFVURi0xNiBjb2RlcG9pbnRzIGFyZSBVbmljb2RlIGNvZGVwb2ludHMsIGV4Y2VwdCB2YWx1ZXMgaW4gdGhpc1xuICAgICAgICAgICAgLy8gcmFuZ2Ugd2hlcmUgdGhlIG5leHQgVVRGLTE2IGNvZGVwb2ludCBuZWVkcyB0byBiZSBjb21iaW5lZCB3aXRoIHRoZVxuICAgICAgICAgICAgLy8gY3VycmVudCBvbmUgdG8gZ2V0IHRoZSBVbmljb2RlIGNvZGVwb2ludFxuICAgICAgICAgICAgY29uc3QgaGlnaFN1cnJvZ2F0ZSA9ICgoY29kZXBvaW50IC0gMHhkODAwKSAqIDB4NDAwKSAmIDB4ZmZmZjtcbiAgICAgICAgICAgIGNvbnN0IGxvd1N1cnJvZ2F0ZSA9IChzdHIuY2hhckNvZGVBdChpICsgMSkgLSAweGRjMDApICYgMHhmZmZmO1xuICAgICAgICAgICAgY29kZXBvaW50ID0gKGxvd1N1cnJvZ2F0ZSB8IGhpZ2hTdXJyb2dhdGUpICsgMHgxMDAwMDtcbiAgICAgICAgICAgIGkgKz0gMTtcbiAgICAgICAgfVxuICAgICAgICBjb2RlcG9pbnRUb1VURjgoY29kZXBvaW50LCBlbWl0KTtcbiAgICB9XG59XG4vKipcbiAqIENvbnZlcnRzIGEgVVRGLTggYnl0ZSB0byBhIFVuaWNvZGUgY29kZXBvaW50LlxuICpcbiAqIEBwYXJhbSBieXRlICBUaGUgVVRGLTggYnl0ZSBuZXh0IGluIHRoZSBzZXF1ZW5jZS5cbiAqIEBwYXJhbSBzdGF0ZSBUaGUgc2hhcmVkIHN0YXRlIGJldHdlZW4gY29uc2VjdXRpdmUgVVRGLTggYnl0ZXMgaW4gdGhlXG4gKiAgICAgICAgICAgICAgc2VxdWVuY2UsIGFuIG9iamVjdCB3aXRoIHRoZSBzaGFwZSBgeyB1dGY4c2VxOiAwLCBjb2RlcG9pbnQ6IDAgfWAuXG4gKiBAcGFyYW0gZW1pdCAgRnVuY3Rpb24gd2hpY2ggd2lsbCBiZSBjYWxsZWQgZm9yIGVhY2ggY29kZXBvaW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gc3RyaW5nRnJvbVVURjgoYnl0ZSwgc3RhdGUsIGVtaXQpIHtcbiAgICBpZiAoc3RhdGUudXRmOHNlcSA9PT0gMCkge1xuICAgICAgICBpZiAoYnl0ZSA8PSAweDdmKSB7XG4gICAgICAgICAgICBlbWl0KGJ5dGUpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIGNvdW50IHRoZSBudW1iZXIgb2YgMSBsZWFkaW5nIGJpdHMgdW50aWwgeW91IHJlYWNoIDBcbiAgICAgICAgZm9yIChsZXQgbGVhZGluZ0JpdCA9IDE7IGxlYWRpbmdCaXQgPCA2OyBsZWFkaW5nQml0ICs9IDEpIHtcbiAgICAgICAgICAgIGlmICgoKGJ5dGUgPj4gKDcgLSBsZWFkaW5nQml0KSkgJiAxKSA9PT0gMCkge1xuICAgICAgICAgICAgICAgIHN0YXRlLnV0ZjhzZXEgPSBsZWFkaW5nQml0O1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChzdGF0ZS51dGY4c2VxID09PSAyKSB7XG4gICAgICAgICAgICBzdGF0ZS5jb2RlcG9pbnQgPSBieXRlICYgMzE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc3RhdGUudXRmOHNlcSA9PT0gMykge1xuICAgICAgICAgICAgc3RhdGUuY29kZXBvaW50ID0gYnl0ZSAmIDE1O1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKHN0YXRlLnV0ZjhzZXEgPT09IDQpIHtcbiAgICAgICAgICAgIHN0YXRlLmNvZGVwb2ludCA9IGJ5dGUgJiA3O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBVVEYtOCBzZXF1ZW5jZVwiKTtcbiAgICAgICAgfVxuICAgICAgICBzdGF0ZS51dGY4c2VxIC09IDE7XG4gICAgfVxuICAgIGVsc2UgaWYgKHN0YXRlLnV0ZjhzZXEgPiAwKSB7XG4gICAgICAgIGlmIChieXRlIDw9IDB4N2YpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgVVRGLTggc2VxdWVuY2VcIik7XG4gICAgICAgIH1cbiAgICAgICAgc3RhdGUuY29kZXBvaW50ID0gKHN0YXRlLmNvZGVwb2ludCA8PCA2KSB8IChieXRlICYgNjMpO1xuICAgICAgICBzdGF0ZS51dGY4c2VxIC09IDE7XG4gICAgICAgIGlmIChzdGF0ZS51dGY4c2VxID09PSAwKSB7XG4gICAgICAgICAgICBlbWl0KHN0YXRlLmNvZGVwb2ludCk7XG4gICAgICAgIH1cbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iYXNlNjR1cmwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/chunker.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* binding */ MAX_CHUNK_SIZE),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isChunkLike: () => (/* binding */ isChunkLike)\n/* harmony export */ });\nconst MAX_CHUNK_SIZE = 3180;\nconst CHUNK_LIKE_REGEX = /^(.*)[.](0|[1-9][0-9]*)$/;\nfunction isChunkLike(cookieName, key) {\n    if (cookieName === key) {\n        return true;\n    }\n    const chunkLike = cookieName.match(CHUNK_LIKE_REGEX);\n    if (chunkLike && chunkLike[1] === key) {\n        return true;\n    }\n    return false;\n}\n/**\n * create chunks from a string and return an array of object\n */\nfunction createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [{ name: key, value }];\n    }\n    const chunks = [];\n    while (encodedValue.length > 0) {\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        // Check if the last escaped character is truncated.\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            // If so, reslice the string to exclude the whole escape sequence.\n            // We only reduce the size of the string as the chunk must\n            // be smaller than the chunk size.\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        // Check if the chunk was split along a valid unicode boundary.\n        while (encodedChunkHead.length > 0) {\n            try {\n                // Try to decode the chunk back and see if it is valid.\n                // Stop when the chunk is valid.\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            }\n            catch (error) {\n                if (error instanceof URIError &&\n                    encodedChunkHead.at(-3) === \"%\" &&\n                    encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                }\n                else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n// Get fully constructed chunks\nasync function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n    return null;\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n    }\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n//# sourceMappingURL=chunker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/utils/constants.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/constants.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS)\n/* harmony export */ });\nconst DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    // https://developer.chrome.com/blog/cookie-max-age-expires\n    // https://httpwg.org/http-extensions/draft-ietf-httpbis-rfc6265bis.html#name-cookie-lifetime-limits\n    maxAge: 400 * 24 * 60 * 60,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdXRpbHNcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgREVGQVVMVF9DT09LSUVfT1BUSU9OUyA9IHtcbiAgICBwYXRoOiBcIi9cIixcbiAgICBzYW1lU2l0ZTogXCJsYXhcIixcbiAgICBodHRwT25seTogZmFsc2UsXG4gICAgLy8gaHR0cHM6Ly9kZXZlbG9wZXIuY2hyb21lLmNvbS9ibG9nL2Nvb2tpZS1tYXgtYWdlLWV4cGlyZXNcbiAgICAvLyBodHRwczovL2h0dHB3Zy5vcmcvaHR0cC1leHRlbnNpb25zL2RyYWZ0LWlldGYtaHR0cGJpcy1yZmM2MjY1YmlzLmh0bWwjbmFtZS1jb29raWUtbGlmZXRpbWUtbGltaXRzXG4gICAgbWF4QWdlOiA0MDAgKiAyNCAqIDYwICogNjAsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/utils/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/helpers.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseCookieHeader: () => (/* binding */ parseCookieHeader),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeCookieHeader: () => (/* binding */ serializeCookieHeader)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/cookie/dist/index.js\");\n\n/**\n * @deprecated Since v0.4.0: Please use {@link parseCookieHeader}. `parse` will\n * not be available for import starting v1.0.0 of `@supabase/ssr`.\n */\nconst parse = cookie__WEBPACK_IMPORTED_MODULE_0__.parse;\n/**\n * @deprecated Since v0.4.0: Please use {@link serializeCookieHeader}.\n * `serialize` will not be available for import starting v1.0.0 of\n * `@supabase/ssr`.\n */\nconst serialize = cookie__WEBPACK_IMPORTED_MODULE_0__.serialize;\n/**\n * Parses the `Cookie` HTTP header into an array of cookie name-value objects.\n *\n * @param header The `Cookie` HTTP header. Decodes cookie names and values from\n * URI encoding first.\n */\nfunction parseCookieHeader(header) {\n    const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(header);\n    return Object.keys(parsed ?? {}).map((name) => ({\n        name,\n        value: parsed[name],\n    }));\n}\n/**\n * Converts the arguments to a valid `Set-Cookie` header. Non US-ASCII chars\n * and other forbidden cookie chars will be URI encoded.\n *\n * @param name Name of cookie.\n * @param value Value of cookie.\n */\nfunction serializeCookieHeader(name, value, options) {\n    return (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n}\nfunction isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/utils/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.combineChunks),\n/* harmony export */   createChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.createChunks),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/constants.js\");\n/* harmony import */ var _chunker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunker */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUNFO0FBQ0Y7QUFDRTtBQUM1QiIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdXRpbHNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2hlbHBlcnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbnN0YW50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY2h1bmtlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vYmFzZTY0dXJsXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/utils/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@supabase/ssr/dist/module/version.js":
/*!***********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/version.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.6.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBWRVJTSU9OID0gJzAuNi4xJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@supabase/ssr/dist/module/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/cookies.js":
/*!***********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/cookies.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyServerStorage: () => (/* binding */ applyServerStorage),\n/* harmony export */   createStorageFromOptions: () => (/* binding */ createStorageFromOptions)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/cookie/dist/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\nconst BASE64_PREFIX = \"base64-\";\n/**\n * Creates a storage client that handles cookies correctly for browser and\n * server clients with or without properly provided cookie methods.\n *\n * @param options The options passed to createBrowserClient or createServer client.\n *\n * @param isServerClient Whether it's called from createServerClient.\n */\nfunction createStorageFromOptions(options, isServerClient) {\n    const cookies = options.cookies ?? null;\n    const cookieEncoding = options.cookieEncoding;\n    const setItems = {};\n    const removedItems = {};\n    let getAll;\n    let setAll;\n    if (cookies) {\n        if (\"get\" in cookies) {\n            // Just get is not enough, because the client needs to see what cookies\n            // are already set and unset them if necessary. To attempt to fix this\n            // behavior for most use cases, we pass \"hints\" which is the keys of the\n            // storage items. They are then converted to their corresponding cookie\n            // chunk names and are fetched with get. Only 5 chunks are fetched, which\n            // should be enough for the majority of use cases, but does not solve\n            // those with very large sessions.\n            const getWithHints = async (keyHints) => {\n                // optimistically find the first 5 potential chunks for the specified key\n                const chunkNames = keyHints.flatMap((keyHint) => [\n                    keyHint,\n                    ...Array.from({ length: 5 }).map((_, i) => `${keyHint}.${i}`),\n                ]);\n                const chunks = [];\n                for (let i = 0; i < chunkNames.length; i += 1) {\n                    const value = await cookies.get(chunkNames[i]);\n                    if (!value && typeof value !== \"string\") {\n                        continue;\n                    }\n                    chunks.push({ name: chunkNames[i], value });\n                }\n                // TODO: detect and log stale chunks error\n                return chunks;\n            };\n            getAll = async (keyHints) => await getWithHints(keyHints);\n            if (\"set\" in cookies && \"remove\" in cookies) {\n                setAll = async (setCookies) => {\n                    for (let i = 0; i < setCookies.length; i += 1) {\n                        const { name, value, options } = setCookies[i];\n                        if (value) {\n                            await cookies.set(name, value, options);\n                        }\n                        else {\n                            await cookies.remove(name, options);\n                        }\n                    }\n                };\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else if (\"getAll\" in cookies) {\n            getAll = async () => await cookies.getAll();\n            if (\"setAll\" in cookies) {\n                setAll = cookies.setAll;\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else {\n            // neither get nor getAll is present on cookies, only will occur if pure JavaScript is used, but cookies is an object\n            throw new Error(`@supabase/ssr: ${isServerClient ? \"createServerClient\" : \"createBrowserClient\"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)() ? \" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.\" : \"\"}`);\n        }\n    }\n    else if (!isServerClient && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)()) {\n        // The environment is browser, so use the document.cookie API to implement getAll and setAll.\n        const noHintGetAll = () => {\n            const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n            return Object.keys(parsed).map((name) => ({\n                name,\n                value: parsed[name] ?? \"\",\n            }));\n        };\n        getAll = () => noHintGetAll();\n        setAll = (setCookies) => {\n            setCookies.forEach(({ name, value, options }) => {\n                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n            });\n        };\n    }\n    else if (isServerClient) {\n        throw new Error(\"@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)\");\n    }\n    else {\n        // getting cookies when there's no window but we're in browser mode can be OK, because the developer probably is not using auth functions\n        getAll = () => {\n            return [];\n        };\n        // this is NOT OK because the developer is using auth functions that require setting some state, so that must error out\n        setAll = () => {\n            throw new Error(\"@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed\");\n        };\n    }\n    if (!isServerClient) {\n        // This is the storage client to be used in browsers. It only\n        // works on the cookies abstraction, unlike the server client\n        // which only uses cookies to read the initial state. When an\n        // item is set, cookies are both cleared and set to values so\n        // that stale chunks are not left remaining.\n        return {\n            getAll, // for type consistency\n            setAll, // for type consistency\n            setItems, // for type consistency\n            removedItems, // for type consistency\n            storage: {\n                isServer: false,\n                getItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                        const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                        if (!cookie) {\n                            return null;\n                        }\n                        return cookie.value;\n                    });\n                    if (!chunkedCookie) {\n                        return null;\n                    }\n                    let decoded = chunkedCookie;\n                    if (chunkedCookie.startsWith(BASE64_PREFIX)) {\n                        decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                    }\n                    return decoded;\n                },\n                setItem: async (key, value) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key)));\n                    let encoded = value;\n                    if (cookieEncoding === \"base64url\") {\n                        encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(value);\n                    }\n                    const setCookies = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(key, encoded);\n                    setCookies.forEach(({ name }) => {\n                        removeCookies.delete(name);\n                    });\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    const setCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    delete setCookieOptions.name;\n                    const allToSet = [\n                        ...[...removeCookies].map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })),\n                        ...setCookies.map(({ name, value }) => ({\n                            name,\n                            value,\n                            options: setCookieOptions,\n                        })),\n                    ];\n                    if (allToSet.length > 0) {\n                        await setAll(allToSet);\n                    }\n                },\n                removeItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key));\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    if (removeCookies.length > 0) {\n                        await setAll(removeCookies.map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })));\n                    }\n                },\n            },\n        };\n    }\n    // This is the server client. It only uses getAll to read the initial\n    // state. Any subsequent changes to the items is persisted in the\n    // setItems and removedItems objects. createServerClient *must* use\n    // getAll, setAll and the values in setItems and removedItems to\n    // persist the changes *at once* when appropriate (usually only when\n    // the TOKEN_REFRESHED, USER_UPDATED or SIGNED_OUT events are fired by\n    // the Supabase Auth client).\n    return {\n        getAll,\n        setAll,\n        setItems,\n        removedItems,\n        storage: {\n            // to signal to the libraries that these cookies are\n            // coming from a server environment and their value\n            // should not be trusted\n            isServer: true,\n            getItem: async (key) => {\n                if (typeof setItems[key] === \"string\") {\n                    return setItems[key];\n                }\n                if (removedItems[key]) {\n                    return null;\n                }\n                const allCookies = await getAll([key]);\n                const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                    const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                    if (!cookie) {\n                        return null;\n                    }\n                    return cookie.value;\n                });\n                if (!chunkedCookie) {\n                    return null;\n                }\n                let decoded = chunkedCookie;\n                if (typeof chunkedCookie === \"string\" &&\n                    chunkedCookie.startsWith(BASE64_PREFIX)) {\n                    decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                }\n                return decoded;\n            },\n            setItem: async (key, value) => {\n                // We don't have an `onAuthStateChange` event that can let us know that\n                // the PKCE code verifier is being set. Therefore, if we see it being\n                // set, we need to apply the storage (call `setAll` so the cookie is\n                // set properly).\n                if (key.endsWith(\"-code-verifier\")) {\n                    await applyServerStorage({\n                        getAll,\n                        setAll,\n                        // pretend only that the code verifier was set\n                        setItems: { [key]: value },\n                        // pretend that nothing was removed\n                        removedItems: {},\n                    }, {\n                        cookieOptions: options?.cookieOptions ?? null,\n                        cookieEncoding,\n                    });\n                }\n                setItems[key] = value;\n                delete removedItems[key];\n            },\n            removeItem: async (key) => {\n                // Intentionally not applying the storage when the key is the PKCE code\n                // verifier, as usually right after it's removed other items are set,\n                // so application of the storage will be handled by the\n                // `onAuthStateChange` callback that follows removal -- usually as part\n                // of the `exchangeCodeForSession` call.\n                delete setItems[key];\n                removedItems[key] = true;\n            },\n        },\n    };\n}\n/**\n * When createServerClient needs to apply the created storage to cookies, it\n * should call this function which handles correcly setting cookies for stored\n * and removed items in the storage.\n */\nasync function applyServerStorage({ getAll, setAll, setItems, removedItems, }, options) {\n    const cookieEncoding = options.cookieEncoding;\n    const cookieOptions = options.cookieOptions ?? null;\n    const allCookies = await getAll([\n        ...(setItems ? Object.keys(setItems) : []),\n        ...(removedItems ? Object.keys(removedItems) : []),\n    ]);\n    const cookieNames = allCookies?.map(({ name }) => name) || [];\n    const removeCookies = Object.keys(removedItems).flatMap((itemName) => {\n        return cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName));\n    });\n    const setCookies = Object.keys(setItems).flatMap((itemName) => {\n        const removeExistingCookiesForItem = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName)));\n        let encoded = setItems[itemName];\n        if (cookieEncoding === \"base64url\") {\n            encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(encoded);\n        }\n        const chunks = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(itemName, encoded);\n        chunks.forEach((chunk) => {\n            removeExistingCookiesForItem.delete(chunk.name);\n        });\n        removeCookies.push(...removeExistingCookiesForItem);\n        return chunks;\n    });\n    const removeCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: 0,\n    };\n    const setCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n    };\n    // the NextJS cookieStore API can get confused if the `name` from\n    // options.cookieOptions leaks\n    delete removeCookieOptions.name;\n    delete setCookieOptions.name;\n    await setAll([\n        ...removeCookies.map((name) => ({\n            name,\n            value: \"\",\n            options: removeCookieOptions,\n        })),\n        ...setCookies.map(({ name, value }) => ({\n            name,\n            value,\n            options: setCookieOptions,\n        })),\n    ]);\n}\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS9jb29raWVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEM7QUFDcUc7QUFDL0k7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxXQUFXLG1CQUFtQixRQUFRLEdBQUcsRUFBRTtBQUMvRTtBQUNBO0FBQ0EsZ0NBQWdDLHVCQUF1QjtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyw0QkFBNEI7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsdUJBQXVCO0FBQzNELGdDQUFnQyx1QkFBdUI7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsK0RBQStELDRHQUE0RyxpREFBUywrSUFBK0k7QUFDalg7QUFDQTtBQUNBLGdDQUFnQyxpREFBUztBQUN6QztBQUNBO0FBQ0EsMkJBQTJCLDZDQUFLO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0Msc0JBQXNCO0FBQ3hELGtDQUFrQyxpREFBUztBQUMzQyxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELHFEQUFhO0FBQzdELDJEQUEyRCxNQUFNO0FBQ2pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsMkRBQW1CO0FBQ3JEO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLDJEQUEyRCxNQUFNO0FBQ2pFLCtFQUErRSxtREFBVztBQUMxRjtBQUNBO0FBQ0Esa0RBQWtELHlEQUFpQjtBQUNuRTtBQUNBLHVDQUF1QyxvREFBWTtBQUNuRCwwQ0FBMEMsTUFBTTtBQUNoRDtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLDJCQUEyQiwwREFBc0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsMERBQXNCO0FBQ2pEO0FBQ0EsZ0NBQWdDLDBEQUFzQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6Qiw2Q0FBNkMsYUFBYTtBQUMxRDtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLDJEQUEyRCxNQUFNO0FBQ2pFLHVFQUF1RSxtREFBVztBQUNsRjtBQUNBLDJCQUEyQiwwREFBc0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBLGlCQUFpQjtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLHFEQUFhO0FBQ3pELHVEQUF1RCxNQUFNO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwyREFBbUI7QUFDakQ7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxjQUFjO0FBQ2xEO0FBQ0Esd0NBQXdDO0FBQ3hDLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLG9DQUFvQyx5Q0FBeUM7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLE1BQU07QUFDakQ7QUFDQSw0Q0FBNEMsbURBQVc7QUFDdkQsS0FBSztBQUNMO0FBQ0Esa0ZBQWtGLG1EQUFXO0FBQzdGO0FBQ0E7QUFDQSxzQ0FBc0MseURBQWlCO0FBQ3ZEO0FBQ0EsdUJBQXVCLG9EQUFZO0FBQ25DO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLFdBQVcsMERBQXNCO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVywwREFBc0I7QUFDakM7QUFDQSxnQkFBZ0IsMERBQXNCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDZCQUE2QixhQUFhO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZXRvc1xcMVxcZ2VyZW1pYXNcXHNlcnZpY2V0ZWNoXFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXGNvb2tpZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcGFyc2UsIHNlcmlhbGl6ZSB9IGZyb20gXCJjb29raWVcIjtcbmltcG9ydCB7IERFRkFVTFRfQ09PS0lFX09QVElPTlMsIGNvbWJpbmVDaHVua3MsIGNyZWF0ZUNodW5rcywgaXNCcm93c2VyLCBpc0NodW5rTGlrZSwgc3RyaW5nRnJvbUJhc2U2NFVSTCwgc3RyaW5nVG9CYXNlNjRVUkwsIH0gZnJvbSBcIi4vdXRpbHNcIjtcbmNvbnN0IEJBU0U2NF9QUkVGSVggPSBcImJhc2U2NC1cIjtcbi8qKlxuICogQ3JlYXRlcyBhIHN0b3JhZ2UgY2xpZW50IHRoYXQgaGFuZGxlcyBjb29raWVzIGNvcnJlY3RseSBmb3IgYnJvd3NlciBhbmRcbiAqIHNlcnZlciBjbGllbnRzIHdpdGggb3Igd2l0aG91dCBwcm9wZXJseSBwcm92aWRlZCBjb29raWUgbWV0aG9kcy5cbiAqXG4gKiBAcGFyYW0gb3B0aW9ucyBUaGUgb3B0aW9ucyBwYXNzZWQgdG8gY3JlYXRlQnJvd3NlckNsaWVudCBvciBjcmVhdGVTZXJ2ZXIgY2xpZW50LlxuICpcbiAqIEBwYXJhbSBpc1NlcnZlckNsaWVudCBXaGV0aGVyIGl0J3MgY2FsbGVkIGZyb20gY3JlYXRlU2VydmVyQ2xpZW50LlxuICovXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlU3RvcmFnZUZyb21PcHRpb25zKG9wdGlvbnMsIGlzU2VydmVyQ2xpZW50KSB7XG4gICAgY29uc3QgY29va2llcyA9IG9wdGlvbnMuY29va2llcyA/PyBudWxsO1xuICAgIGNvbnN0IGNvb2tpZUVuY29kaW5nID0gb3B0aW9ucy5jb29raWVFbmNvZGluZztcbiAgICBjb25zdCBzZXRJdGVtcyA9IHt9O1xuICAgIGNvbnN0IHJlbW92ZWRJdGVtcyA9IHt9O1xuICAgIGxldCBnZXRBbGw7XG4gICAgbGV0IHNldEFsbDtcbiAgICBpZiAoY29va2llcykge1xuICAgICAgICBpZiAoXCJnZXRcIiBpbiBjb29raWVzKSB7XG4gICAgICAgICAgICAvLyBKdXN0IGdldCBpcyBub3QgZW5vdWdoLCBiZWNhdXNlIHRoZSBjbGllbnQgbmVlZHMgdG8gc2VlIHdoYXQgY29va2llc1xuICAgICAgICAgICAgLy8gYXJlIGFscmVhZHkgc2V0IGFuZCB1bnNldCB0aGVtIGlmIG5lY2Vzc2FyeS4gVG8gYXR0ZW1wdCB0byBmaXggdGhpc1xuICAgICAgICAgICAgLy8gYmVoYXZpb3IgZm9yIG1vc3QgdXNlIGNhc2VzLCB3ZSBwYXNzIFwiaGludHNcIiB3aGljaCBpcyB0aGUga2V5cyBvZiB0aGVcbiAgICAgICAgICAgIC8vIHN0b3JhZ2UgaXRlbXMuIFRoZXkgYXJlIHRoZW4gY29udmVydGVkIHRvIHRoZWlyIGNvcnJlc3BvbmRpbmcgY29va2llXG4gICAgICAgICAgICAvLyBjaHVuayBuYW1lcyBhbmQgYXJlIGZldGNoZWQgd2l0aCBnZXQuIE9ubHkgNSBjaHVua3MgYXJlIGZldGNoZWQsIHdoaWNoXG4gICAgICAgICAgICAvLyBzaG91bGQgYmUgZW5vdWdoIGZvciB0aGUgbWFqb3JpdHkgb2YgdXNlIGNhc2VzLCBidXQgZG9lcyBub3Qgc29sdmVcbiAgICAgICAgICAgIC8vIHRob3NlIHdpdGggdmVyeSBsYXJnZSBzZXNzaW9ucy5cbiAgICAgICAgICAgIGNvbnN0IGdldFdpdGhIaW50cyA9IGFzeW5jIChrZXlIaW50cykgPT4ge1xuICAgICAgICAgICAgICAgIC8vIG9wdGltaXN0aWNhbGx5IGZpbmQgdGhlIGZpcnN0IDUgcG90ZW50aWFsIGNodW5rcyBmb3IgdGhlIHNwZWNpZmllZCBrZXlcbiAgICAgICAgICAgICAgICBjb25zdCBjaHVua05hbWVzID0ga2V5SGludHMuZmxhdE1hcCgoa2V5SGludCkgPT4gW1xuICAgICAgICAgICAgICAgICAgICBrZXlIaW50LFxuICAgICAgICAgICAgICAgICAgICAuLi5BcnJheS5mcm9tKHsgbGVuZ3RoOiA1IH0pLm1hcCgoXywgaSkgPT4gYCR7a2V5SGludH0uJHtpfWApLFxuICAgICAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGNodW5rcyA9IFtdO1xuICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgY2h1bmtOYW1lcy5sZW5ndGg7IGkgKz0gMSkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZSA9IGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZXNbaV0pO1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXZhbHVlICYmIHR5cGVvZiB2YWx1ZSAhPT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY2h1bmtzLnB1c2goeyBuYW1lOiBjaHVua05hbWVzW2ldLCB2YWx1ZSB9KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gVE9ETzogZGV0ZWN0IGFuZCBsb2cgc3RhbGUgY2h1bmtzIGVycm9yXG4gICAgICAgICAgICAgICAgcmV0dXJuIGNodW5rcztcbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBnZXRBbGwgPSBhc3luYyAoa2V5SGludHMpID0+IGF3YWl0IGdldFdpdGhIaW50cyhrZXlIaW50cyk7XG4gICAgICAgICAgICBpZiAoXCJzZXRcIiBpbiBjb29raWVzICYmIFwicmVtb3ZlXCIgaW4gY29va2llcykge1xuICAgICAgICAgICAgICAgIHNldEFsbCA9IGFzeW5jIChzZXRDb29raWVzKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2V0Q29va2llcy5sZW5ndGg7IGkgKz0gMSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBuYW1lLCB2YWx1ZSwgb3B0aW9ucyB9ID0gc2V0Q29va2llc1tpXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IGNvb2tpZXMuc2V0KG5hbWUsIHZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IGNvb2tpZXMucmVtb3ZlKG5hbWUsIG9wdGlvbnMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGlzU2VydmVyQ2xpZW50KSB7XG4gICAgICAgICAgICAgICAgc2V0QWxsID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJAc3VwYWJhc2Uvc3NyOiBjcmVhdGVTZXJ2ZXJDbGllbnQgd2FzIGNvbmZpZ3VyZWQgd2l0aG91dCBzZXQgYW5kIHJlbW92ZSBjb29raWUgbWV0aG9kcywgYnV0IHRoZSBjbGllbnQgbmVlZHMgdG8gc2V0IGNvb2tpZXMuIFRoaXMgY2FuIGxlYWQgdG8gaXNzdWVzIHN1Y2ggYXMgcmFuZG9tIGxvZ291dHMsIGVhcmx5IHNlc3Npb24gdGVybWluYXRpb24gb3IgaW5jcmVhc2VkIHRva2VuIHJlZnJlc2ggcmVxdWVzdHMuIElmIGluIE5leHRKUywgY2hlY2sgeW91ciBtaWRkbGV3YXJlLnRzIGZpbGUsIHJvdXRlIGhhbmRsZXJzIGFuZCBzZXJ2ZXIgYWN0aW9ucyBmb3IgY29ycmVjdG5lc3MuIENvbnNpZGVyIHN3aXRjaGluZyB0byB0aGUgZ2V0QWxsIGFuZCBzZXRBbGwgY29va2llIG1ldGhvZHMgaW5zdGVhZCBvZiBnZXQsIHNldCBhbmQgcmVtb3ZlIHdoaWNoIGFyZSBkZXByZWNhdGVkIGFuZCBjYW4gYmUgZGlmZmljdWx0IHRvIHVzZSBjb3JyZWN0bHkuXCIpO1xuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJAc3VwYWJhc2Uvc3NyOiBjcmVhdGVCcm93c2VyQ2xpZW50IHJlcXVpcmVzIGNvbmZpZ3VyaW5nIGEgZ2V0QWxsIGFuZCBzZXRBbGwgY29va2llIG1ldGhvZCAoZGVwcmVjYXRlZDogYWx0ZXJuYXRpdmVseSBib3RoIGdldCwgc2V0IGFuZCByZW1vdmUgY2FuIGJlIHVzZWQpXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKFwiZ2V0QWxsXCIgaW4gY29va2llcykge1xuICAgICAgICAgICAgZ2V0QWxsID0gYXN5bmMgKCkgPT4gYXdhaXQgY29va2llcy5nZXRBbGwoKTtcbiAgICAgICAgICAgIGlmIChcInNldEFsbFwiIGluIGNvb2tpZXMpIHtcbiAgICAgICAgICAgICAgICBzZXRBbGwgPSBjb29raWVzLnNldEFsbDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGlzU2VydmVyQ2xpZW50KSB7XG4gICAgICAgICAgICAgICAgc2V0QWxsID0gYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJAc3VwYWJhc2Uvc3NyOiBjcmVhdGVTZXJ2ZXJDbGllbnQgd2FzIGNvbmZpZ3VyZWQgd2l0aG91dCB0aGUgc2V0QWxsIGNvb2tpZSBtZXRob2QsIGJ1dCB0aGUgY2xpZW50IG5lZWRzIHRvIHNldCBjb29raWVzLiBUaGlzIGNhbiBsZWFkIHRvIGlzc3VlcyBzdWNoIGFzIHJhbmRvbSBsb2dvdXRzLCBlYXJseSBzZXNzaW9uIHRlcm1pbmF0aW9uIG9yIGluY3JlYXNlZCB0b2tlbiByZWZyZXNoIHJlcXVlc3RzLiBJZiBpbiBOZXh0SlMsIGNoZWNrIHlvdXIgbWlkZGxld2FyZS50cyBmaWxlLCByb3V0ZSBoYW5kbGVycyBhbmQgc2VydmVyIGFjdGlvbnMgZm9yIGNvcnJlY3RuZXNzLlwiKTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQHN1cGFiYXNlL3NzcjogY3JlYXRlQnJvd3NlckNsaWVudCByZXF1aXJlcyBjb25maWd1cmluZyBib3RoIGdldEFsbCBhbmQgc2V0QWxsIGNvb2tpZSBtZXRob2RzIChkZXByZWNhdGVkOiBhbHRlcm5hdGl2ZWx5IGJvdGggZ2V0LCBzZXQgYW5kIHJlbW92ZSBjYW4gYmUgdXNlZClcIik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBuZWl0aGVyIGdldCBub3IgZ2V0QWxsIGlzIHByZXNlbnQgb24gY29va2llcywgb25seSB3aWxsIG9jY3VyIGlmIHB1cmUgSmF2YVNjcmlwdCBpcyB1c2VkLCBidXQgY29va2llcyBpcyBhbiBvYmplY3RcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQHN1cGFiYXNlL3NzcjogJHtpc1NlcnZlckNsaWVudCA/IFwiY3JlYXRlU2VydmVyQ2xpZW50XCIgOiBcImNyZWF0ZUJyb3dzZXJDbGllbnRcIn0gcmVxdWlyZXMgY29uZmlndXJpbmcgZ2V0QWxsIGFuZCBzZXRBbGwgY29va2llIG1ldGhvZHMgKGRlcHJlY2F0ZWQ6IGFsdGVybmF0aXZlbHkgdXNlIGdldCwgc2V0IGFuZCByZW1vdmUpLiR7aXNCcm93c2VyKCkgPyBcIiBBcyB0aGlzIGlzIGNhbGxlZCBpbiBhIGJyb3dzZXIgcnVudGltZSwgY29uc2lkZXIgcmVtb3ZpbmcgdGhlIGNvb2tpZXMgb3B0aW9uIG9iamVjdCB0byB1c2UgdGhlIGRvY3VtZW50LmNvb2tpZSBBUEkgYXV0b21hdGljYWxseS5cIiA6IFwiXCJ9YCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZWxzZSBpZiAoIWlzU2VydmVyQ2xpZW50ICYmIGlzQnJvd3NlcigpKSB7XG4gICAgICAgIC8vIFRoZSBlbnZpcm9ubWVudCBpcyBicm93c2VyLCBzbyB1c2UgdGhlIGRvY3VtZW50LmNvb2tpZSBBUEkgdG8gaW1wbGVtZW50IGdldEFsbCBhbmQgc2V0QWxsLlxuICAgICAgICBjb25zdCBub0hpbnRHZXRBbGwgPSAoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwYXJzZWQgPSBwYXJzZShkb2N1bWVudC5jb29raWUpO1xuICAgICAgICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHBhcnNlZCkubWFwKChuYW1lKSA9PiAoe1xuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgdmFsdWU6IHBhcnNlZFtuYW1lXSA/PyBcIlwiLFxuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9O1xuICAgICAgICBnZXRBbGwgPSAoKSA9PiBub0hpbnRHZXRBbGwoKTtcbiAgICAgICAgc2V0QWxsID0gKHNldENvb2tpZXMpID0+IHtcbiAgICAgICAgICAgIHNldENvb2tpZXMuZm9yRWFjaCgoeyBuYW1lLCB2YWx1ZSwgb3B0aW9ucyB9KSA9PiB7XG4gICAgICAgICAgICAgICAgZG9jdW1lbnQuY29va2llID0gc2VyaWFsaXplKG5hbWUsIHZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1NlcnZlckNsaWVudCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJAc3VwYWJhc2Uvc3NyOiBjcmVhdGVTZXJ2ZXJDbGllbnQgbXVzdCBiZSBpbml0aWFsaXplZCB3aXRoIGNvb2tpZSBvcHRpb25zIHRoYXQgc3BlY2lmeSBnZXRBbGwgYW5kIHNldEFsbCBmdW5jdGlvbnMgKGRlcHJlY2F0ZWQsIG5vdCByZWNvbW1lbmRlZDogYWx0ZXJuYXRpdmVseSB1c2UgZ2V0LCBzZXQgYW5kIHJlbW92ZSlcIik7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICAvLyBnZXR0aW5nIGNvb2tpZXMgd2hlbiB0aGVyZSdzIG5vIHdpbmRvdyBidXQgd2UncmUgaW4gYnJvd3NlciBtb2RlIGNhbiBiZSBPSywgYmVjYXVzZSB0aGUgZGV2ZWxvcGVyIHByb2JhYmx5IGlzIG5vdCB1c2luZyBhdXRoIGZ1bmN0aW9uc1xuICAgICAgICBnZXRBbGwgPSAoKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIH07XG4gICAgICAgIC8vIHRoaXMgaXMgTk9UIE9LIGJlY2F1c2UgdGhlIGRldmVsb3BlciBpcyB1c2luZyBhdXRoIGZ1bmN0aW9ucyB0aGF0IHJlcXVpcmUgc2V0dGluZyBzb21lIHN0YXRlLCBzbyB0aGF0IG11c3QgZXJyb3Igb3V0XG4gICAgICAgIHNldEFsbCA9ICgpID0+IHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkBzdXBhYmFzZS9zc3I6IGNyZWF0ZUJyb3dzZXJDbGllbnQgaW4gbm9uLWJyb3dzZXIgcnVudGltZXMgKGluY2x1ZGluZyBOZXh0LmpzIHByZS1yZW5kZXJpbmcgbW9kZSkgd2FzIG5vdCBpbml0aWFsaXplZCBjb29raWUgb3B0aW9ucyB0aGF0IHNwZWNpZnkgZ2V0QWxsIGFuZCBzZXRBbGwgZnVuY3Rpb25zIChkZXByZWNhdGVkOiBhbHRlcm5hdGl2ZWx5IHVzZSBnZXQsIHNldCBhbmQgcmVtb3ZlKSwgYnV0IHRoZXkgd2VyZSBuZWVkZWRcIik7XG4gICAgICAgIH07XG4gICAgfVxuICAgIGlmICghaXNTZXJ2ZXJDbGllbnQpIHtcbiAgICAgICAgLy8gVGhpcyBpcyB0aGUgc3RvcmFnZSBjbGllbnQgdG8gYmUgdXNlZCBpbiBicm93c2Vycy4gSXQgb25seVxuICAgICAgICAvLyB3b3JrcyBvbiB0aGUgY29va2llcyBhYnN0cmFjdGlvbiwgdW5saWtlIHRoZSBzZXJ2ZXIgY2xpZW50XG4gICAgICAgIC8vIHdoaWNoIG9ubHkgdXNlcyBjb29raWVzIHRvIHJlYWQgdGhlIGluaXRpYWwgc3RhdGUuIFdoZW4gYW5cbiAgICAgICAgLy8gaXRlbSBpcyBzZXQsIGNvb2tpZXMgYXJlIGJvdGggY2xlYXJlZCBhbmQgc2V0IHRvIHZhbHVlcyBzb1xuICAgICAgICAvLyB0aGF0IHN0YWxlIGNodW5rcyBhcmUgbm90IGxlZnQgcmVtYWluaW5nLlxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgZ2V0QWxsLCAvLyBmb3IgdHlwZSBjb25zaXN0ZW5jeVxuICAgICAgICAgICAgc2V0QWxsLCAvLyBmb3IgdHlwZSBjb25zaXN0ZW5jeVxuICAgICAgICAgICAgc2V0SXRlbXMsIC8vIGZvciB0eXBlIGNvbnNpc3RlbmN5XG4gICAgICAgICAgICByZW1vdmVkSXRlbXMsIC8vIGZvciB0eXBlIGNvbnNpc3RlbmN5XG4gICAgICAgICAgICBzdG9yYWdlOiB7XG4gICAgICAgICAgICAgICAgaXNTZXJ2ZXI6IGZhbHNlLFxuICAgICAgICAgICAgICAgIGdldEl0ZW06IGFzeW5jIChrZXkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYWxsQ29va2llcyA9IGF3YWl0IGdldEFsbChba2V5XSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNodW5rZWRDb29raWUgPSBhd2FpdCBjb21iaW5lQ2h1bmtzKGtleSwgYXN5bmMgKGNodW5rTmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgY29va2llID0gYWxsQ29va2llcz8uZmluZCgoeyBuYW1lIH0pID0+IG5hbWUgPT09IGNodW5rTmFtZSkgfHwgbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghY29va2llKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY29va2llLnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFjaHVua2VkQ29va2llKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBsZXQgZGVjb2RlZCA9IGNodW5rZWRDb29raWU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjaHVua2VkQ29va2llLnN0YXJ0c1dpdGgoQkFTRTY0X1BSRUZJWCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlY29kZWQgPSBzdHJpbmdGcm9tQmFzZTY0VVJMKGNodW5rZWRDb29raWUuc3Vic3RyaW5nKEJBU0U2NF9QUkVGSVgubGVuZ3RoKSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRlY29kZWQ7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBzZXRJdGVtOiBhc3luYyAoa2V5LCB2YWx1ZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBhbGxDb29raWVzID0gYXdhaXQgZ2V0QWxsKFtrZXldKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY29va2llTmFtZXMgPSBhbGxDb29raWVzPy5tYXAoKHsgbmFtZSB9KSA9PiBuYW1lKSB8fCBbXTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVtb3ZlQ29va2llcyA9IG5ldyBTZXQoY29va2llTmFtZXMuZmlsdGVyKChuYW1lKSA9PiBpc0NodW5rTGlrZShuYW1lLCBrZXkpKSk7XG4gICAgICAgICAgICAgICAgICAgIGxldCBlbmNvZGVkID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgICAgIGlmIChjb29raWVFbmNvZGluZyA9PT0gXCJiYXNlNjR1cmxcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgZW5jb2RlZCA9IEJBU0U2NF9QUkVGSVggKyBzdHJpbmdUb0Jhc2U2NFVSTCh2YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2V0Q29va2llcyA9IGNyZWF0ZUNodW5rcyhrZXksIGVuY29kZWQpO1xuICAgICAgICAgICAgICAgICAgICBzZXRDb29raWVzLmZvckVhY2goKHsgbmFtZSB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZW1vdmVDb29raWVzLmRlbGV0ZShuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlbW92ZUNvb2tpZU9wdGlvbnMgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5ERUZBVUxUX0NPT0tJRV9PUFRJT05TLFxuICAgICAgICAgICAgICAgICAgICAgICAgLi4ub3B0aW9ucz8uY29va2llT3B0aW9ucyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1heEFnZTogMCxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2V0Q29va2llT3B0aW9ucyA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5vcHRpb25zPy5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4QWdlOiBERUZBVUxUX0NPT0tJRV9PUFRJT05TLm1heEFnZSxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhlIE5leHRKUyBjb29raWVTdG9yZSBBUEkgY2FuIGdldCBjb25mdXNlZCBpZiB0aGUgYG5hbWVgIGZyb21cbiAgICAgICAgICAgICAgICAgICAgLy8gb3B0aW9ucy5jb29raWVPcHRpb25zIGxlYWtzXG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSByZW1vdmVDb29raWVPcHRpb25zLm5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGRlbGV0ZSBzZXRDb29raWVPcHRpb25zLm5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFsbFRvU2V0ID0gW1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4uWy4uLnJlbW92ZUNvb2tpZXNdLm1hcCgobmFtZSkgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBcIlwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM6IHJlbW92ZUNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSksXG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5zZXRDb29raWVzLm1hcCgoeyBuYW1lLCB2YWx1ZSB9KSA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uczogc2V0Q29va2llT3B0aW9ucyxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pKSxcbiAgICAgICAgICAgICAgICAgICAgXTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGFsbFRvU2V0Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IHNldEFsbChhbGxUb1NldCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHJlbW92ZUl0ZW06IGFzeW5jIChrZXkpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYWxsQ29va2llcyA9IGF3YWl0IGdldEFsbChba2V5XSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGNvb2tpZU5hbWVzID0gYWxsQ29va2llcz8ubWFwKCh7IG5hbWUgfSkgPT4gbmFtZSkgfHwgW107XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlbW92ZUNvb2tpZXMgPSBjb29raWVOYW1lcy5maWx0ZXIoKG5hbWUpID0+IGlzQ2h1bmtMaWtlKG5hbWUsIGtleSkpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZW1vdmVDb29raWVPcHRpb25zID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4uREVGQVVMVF9DT09LSUVfT1BUSU9OUyxcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLm9wdGlvbnM/LmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgICAgICBtYXhBZ2U6IDAsXG4gICAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgICAgIC8vIHRoZSBOZXh0SlMgY29va2llU3RvcmUgQVBJIGNhbiBnZXQgY29uZnVzZWQgaWYgdGhlIGBuYW1lYCBmcm9tXG4gICAgICAgICAgICAgICAgICAgIC8vIG9wdGlvbnMuY29va2llT3B0aW9ucyBsZWFrc1xuICAgICAgICAgICAgICAgICAgICBkZWxldGUgcmVtb3ZlQ29va2llT3B0aW9ucy5uYW1lO1xuICAgICAgICAgICAgICAgICAgICBpZiAocmVtb3ZlQ29va2llcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBzZXRBbGwocmVtb3ZlQ29va2llcy5tYXAoKG5hbWUpID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogXCJcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zOiByZW1vdmVDb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH1cbiAgICAvLyBUaGlzIGlzIHRoZSBzZXJ2ZXIgY2xpZW50LiBJdCBvbmx5IHVzZXMgZ2V0QWxsIHRvIHJlYWQgdGhlIGluaXRpYWxcbiAgICAvLyBzdGF0ZS4gQW55IHN1YnNlcXVlbnQgY2hhbmdlcyB0byB0aGUgaXRlbXMgaXMgcGVyc2lzdGVkIGluIHRoZVxuICAgIC8vIHNldEl0ZW1zIGFuZCByZW1vdmVkSXRlbXMgb2JqZWN0cy4gY3JlYXRlU2VydmVyQ2xpZW50ICptdXN0KiB1c2VcbiAgICAvLyBnZXRBbGwsIHNldEFsbCBhbmQgdGhlIHZhbHVlcyBpbiBzZXRJdGVtcyBhbmQgcmVtb3ZlZEl0ZW1zIHRvXG4gICAgLy8gcGVyc2lzdCB0aGUgY2hhbmdlcyAqYXQgb25jZSogd2hlbiBhcHByb3ByaWF0ZSAodXN1YWxseSBvbmx5IHdoZW5cbiAgICAvLyB0aGUgVE9LRU5fUkVGUkVTSEVELCBVU0VSX1VQREFURUQgb3IgU0lHTkVEX09VVCBldmVudHMgYXJlIGZpcmVkIGJ5XG4gICAgLy8gdGhlIFN1cGFiYXNlIEF1dGggY2xpZW50KS5cbiAgICByZXR1cm4ge1xuICAgICAgICBnZXRBbGwsXG4gICAgICAgIHNldEFsbCxcbiAgICAgICAgc2V0SXRlbXMsXG4gICAgICAgIHJlbW92ZWRJdGVtcyxcbiAgICAgICAgc3RvcmFnZToge1xuICAgICAgICAgICAgLy8gdG8gc2lnbmFsIHRvIHRoZSBsaWJyYXJpZXMgdGhhdCB0aGVzZSBjb29raWVzIGFyZVxuICAgICAgICAgICAgLy8gY29taW5nIGZyb20gYSBzZXJ2ZXIgZW52aXJvbm1lbnQgYW5kIHRoZWlyIHZhbHVlXG4gICAgICAgICAgICAvLyBzaG91bGQgbm90IGJlIHRydXN0ZWRcbiAgICAgICAgICAgIGlzU2VydmVyOiB0cnVlLFxuICAgICAgICAgICAgZ2V0SXRlbTogYXN5bmMgKGtleSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2Ygc2V0SXRlbXNba2V5XSA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2V0SXRlbXNba2V5XTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHJlbW92ZWRJdGVtc1trZXldKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBhbGxDb29raWVzID0gYXdhaXQgZ2V0QWxsKFtrZXldKTtcbiAgICAgICAgICAgICAgICBjb25zdCBjaHVua2VkQ29va2llID0gYXdhaXQgY29tYmluZUNodW5rcyhrZXksIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgY29va2llID0gYWxsQ29va2llcz8uZmluZCgoeyBuYW1lIH0pID0+IG5hbWUgPT09IGNodW5rTmFtZSkgfHwgbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFjb29raWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBjb29raWUudmFsdWU7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaWYgKCFjaHVua2VkQ29va2llKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBsZXQgZGVjb2RlZCA9IGNodW5rZWRDb29raWU7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjaHVua2VkQ29va2llID09PSBcInN0cmluZ1wiICYmXG4gICAgICAgICAgICAgICAgICAgIGNodW5rZWRDb29raWUuc3RhcnRzV2l0aChCQVNFNjRfUFJFRklYKSkge1xuICAgICAgICAgICAgICAgICAgICBkZWNvZGVkID0gc3RyaW5nRnJvbUJhc2U2NFVSTChjaHVua2VkQ29va2llLnN1YnN0cmluZyhCQVNFNjRfUFJFRklYLmxlbmd0aCkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gZGVjb2RlZDtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXRJdGVtOiBhc3luYyAoa2V5LCB2YWx1ZSkgPT4ge1xuICAgICAgICAgICAgICAgIC8vIFdlIGRvbid0IGhhdmUgYW4gYG9uQXV0aFN0YXRlQ2hhbmdlYCBldmVudCB0aGF0IGNhbiBsZXQgdXMga25vdyB0aGF0XG4gICAgICAgICAgICAgICAgLy8gdGhlIFBLQ0UgY29kZSB2ZXJpZmllciBpcyBiZWluZyBzZXQuIFRoZXJlZm9yZSwgaWYgd2Ugc2VlIGl0IGJlaW5nXG4gICAgICAgICAgICAgICAgLy8gc2V0LCB3ZSBuZWVkIHRvIGFwcGx5IHRoZSBzdG9yYWdlIChjYWxsIGBzZXRBbGxgIHNvIHRoZSBjb29raWUgaXNcbiAgICAgICAgICAgICAgICAvLyBzZXQgcHJvcGVybHkpLlxuICAgICAgICAgICAgICAgIGlmIChrZXkuZW5kc1dpdGgoXCItY29kZS12ZXJpZmllclwiKSkge1xuICAgICAgICAgICAgICAgICAgICBhd2FpdCBhcHBseVNlcnZlclN0b3JhZ2Uoe1xuICAgICAgICAgICAgICAgICAgICAgICAgZ2V0QWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0QWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gcHJldGVuZCBvbmx5IHRoYXQgdGhlIGNvZGUgdmVyaWZpZXIgd2FzIHNldFxuICAgICAgICAgICAgICAgICAgICAgICAgc2V0SXRlbXM6IHsgW2tleV06IHZhbHVlIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBwcmV0ZW5kIHRoYXQgbm90aGluZyB3YXMgcmVtb3ZlZFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVtb3ZlZEl0ZW1zOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgfSwge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29va2llT3B0aW9uczogb3B0aW9ucz8uY29va2llT3B0aW9ucyA/PyBudWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29va2llRW5jb2RpbmcsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBzZXRJdGVtc1trZXldID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgZGVsZXRlIHJlbW92ZWRJdGVtc1trZXldO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHJlbW92ZUl0ZW06IGFzeW5jIChrZXkpID0+IHtcbiAgICAgICAgICAgICAgICAvLyBJbnRlbnRpb25hbGx5IG5vdCBhcHBseWluZyB0aGUgc3RvcmFnZSB3aGVuIHRoZSBrZXkgaXMgdGhlIFBLQ0UgY29kZVxuICAgICAgICAgICAgICAgIC8vIHZlcmlmaWVyLCBhcyB1c3VhbGx5IHJpZ2h0IGFmdGVyIGl0J3MgcmVtb3ZlZCBvdGhlciBpdGVtcyBhcmUgc2V0LFxuICAgICAgICAgICAgICAgIC8vIHNvIGFwcGxpY2F0aW9uIG9mIHRoZSBzdG9yYWdlIHdpbGwgYmUgaGFuZGxlZCBieSB0aGVcbiAgICAgICAgICAgICAgICAvLyBgb25BdXRoU3RhdGVDaGFuZ2VgIGNhbGxiYWNrIHRoYXQgZm9sbG93cyByZW1vdmFsIC0tIHVzdWFsbHkgYXMgcGFydFxuICAgICAgICAgICAgICAgIC8vIG9mIHRoZSBgZXhjaGFuZ2VDb2RlRm9yU2Vzc2lvbmAgY2FsbC5cbiAgICAgICAgICAgICAgICBkZWxldGUgc2V0SXRlbXNba2V5XTtcbiAgICAgICAgICAgICAgICByZW1vdmVkSXRlbXNba2V5XSA9IHRydWU7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgIH07XG59XG4vKipcbiAqIFdoZW4gY3JlYXRlU2VydmVyQ2xpZW50IG5lZWRzIHRvIGFwcGx5IHRoZSBjcmVhdGVkIHN0b3JhZ2UgdG8gY29va2llcywgaXRcbiAqIHNob3VsZCBjYWxsIHRoaXMgZnVuY3Rpb24gd2hpY2ggaGFuZGxlcyBjb3JyZWNseSBzZXR0aW5nIGNvb2tpZXMgZm9yIHN0b3JlZFxuICogYW5kIHJlbW92ZWQgaXRlbXMgaW4gdGhlIHN0b3JhZ2UuXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhcHBseVNlcnZlclN0b3JhZ2UoeyBnZXRBbGwsIHNldEFsbCwgc2V0SXRlbXMsIHJlbW92ZWRJdGVtcywgfSwgb3B0aW9ucykge1xuICAgIGNvbnN0IGNvb2tpZUVuY29kaW5nID0gb3B0aW9ucy5jb29raWVFbmNvZGluZztcbiAgICBjb25zdCBjb29raWVPcHRpb25zID0gb3B0aW9ucy5jb29raWVPcHRpb25zID8/IG51bGw7XG4gICAgY29uc3QgYWxsQ29va2llcyA9IGF3YWl0IGdldEFsbChbXG4gICAgICAgIC4uLihzZXRJdGVtcyA/IE9iamVjdC5rZXlzKHNldEl0ZW1zKSA6IFtdKSxcbiAgICAgICAgLi4uKHJlbW92ZWRJdGVtcyA/IE9iamVjdC5rZXlzKHJlbW92ZWRJdGVtcykgOiBbXSksXG4gICAgXSk7XG4gICAgY29uc3QgY29va2llTmFtZXMgPSBhbGxDb29raWVzPy5tYXAoKHsgbmFtZSB9KSA9PiBuYW1lKSB8fCBbXTtcbiAgICBjb25zdCByZW1vdmVDb29raWVzID0gT2JqZWN0LmtleXMocmVtb3ZlZEl0ZW1zKS5mbGF0TWFwKChpdGVtTmFtZSkgPT4ge1xuICAgICAgICByZXR1cm4gY29va2llTmFtZXMuZmlsdGVyKChuYW1lKSA9PiBpc0NodW5rTGlrZShuYW1lLCBpdGVtTmFtZSkpO1xuICAgIH0pO1xuICAgIGNvbnN0IHNldENvb2tpZXMgPSBPYmplY3Qua2V5cyhzZXRJdGVtcykuZmxhdE1hcCgoaXRlbU5hbWUpID0+IHtcbiAgICAgICAgY29uc3QgcmVtb3ZlRXhpc3RpbmdDb29raWVzRm9ySXRlbSA9IG5ldyBTZXQoY29va2llTmFtZXMuZmlsdGVyKChuYW1lKSA9PiBpc0NodW5rTGlrZShuYW1lLCBpdGVtTmFtZSkpKTtcbiAgICAgICAgbGV0IGVuY29kZWQgPSBzZXRJdGVtc1tpdGVtTmFtZV07XG4gICAgICAgIGlmIChjb29raWVFbmNvZGluZyA9PT0gXCJiYXNlNjR1cmxcIikge1xuICAgICAgICAgICAgZW5jb2RlZCA9IEJBU0U2NF9QUkVGSVggKyBzdHJpbmdUb0Jhc2U2NFVSTChlbmNvZGVkKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBjaHVua3MgPSBjcmVhdGVDaHVua3MoaXRlbU5hbWUsIGVuY29kZWQpO1xuICAgICAgICBjaHVua3MuZm9yRWFjaCgoY2h1bmspID0+IHtcbiAgICAgICAgICAgIHJlbW92ZUV4aXN0aW5nQ29va2llc0Zvckl0ZW0uZGVsZXRlKGNodW5rLm5hbWUpO1xuICAgICAgICB9KTtcbiAgICAgICAgcmVtb3ZlQ29va2llcy5wdXNoKC4uLnJlbW92ZUV4aXN0aW5nQ29va2llc0Zvckl0ZW0pO1xuICAgICAgICByZXR1cm4gY2h1bmtzO1xuICAgIH0pO1xuICAgIGNvbnN0IHJlbW92ZUNvb2tpZU9wdGlvbnMgPSB7XG4gICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgIG1heEFnZTogMCxcbiAgICB9O1xuICAgIGNvbnN0IHNldENvb2tpZU9wdGlvbnMgPSB7XG4gICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgIG1heEFnZTogREVGQVVMVF9DT09LSUVfT1BUSU9OUy5tYXhBZ2UsXG4gICAgfTtcbiAgICAvLyB0aGUgTmV4dEpTIGNvb2tpZVN0b3JlIEFQSSBjYW4gZ2V0IGNvbmZ1c2VkIGlmIHRoZSBgbmFtZWAgZnJvbVxuICAgIC8vIG9wdGlvbnMuY29va2llT3B0aW9ucyBsZWFrc1xuICAgIGRlbGV0ZSByZW1vdmVDb29raWVPcHRpb25zLm5hbWU7XG4gICAgZGVsZXRlIHNldENvb2tpZU9wdGlvbnMubmFtZTtcbiAgICBhd2FpdCBzZXRBbGwoW1xuICAgICAgICAuLi5yZW1vdmVDb29raWVzLm1hcCgobmFtZSkgPT4gKHtcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB2YWx1ZTogXCJcIixcbiAgICAgICAgICAgIG9wdGlvbnM6IHJlbW92ZUNvb2tpZU9wdGlvbnMsXG4gICAgICAgIH0pKSxcbiAgICAgICAgLi4uc2V0Q29va2llcy5tYXAoKHsgbmFtZSwgdmFsdWUgfSkgPT4gKHtcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB2YWx1ZSxcbiAgICAgICAgICAgIG9wdGlvbnM6IHNldENvb2tpZU9wdGlvbnMsXG4gICAgICAgIH0pKSxcbiAgICBdKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvb2tpZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/cookies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/createBrowserClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/index.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cookies */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\n\nlet cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n    // singleton client is created only if isSingleton is set to true, or if isSingleton is not defined and we detect a browser\n    const shouldUseSingleton = options?.isSingleton === true ||\n        ((!options || !(\"isSingleton\" in options)) && (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isBrowser)());\n    if (shouldUseSingleton && cachedBrowserClient) {\n        return cachedBrowserClient;\n    }\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage } = (0,_cookies__WEBPACK_IMPORTED_MODULE_3__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, false);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_1__.VERSION} createBrowserClient`,\n            },\n        },\n        auth: {\n            ...options?.auth,\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            flowType: \"pkce\",\n            autoRefreshToken: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isBrowser)(),\n            detectSessionInUrl: (0,_utils__WEBPACK_IMPORTED_MODULE_2__.isBrowser)(),\n            persistSession: true,\n            storage,\n        },\n    });\n    if (shouldUseSingleton) {\n        cachedBrowserClient = client;\n    }\n    return client;\n}\n//# sourceMappingURL=createBrowserClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/createServerClient.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/createServerClient.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cookies */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage, getAll, setAll, setItems, removedItems } = (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, true);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_1__.VERSION} createServerClient`,\n            },\n        },\n        auth: {\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            ...options?.auth,\n            flowType: \"pkce\",\n            autoRefreshToken: false,\n            detectSessionInUrl: false,\n            persistSession: true,\n            storage,\n        },\n    });\n    client.auth.onAuthStateChange(async (event) => {\n        // The SIGNED_IN event is fired very often, but we don't need to\n        // apply the storage each time it fires, only if there are changes\n        // that need to be set -- which is if setItems / removeItems have\n        // data.\n        const hasStorageChanges = Object.keys(setItems).length > 0 || Object.keys(removedItems).length > 0;\n        if (hasStorageChanges &&\n            (event === \"SIGNED_IN\" ||\n                event === \"TOKEN_REFRESHED\" ||\n                event === \"USER_UPDATED\" ||\n                event === \"PASSWORD_RECOVERY\" ||\n                event === \"SIGNED_OUT\" ||\n                event === \"MFA_CHALLENGE_VERIFIED\")) {\n            await (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.applyServerStorage)({ getAll, setAll, setItems, removedItems }, {\n                cookieOptions: options?.cookieOptions ?? null,\n                cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n            });\n        }\n    });\n    return client;\n}\n//# sourceMappingURL=createServerClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/createServerClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.combineChunks),\n/* harmony export */   createBrowserClient: () => (/* reexport safe */ _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient),\n/* harmony export */   createChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.createChunks),\n/* harmony export */   createServerClient: () => (/* reexport safe */ _createServerClient__WEBPACK_IMPORTED_MODULE_1__.createServerClient),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createBrowserClient */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js\");\n/* harmony import */ var _createServerClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createServerClient */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/createServerClient.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"createBrowserClient\",\"createServerClient\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNEO0FBQ2I7QUFDQTtBQUN4QiIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY3JlYXRlQnJvd3NlckNsaWVudFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vY3JlYXRlU2VydmVyQ2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi90eXBlc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdXRpbHNcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/types.js":
/*!*********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/types.js ***!
  \*********************************************************/
/***/ (() => {

eval("//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/base64url.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\".split(\"\");\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = \" \\t\\n\\r=\".split(\"\");\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    let queue = 0;\n    let queuedBits = 0;\n    const emitter = (byte) => {\n        queue = (queue << 8) | byte;\n        queuedBits += 8;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    };\n    stringToUTF8(str, emitter);\n    if (queuedBits > 0) {\n        queue = queue << (6 - queuedBits);\n        queuedBits = 6;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    }\n    return base64.join(\"\");\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const state = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    let queue = 0;\n    let queuedBits = 0;\n    for (let i = 0; i < str.length; i += 1) {\n        const codepoint = str.charCodeAt(i);\n        const bits = FROM_BASE64URL[codepoint];\n        if (bits > -1) {\n            // valid Base64-URL character\n            queue = (queue << 6) | bits;\n            queuedBits += 6;\n            while (queuedBits >= 8) {\n                stringFromUTF8((queue >> (queuedBits - 8)) & 0xff, state, emit);\n                queuedBits -= 8;\n            }\n        }\n        else if (bits === -2) {\n            // ignore spaces, tabs, newlines, =\n            continue;\n        }\n        else {\n            throw new Error(`Invalid Base64-URL character \"${str.at(i)}\" at position ${i}`);\n        }\n    }\n    return conv.join(\"\");\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/chunker.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* binding */ MAX_CHUNK_SIZE),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isChunkLike: () => (/* binding */ isChunkLike)\n/* harmony export */ });\nconst MAX_CHUNK_SIZE = 3180;\nconst CHUNK_LIKE_REGEX = /^(.*)[.](0|[1-9][0-9]*)$/;\nfunction isChunkLike(cookieName, key) {\n    if (cookieName === key) {\n        return true;\n    }\n    const chunkLike = cookieName.match(CHUNK_LIKE_REGEX);\n    if (chunkLike && chunkLike[1] === key) {\n        return true;\n    }\n    return false;\n}\n/**\n * create chunks from a string and return an array of object\n */\nfunction createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [{ name: key, value }];\n    }\n    const chunks = [];\n    while (encodedValue.length > 0) {\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        // Check if the last escaped character is truncated.\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            // If so, reslice the string to exclude the whole escape sequence.\n            // We only reduce the size of the string as the chunk must\n            // be smaller than the chunk size.\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        // Check if the chunk was split along a valid unicode boundary.\n        while (encodedChunkHead.length > 0) {\n            try {\n                // Try to decode the chunk back and see if it is valid.\n                // Stop when the chunk is valid.\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            }\n            catch (error) {\n                if (error instanceof URIError &&\n                    encodedChunkHead.at(-3) === \"%\" &&\n                    encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                }\n                else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n// Get fully constructed chunks\nasync function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n    return null;\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n    }\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n//# sourceMappingURL=chunker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/utils/constants.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/constants.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS)\n/* harmony export */ });\nconst DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    // https://developer.chrome.com/blog/cookie-max-age-expires\n    // https://httpwg.org/http-extensions/draft-ietf-httpbis-rfc6265bis.html#name-cookie-lifetime-limits\n    maxAge: 400 * 24 * 60 * 60,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdXRpbHNcXGNvbnN0YW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgREVGQVVMVF9DT09LSUVfT1BUSU9OUyA9IHtcbiAgICBwYXRoOiBcIi9cIixcbiAgICBzYW1lU2l0ZTogXCJsYXhcIixcbiAgICBodHRwT25seTogZmFsc2UsXG4gICAgLy8gaHR0cHM6Ly9kZXZlbG9wZXIuY2hyb21lLmNvbS9ibG9nL2Nvb2tpZS1tYXgtYWdlLWV4cGlyZXNcbiAgICAvLyBodHRwczovL2h0dHB3Zy5vcmcvaHR0cC1leHRlbnNpb25zL2RyYWZ0LWlldGYtaHR0cGJpcy1yZmM2MjY1YmlzLmh0bWwjbmFtZS1jb29raWUtbGlmZXRpbWUtbGltaXRzXG4gICAgbWF4QWdlOiA0MDAgKiAyNCAqIDYwICogNjAsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/utils/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/helpers.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseCookieHeader: () => (/* binding */ parseCookieHeader),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeCookieHeader: () => (/* binding */ serializeCookieHeader)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/cookie/dist/index.js\");\n\n/**\n * @deprecated Since v0.4.0: Please use {@link parseCookieHeader}. `parse` will\n * not be available for import starting v1.0.0 of `@supabase/ssr`.\n */\nconst parse = cookie__WEBPACK_IMPORTED_MODULE_0__.parse;\n/**\n * @deprecated Since v0.4.0: Please use {@link serializeCookieHeader}.\n * `serialize` will not be available for import starting v1.0.0 of\n * `@supabase/ssr`.\n */\nconst serialize = cookie__WEBPACK_IMPORTED_MODULE_0__.serialize;\n/**\n * Parses the `Cookie` HTTP header into an array of cookie name-value objects.\n *\n * @param header The `Cookie` HTTP header. Decodes cookie names and values from\n * URI encoding first.\n */\nfunction parseCookieHeader(header) {\n    const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(header);\n    return Object.keys(parsed ?? {}).map((name) => ({\n        name,\n        value: parsed[name],\n    }));\n}\n/**\n * Converts the arguments to a valid `Set-Cookie` header. Non US-ASCII chars\n * and other forbidden cookie chars will be URI encoded.\n *\n * @param name Name of cookie.\n * @param value Value of cookie.\n */\nfunction serializeCookieHeader(name, value, options) {\n    return (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n}\nfunction isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/utils/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/utils/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.combineChunks),\n/* harmony export */   createChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.createChunks),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/constants.js\");\n/* harmony import */ var _chunker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunker */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS91dGlscy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQjtBQUNFO0FBQ0Y7QUFDRTtBQUM1QiIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdXRpbHNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2hlbHBlcnNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NvbnN0YW50c1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY2h1bmtlclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vYmFzZTY0dXJsXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/utils/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/module/version.js":
/*!***********************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/module/version.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.6.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L21vZHVsZS92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJEOlxcUHJvamV0b3NcXDFcXGdlcmVtaWFzXFxzZXJ2aWNldGVjaFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBWRVJTSU9OID0gJzAuNi4xJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/module/version.js\n");

/***/ })

};
;