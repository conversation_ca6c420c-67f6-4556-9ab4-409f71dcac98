'use client';

import { useState, useCallback, useMemo } from 'react';
import { Servico, CriarServicoData, AtualizarServicoData, FiltrosServicos, ServicoApiResponse } from '@/types/servicos';
import { useApiCache } from './useApiCache';

export function useServicosComCache(filtros?: FiltrosServicos) {
  const [error, setError] = useState<string | null>(null);

  // Criar chave de cache baseada nos filtros
  const cacheKey = useMemo(() => {
    const params = new URLSearchParams();
    if (filtros?.empresa_id) params.append('empresa_id', filtros.empresa_id.toString());
    if (filtros?.categoria) params.append('categoria', filtros.categoria);
    if (filtros?.ativo !== undefined) params.append('ativo', filtros.ativo.toString());
    if (filtros?.busca) params.append('busca', filtros.busca);
    return `servicos-${params.toString()}`;
  }, [filtros]);

  // Função para buscar serviços
  const fetchServicos = useCallback(async () => {
    const params = new URLSearchParams();
    
    if (filtros?.empresa_id) {
      params.append('empresa_id', filtros.empresa_id.toString());
    }
    if (filtros?.categoria) {
      params.append('categoria', filtros.categoria);
    }
    if (filtros?.ativo !== undefined) {
      params.append('ativo', filtros.ativo.toString());
    }
    if (filtros?.busca) {
      params.append('busca', filtros.busca);
    }

    const queryString = params.toString();
    const url = `/api/servicos${queryString ? `?${queryString}` : ''}`;
    const response = await fetch(url);
    const data: ServicoApiResponse = await response.json();

    if (!response.ok) {
      throw new Error(data.error ?? 'Erro ao buscar serviços');
    }

    if (data.success && Array.isArray(data.data)) {
      return data.data;
    } else {
      throw new Error('Formato de resposta inválido');
    }
  }, [filtros]);

  // Usar cache para os serviços
  const {
    data: servicos,
    loading,
    error: cacheError,
    refetch,
    invalidate,
    mutate
  } = useApiCache<Servico[]>(cacheKey, fetchServicos, {
    ttl: 5 * 60 * 1000, // 5 minutos
    staleWhileRevalidate: true
  });

  // Sincronizar erro do cache com estado local
  useMemo(() => {
    if (cacheError) {
      setError(cacheError);
    }
  }, [cacheError]);

  // Criar serviço
  const criarServico = useCallback(async (dadosServico: CriarServicoData): Promise<Servico | null> => {
    setError(null);

    try {
      const response = await fetch('/api/servicos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosServico),
      });

      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao criar serviço');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        const novoServico = data.data as Servico;
        
        // Atualizar cache otimisticamente
        mutate((servicosAtuais) => {
          if (servicosAtuais) {
            return [novoServico, ...servicosAtuais];
          }
          return [novoServico];
        });

        return novoServico;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao criar serviço:', err);
      return null;
    }
  }, [mutate]);

  // Atualizar serviço
  const atualizarServico = useCallback(async (servicoId: number, dadosAtualizacao: AtualizarServicoData): Promise<Servico | null> => {
    setError(null);

    try {
      const response = await fetch(`/api/servicos/${servicoId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizacao),
      });

      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao atualizar serviço');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        const servicoAtualizado = data.data as Servico;
        
        // Atualizar cache otimisticamente
        mutate((servicosAtuais) => {
          if (servicosAtuais) {
            return servicosAtuais.map(servico => 
              servico.servico_id === servicoId ? servicoAtualizado : servico
            );
          }
          return [servicoAtualizado];
        });

        return servicoAtualizado;
      } else {
        throw new Error('Formato de resposta inválido');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao atualizar serviço:', err);
      return null;
    }
  }, [mutate]);

  // Excluir serviço
  const excluirServico = useCallback(async (servicoId: number): Promise<boolean> => {
    setError(null);

    try {
      const response = await fetch(`/api/servicos/${servicoId}`, {
        method: 'DELETE',
      });

      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao excluir serviço');
      }

      if (data.success) {
        // Atualizar cache otimisticamente
        mutate((servicosAtuais) => {
          if (servicosAtuais) {
            return servicosAtuais.filter(servico => servico.servico_id !== servicoId);
          }
          return [];
        });

        return true;
      } else {
        throw new Error('Erro ao excluir serviço');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao excluir serviço:', err);
      return false;
    }
  }, [mutate]);

  // Buscar serviço por ID
  const buscarServicoPorId = useCallback(async (servicoId: number): Promise<Servico | null> => {
    setError(null);

    try {
      // Primeiro, tentar encontrar no cache
      if (servicos) {
        const servicoNoCache = servicos.find(s => s.servico_id === servicoId);
        if (servicoNoCache) {
          return servicoNoCache;
        }
      }

      // Se não encontrou no cache, buscar na API
      const response = await fetch(`/api/servicos/${servicoId}`);
      const data: ServicoApiResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? 'Erro ao buscar serviço');
      }

      if (data.success && data.data && !Array.isArray(data.data)) {
        return data.data as Servico;
      } else {
        throw new Error('Serviço não encontrado');
      }
    } catch (err: any) {
      setError(err.message);
      console.error('Erro ao buscar serviço:', err);
      return null;
    }
  }, [servicos]);

  // Limpar erro
  const limparErro = useCallback(() => {
    setError(null);
  }, []);

  // Invalidar cache e recarregar
  const recarregar = useCallback(() => {
    invalidate();
    return refetch();
  }, [invalidate, refetch]);

  return {
    servicos: servicos ?? [],
    loading,
    error,
    criarServico,
    atualizarServico,
    excluirServico,
    buscarServicoPorId,
    limparErro,
    recarregar,
    invalidateCache: invalidate,
    refetch
  };
}

// Hook para buscar serviços de uma empresa específica
export function useServicosEmpresa(empresaId: number) {
  return useServicosComCache({ empresa_id: empresaId, ativo: true });
}

// Hook para buscar serviços por categoria
export function useServicosPorCategoria(categoria: string, empresaId?: number) {
  return useServicosComCache({ 
    categoria, 
    empresa_id: empresaId,
    ativo: true 
  });
}

// Hook para buscar todos os serviços ativos
export function useServicosAtivos(empresaId?: number) {
  return useServicosComCache({ 
    ativo: true,
    empresa_id: empresaId
  });
}
