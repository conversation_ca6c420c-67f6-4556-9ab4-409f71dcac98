/**
 * Hook para gerenciar estatísticas do sistema Round-Robin
 * Implementação da Tarefa #17 - Lógica de Agendamento Round-Robin
 */

import { useState, useCallback, useEffect } from 'react';
import type { EstatisticasRoundRobin, ColaboradorRoundRobin } from '@/types/roundRobin';

interface RoundRobinStatsData {
  empresa_id: number;
  servico_id: number | null;
  periodo_dias: number;
  apenas_confirmados: boolean;
  estatisticas: EstatisticasRoundRobin;
  colaboradores: ColaboradorRoundRobin[];
  gerado_em: string;
}

interface UseRoundRobinStatsState {
  data: RoundRobinStatsData | null;
  loading: boolean;
  error: string | null;
}

interface UseRoundRobinStatsParams {
  empresa_id?: number;
  servico_id?: number;
  periodo_dias?: number;
  apenas_confirmados?: boolean;
  auto_refresh?: boolean;
  refresh_interval?: number; // em segundos
}

export function useRoundRobinStats(params: UseRoundRobinStatsParams = {}) {
  const {
    empresa_id,
    servico_id,
    periodo_dias = 30,
    apenas_confirmados = false,
    auto_refresh = false,
    refresh_interval = 60
  } = params;

  const [state, setState] = useState<UseRoundRobinStatsState>({
    data: null,
    loading: false,
    error: null
  });

  // Buscar estatísticas
  const buscarEstatisticas = useCallback(async (
    empresaId?: number,
    servicoId?: number,
    periodoDias?: number,
    apenasConfirmados?: boolean
  ) => {
    if (!empresaId) {
      setState(prev => ({
        ...prev,
        error: 'ID da empresa é obrigatório'
      }));
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const searchParams = new URLSearchParams({
        empresa_id: empresaId.toString(),
        periodo_dias: (periodoDias || periodo_dias).toString(),
        apenas_confirmados: (apenasConfirmados || apenas_confirmados).toString()
      });

      if (servicoId) {
        searchParams.append('servico_id', servicoId.toString());
      }

      const response = await fetch(`/api/agendamentos/round-robin-stats?${searchParams}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar estatísticas');
      }

      setState(prev => ({
        ...prev,
        data: result.data,
        loading: false
      }));

    } catch (error: any) {
      setState(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
    }
  }, [periodo_dias, apenas_confirmados]);

  // Atualizar estatísticas
  const atualizarEstatisticas = useCallback(() => {
    buscarEstatisticas(empresa_id, servico_id, periodo_dias, apenas_confirmados);
  }, [buscarEstatisticas, empresa_id, servico_id, periodo_dias, apenas_confirmados]);

  // Auto-refresh
  useEffect(() => {
    if (auto_refresh && empresa_id) {
      const interval = setInterval(atualizarEstatisticas, refresh_interval * 1000);
      return () => clearInterval(interval);
    }
  }, [auto_refresh, empresa_id, refresh_interval, atualizarEstatisticas]);

  // Buscar dados iniciais
  useEffect(() => {
    if (empresa_id) {
      atualizarEstatisticas();
    }
  }, [empresa_id, servico_id, periodo_dias, apenas_confirmados, atualizarEstatisticas]);

  // Funções auxiliares para análise dos dados
  const getColaboradorMaisUtilizado = useCallback(() => {
    if (!state.data?.estatisticas) return null;
    
    const colaboradorId = state.data.estatisticas.colaborador_mais_utilizado;
    return state.data.colaboradores.find(c => c.colaborador_user_id === colaboradorId) || null;
  }, [state.data]);

  const getColaboradorMenosUtilizado = useCallback(() => {
    if (!state.data?.estatisticas) return null;
    
    const colaboradorId = state.data.estatisticas.colaborador_menos_utilizado;
    return state.data.colaboradores.find(c => c.colaborador_user_id === colaboradorId) || null;
  }, [state.data]);

  const getDistribuicaoEquilibrada = useCallback(() => {
    if (!state.data?.estatisticas) return true;
    
    const { diferenca_maxima, total_colaboradores } = state.data.estatisticas;
    
    // Considera equilibrado se a diferença máxima for <= 20% do total de colaboradores
    const limiteEquilibrio = Math.max(1, Math.ceil(total_colaboradores * 0.2));
    return diferenca_maxima <= limiteEquilibrio;
  }, [state.data]);

  const getRecomendacoes = useCallback(() => {
    if (!state.data?.estatisticas) return [];

    const recomendacoes: string[] = [];
    const { diferenca_maxima, total_colaboradores, total_agendamentos } = state.data.estatisticas;

    if (total_agendamentos === 0) {
      recomendacoes.push('Nenhum agendamento encontrado no período selecionado.');
      return recomendacoes;
    }

    if (total_colaboradores === 1) {
      recomendacoes.push('Apenas um colaborador disponível. Considere adicionar mais colaboradores para melhor distribuição.');
      return recomendacoes;
    }

    const isEquilibrado = getDistribuicaoEquilibrada();
    
    if (!isEquilibrado) {
      recomendacoes.push(`Distribuição desbalanceada detectada (diferença de ${diferenca_maxima} agendamentos).`);
      
      const colaboradorMenos = getColaboradorMenosUtilizado();
      const colaboradorMais = getColaboradorMaisUtilizado();
      
      if (colaboradorMenos && colaboradorMais) {
        recomendacoes.push(`${colaboradorMais.name} tem ${colaboradorMais.total_agendamentos} agendamentos enquanto ${colaboradorMenos.name} tem ${colaboradorMenos.total_agendamentos}.`);
      }
    } else {
      recomendacoes.push('Distribuição de agendamentos está equilibrada entre os colaboradores.');
    }

    return recomendacoes;
  }, [state.data, getDistribuicaoEquilibrada, getColaboradorMenosUtilizado, getColaboradorMaisUtilizado]);

  return {
    // Estado
    data: state.data,
    loading: state.loading,
    error: state.error,

    // Ações
    buscarEstatisticas,
    atualizarEstatisticas,

    // Análises
    colaboradorMaisUtilizado: getColaboradorMaisUtilizado(),
    colaboradorMenosUtilizado: getColaboradorMenosUtilizado(),
    distribuicaoEquilibrada: getDistribuicaoEquilibrada(),
    recomendacoes: getRecomendacoes(),

    // Utilitários
    temDados: !!state.data,
    ultimaAtualizacao: state.data?.gerado_em
  };
}
