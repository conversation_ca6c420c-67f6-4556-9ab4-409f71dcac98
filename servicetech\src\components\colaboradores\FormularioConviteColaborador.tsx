'use client';

import React, { useMemo, useState, useEffect } from 'react';
import { BaseForm, FormField } from '@/components/forms/BaseForm';
import { CriarConviteData } from '@/types/colaboradores';
import { Servico } from '@/types/servicos';

interface FormularioConviteColaboradorProps {
  onSubmit: (dados: CriarConviteData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  servicos?: Servico[];
}

export function FormularioConviteColaborador({
  onSubmit,
  onCancel,
  loading = false,
  servicos = []
}: FormularioConviteColaboradorProps) {
  const [servicosDisponiveis, setServicosDisponiveis] = useState<Servico[]>([]);

  // Buscar serviços se não foram fornecidos
  useEffect(() => {
    if (servicos.length > 0) {
      setServicosDisponiveis(servicos);
    } else {
      // Buscar serviços da empresa
      const buscarServicos = async () => {
        try {
          const response = await fetch('/api/servicos');
          const data = await response.json();
          if (data.success && Array.isArray(data.data)) {
            setServicosDisponiveis(data.data);
          }
        } catch (error) {
          console.error('Erro ao buscar serviços:', error);
        }
      };
      buscarServicos();
    }
  }, [servicos]);

  // Configuração dos campos do formulário
  const formFields: FormField[] = useMemo(() => [
    {
      name: 'email_colaborador',
      label: 'Email do Colaborador',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>',
      helperText: 'O colaborador receberá um convite neste email',
      validation: (value: string) => {
        if (!value?.trim()) return 'Email é obrigatório';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return 'Email inválido';
        if (value.length > 255) return 'Email muito longo';
      }
    },
    {
      name: 'servicos_ids',
      label: 'Serviços que o Colaborador Pode Realizar',
      type: 'select',
      required: true,
      helperText: 'Selecione os serviços que este colaborador poderá realizar',
      options: servicosDisponiveis.map(servico => ({
        value: servico.servico_id?.toString() || '',
        label: `${servico.nome_servico} - R$ ${servico.preco?.toFixed(2)} (${servico.duracao_minutos}min)`
      })),
      validation: (value: string) => {
        if (!value) return 'Selecione pelo menos um serviço';
      }
    },
    {
      name: 'ativo_como_prestador',
      label: 'Ativo como Prestador',
      type: 'select',
      required: false,
      helperText: 'Define se o colaborador pode realizar agendamentos',
      options: [
        { value: 'true', label: 'Sim - Pode realizar agendamentos' },
        { value: 'false', label: 'Não - Apenas acesso administrativo' }
      ]
    },
    {
      name: 'percentual_comissao',
      label: 'Percentual de Comissão (%)',
      type: 'number',
      required: false,
      placeholder: '50',
      helperText: 'Percentual que o colaborador receberá dos serviços realizados',
      validation: (value: number) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = Number(value);
          if (isNaN(num)) return 'Deve ser um número válido';
          if (num < 0) return 'Percentual não pode ser negativo';
          if (num > 100) return 'Percentual não pode ser maior que 100%';
        }
      }
    },
    {
      name: 'custos_operacionais',
      label: 'Custos Operacionais (R$)',
      type: 'number',
      required: false,
      placeholder: '0.00',
      helperText: 'Custos fixos por serviço realizado pelo colaborador',
      validation: (value: number) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = Number(value);
          if (isNaN(num)) return 'Deve ser um número válido';
          if (num < 0) return 'Custos não podem ser negativos';
          if (num > 1000) return 'Valor muito alto para custos operacionais';
        }
      }
    }
  ], [servicosDisponiveis]);

  // Função para processar dados antes do envio
  const handleSubmit = async (formData: Record<string, any>) => {
    try {
      // Processar dados do formulário
      const dadosConvite: CriarConviteData = {
        email_colaborador: formData.email_colaborador,
        servicos_ids: [Number(formData.servicos_ids)], // Por enquanto apenas um serviço
        ativo_como_prestador: formData.ativo_como_prestador === 'true',
        percentual_comissao: formData.percentual_comissao ? Number(formData.percentual_comissao) : undefined,
        custos_operacionais: formData.custos_operacionais ? Number(formData.custos_operacionais) : undefined
      };

      await onSubmit(dadosConvite);
    } catch (error) {
      console.error('Erro ao processar convite:', error);
      throw error;
    }
  };

  return (
    <BaseForm
      title="Convidar Colaborador"
      description="Envie um convite para um novo colaborador se juntar à sua equipe"
      fields={formFields}
      onSubmit={handleSubmit}
      onCancel={onCancel}
      submitText="Enviar Convite"
      cancelText="Cancelar"
      loading={loading}
      initialData={{
        email_colaborador: '',
        servicos_ids: '',
        ativo_como_prestador: 'true',
        percentual_comissao: '',
        custos_operacionais: ''
      }}
    />
  );
}

// Componente para formulário de múltiplos serviços (versão avançada)
interface FormularioConviteAvancadoProps extends FormularioConviteColaboradorProps {
  permitirMultiplosServicos?: boolean;
}

export function FormularioConviteColaboradorAvancado({
  onSubmit,
  onCancel,
  loading = false,
  servicos = [],
  permitirMultiplosServicos = true
}: FormularioConviteAvancadoProps) {
  const [servicosDisponiveis, setServicosDisponiveis] = useState<Servico[]>([]);
  const [servicosSelecionados, setServicosSelecionados] = useState<number[]>([]);

  // Buscar serviços se não foram fornecidos
  useEffect(() => {
    if (servicos.length > 0) {
      setServicosDisponiveis(servicos);
    } else {
      const buscarServicos = async () => {
        try {
          const response = await fetch('/api/servicos');
          const data = await response.json();
          if (data.success && Array.isArray(data.data)) {
            setServicosDisponiveis(data.data);
          }
        } catch (error) {
          console.error('Erro ao buscar serviços:', error);
        }
      };
      buscarServicos();
    }
  }, [servicos]);

  // Campos básicos do formulário
  const formFields: FormField[] = useMemo(() => [
    {
      name: 'email_colaborador',
      label: 'Email do Colaborador',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>',
      helperText: 'O colaborador receberá um convite neste email',
      validation: (value: string) => {
        if (!value?.trim()) return 'Email é obrigatório';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) return 'Email inválido';
      }
    },
    {
      name: 'ativo_como_prestador',
      label: 'Ativo como Prestador',
      type: 'select',
      required: false,
      helperText: 'Define se o colaborador pode realizar agendamentos',
      options: [
        { value: 'true', label: 'Sim - Pode realizar agendamentos' },
        { value: 'false', label: 'Não - Apenas acesso administrativo' }
      ]
    },
    {
      name: 'percentual_comissao',
      label: 'Percentual de Comissão (%)',
      type: 'number',
      required: false,
      placeholder: '50',
      helperText: 'Percentual que o colaborador receberá dos serviços realizados',
      validation: (value: number) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = Number(value);
          if (isNaN(num) || num < 0 || num > 100) {
            return 'Percentual deve estar entre 0 e 100';
          }
        }
      }
    },
    {
      name: 'custos_operacionais',
      label: 'Custos Operacionais (R$)',
      type: 'number',
      required: false,
      placeholder: '0.00',
      helperText: 'Custos fixos por serviço realizado pelo colaborador',
      validation: (value: number) => {
        if (value !== undefined && value !== null && value !== '') {
          const num = Number(value);
          if (isNaN(num) || num < 0) {
            return 'Custos não podem ser negativos';
          }
        }
      }
    }
  ], []);

  // Função para processar dados antes do envio
  const handleSubmit = async (formData: Record<string, any>) => {
    if (servicosSelecionados.length === 0) {
      throw new Error('Selecione pelo menos um serviço para o colaborador');
    }

    try {
      const dadosConvite: CriarConviteData = {
        email_colaborador: formData.email_colaborador,
        servicos_ids: servicosSelecionados,
        ativo_como_prestador: formData.ativo_como_prestador === 'true',
        percentual_comissao: formData.percentual_comissao ? Number(formData.percentual_comissao) : undefined,
        custos_operacionais: formData.custos_operacionais ? Number(formData.custos_operacionais) : undefined
      };

      await onSubmit(dadosConvite);
    } catch (error) {
      console.error('Erro ao processar convite:', error);
      throw error;
    }
  };

  return (
    <div className="space-y-6">
      {/* Seleção de Serviços */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-lg font-semibold mb-4">Serviços que o Colaborador Pode Realizar</h3>
        <p className="text-sm text-gray-600 mb-4">
          Selecione os serviços que este colaborador poderá realizar
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {servicosDisponiveis.map(servico => (
            <label
              key={servico.servico_id}
              className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
            >
              <input
                type="checkbox"
                checked={servicosSelecionados.includes(servico.servico_id!)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setServicosSelecionados(prev => [...prev, servico.servico_id!]);
                  } else {
                    setServicosSelecionados(prev => prev.filter(id => id !== servico.servico_id));
                  }
                }}
                className="mr-3"
              />
              <div className="flex-1">
                <div className="font-medium">{servico.nome_servico}</div>
                <div className="text-sm text-gray-600">
                  R$ {servico.preco?.toFixed(2)} • {servico.duracao_minutos}min
                </div>
              </div>
            </label>
          ))}
        </div>
        
        {servicosSelecionados.length === 0 && (
          <p className="text-red-500 text-sm mt-2">Selecione pelo menos um serviço</p>
        )}
      </div>

      {/* Formulário Principal */}
      <BaseForm
        title="Dados do Colaborador"
        fields={formFields}
        onSubmit={handleSubmit}
        onCancel={onCancel}
        submitText="Enviar Convite"
        cancelText="Cancelar"
        loading={loading}
        initialData={{
          email_colaborador: '',
          ativo_como_prestador: 'true',
          percentual_comissao: '',
          custos_operacionais: ''
        }}
      />
    </div>
  );
}
