'use client';

import React, { memo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
  fallbackUrl?: string;
  showLoading?: boolean;
}

const ProtectedRoute = memo(function ProtectedRoute({
  children,
  requiredRole,
  fallbackUrl = '/login',
  showLoading = true
}: ProtectedRouteProps) {
  const { user, loading, initialized } = useAuth();
  const router = useRouter();

  // Mostrar loading enquanto verifica autenticação
  if (loading || !initialized) {
    if (!showLoading) return null;
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-[var(--background)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary)] mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
            Verificando autenticação...
          </h2>
          <p className="text-[var(--text-secondary)]">
            Aguarde um momento.
          </p>
        </div>
      </div>
    );
  }

  // Redirecionar se não estiver autenticado
  if (!user) {
    router.push(fallbackUrl);
    return null;
  }

  // Verificar papel se especificado
  if (requiredRole) {
    const hasRequiredRole = Array.isArray(requiredRole) 
      ? requiredRole.includes(user.role)
      : user.role === requiredRole;

    if (!hasRequiredRole) {
      router.push('/acesso-negado');
      return null;
    }
  }

  return <>{children}</>;
});

export { ProtectedRoute };

// Hook para verificar permissões
export function usePermissions() {
  const { user } = useAuth();

  const hasRole = (role: string | string[]) => {
    if (!user) return false;
    
    if (Array.isArray(role)) {
      return role.includes(user.role);
    }
    
    return user.role === role;
  };

  const isAdmin = () => hasRole('Administrador');
  const isOwner = () => hasRole('Proprietario');
  const isCollaborator = () => hasRole('Colaborador');
  const isUser = () => hasRole('Usuario');
  
  const canManageEstablishment = () => hasRole(['Administrador', 'Proprietario']);
  const canManageSchedules = () => hasRole(['Administrador', 'Proprietario', 'Colaborador']);
  const canViewReports = () => hasRole(['Administrador', 'Proprietario']);

  return {
    user,
    hasRole,
    isAdmin,
    isOwner,
    isCollaborator,
    isUser,
    canManageEstablishment,
    canManageSchedules,
    canViewReports,
  };
}

// Componente para renderizar conteúdo baseado em papel
interface RoleBasedRenderProps {
  allowedRoles: string | string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const RoleBasedRender = memo(function RoleBasedRender({ allowedRoles, children, fallback = null }: RoleBasedRenderProps) {
  const { hasRole } = usePermissions();
  
  if (hasRole(allowedRoles)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
});

export { RoleBasedRender };

// HOC para proteger componentes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string | string[]
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requiredRole={requiredRole}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}
