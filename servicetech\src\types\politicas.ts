// Tipos para políticas de cancelamento e reembolso

// Política de cancelamento da empresa
export interface PoliticaCancelamento {
  // Cancelamento pelo cliente
  cliente: {
    // Cancelamento com mais de 24h de antecedência (não confirmado)
    antecedencia_24h_nao_confirmado: {
      percentual_reembolso: number; // Sempre 100% conforme PRD
      ativo: boolean;
    };
    
    // Cancelamento com menos de 24h de antecedência (confirmado)
    antecedencia_24h_confirmado: {
      percentual_reembolso: number; // 0-100% configurável pela empresa
      ativo: boolean;
    };
    
    // Cancelamento no mesmo dia
    mesmo_dia: {
      percentual_reembolso: number; // 0-100% configurável pela empresa
      ativo: boolean;
    };
  };
  
  // Cancelamento pela empresa (sempre 100% reembolso)
  empresa: {
    percentual_reembolso: number; // Sempre 100% conforme PRD
    ativo: boolean;
  };
  
  // Configurações gerais
  configuracoes: {
    prazo_cancelamento_cliente: number; // Horas antes do agendamento
    permitir_cancelamento_cliente: boolean;
    notificar_cancelamentos: boolean;
    motivo_obrigatorio: boolean;
  };
  
  // Metadados
  created_at: string;
  updated_at: string;
}

// Dados para atualizar política de cancelamento
export interface AtualizarPoliticaCancelamentoData {
  cliente?: {
    antecedencia_24h_confirmado?: {
      percentual_reembolso?: number;
      ativo?: boolean;
    };
    mesmo_dia?: {
      percentual_reembolso?: number;
      ativo?: boolean;
    };
  };
  configuracoes?: {
    prazo_cancelamento_cliente?: number;
    permitir_cancelamento_cliente?: boolean;
    notificar_cancelamentos?: boolean;
    motivo_obrigatorio?: boolean;
  };
}

// Dados para cancelar agendamento
export interface CancelarAgendamentoData {
  motivo?: string;
  cancelado_por: 'cliente' | 'empresa';
  usuario_id: string;
}

// Resposta do cancelamento
export interface CancelamentoResponse {
  success: boolean;
  agendamento_cancelado: boolean;
  reembolso_processado: boolean;
  valor_reembolsado: number;
  percentual_aplicado: number;
  motivo_reembolso: string;
  message: string;
}

// Cálculo de reembolso
export interface CalculoReembolso {
  valor_original: number;
  percentual_reembolso: number;
  valor_reembolso: number;
  taxa_aplicada: number;
  motivo: string;
  pode_reembolsar: boolean;
}

// Regras de cancelamento aplicáveis
export interface RegrasCancelamento {
  pode_cancelar: boolean;
  prazo_expirado: boolean;
  horas_ate_agendamento: number;
  agendamento_confirmado: boolean;
  percentual_reembolso: number;
  motivo_regra: string;
}

// Política padrão para novas empresas
export const POLITICA_CANCELAMENTO_PADRAO: PoliticaCancelamento = {
  cliente: {
    antecedencia_24h_nao_confirmado: {
      percentual_reembolso: 100,
      ativo: true
    },
    antecedencia_24h_confirmado: {
      percentual_reembolso: 50, // Padrão: 50% de reembolso
      ativo: true
    },
    mesmo_dia: {
      percentual_reembolso: 0, // Padrão: sem reembolso no mesmo dia
      ativo: true
    }
  },
  empresa: {
    percentual_reembolso: 100, // Sempre 100% conforme PRD
    ativo: true
  },
  configuracoes: {
    prazo_cancelamento_cliente: 2, // 2 horas antes do agendamento
    permitir_cancelamento_cliente: true,
    notificar_cancelamentos: true,
    motivo_obrigatorio: false
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
};
