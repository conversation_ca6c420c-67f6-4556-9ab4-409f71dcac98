import { useState, useCallback, useEffect } from 'react';
import {
  Agendamento,
  AgendamentoCompleto,
  StatusAgendamento,
  StatusPagamento,
  FiltrosAgendamentos,
  EstatisticasAgendamentos,
  AtualizarAgendamentoData
} from '@/types/agendamentos';
import { CancelarAgendamentoData, CancelamentoResponse } from '@/types/politicas';
import { transformarAgendamentosAPI } from '@/utils/agendamentos';

interface EstadoGerenciamento {
  agendamentos: AgendamentoCompleto[];
  agendamentoSelecionado: AgendamentoCompleto | null;
  estatisticas: EstatisticasAgendamentos | null;
  loading: boolean;
  error: string | null;
  atualizando: boolean;
}

interface FiltrosAtivos extends FiltrosAgendamentos {
  periodo?: 'hoje' | 'semana' | 'mes' | 'personalizado';
  visualizacao?: 'lista' | 'calendario';
}

export function useGerenciamentoAgendamentos() {
  const [estado, setEstado] = useState<EstadoGerenciamento>({
    agendamentos: [],
    agendamentoSelecionado: null,
    estatisticas: null,
    loading: false,
    error: null,
    atualizando: false
  });

  const [filtros, setFiltros] = useState<FiltrosAtivos>({
    periodo: 'hoje',
    visualizacao: 'lista'
  });

  // Buscar agendamentos com filtros
  const buscarAgendamentos = useCallback(async (filtrosPersonalizados?: FiltrosAgendamentos) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const filtrosFinais = { ...filtros, ...filtrosPersonalizados };
      
      // Construir query params
      const params = new URLSearchParams();
      
      if (filtrosFinais.status_agendamento) {
        params.append('status_agendamento', filtrosFinais.status_agendamento);
      }
      if (filtrosFinais.status_pagamento) {
        params.append('status_pagamento', filtrosFinais.status_pagamento);
      }
      if (filtrosFinais.forma_pagamento) {
        params.append('forma_pagamento', filtrosFinais.forma_pagamento);
      }
      if (filtrosFinais.data_inicio) {
        params.append('data_inicio', filtrosFinais.data_inicio);
      }
      if (filtrosFinais.data_fim) {
        params.append('data_fim', filtrosFinais.data_fim);
      }
      if (filtrosFinais.empresa_id) {
        params.append('empresa_id', filtrosFinais.empresa_id.toString());
      }
      if (filtrosFinais.colaborador_user_id) {
        params.append('colaborador_user_id', filtrosFinais.colaborador_user_id);
      }

      // Aplicar filtros de período
      if (filtrosFinais.periodo && filtrosFinais.periodo !== 'personalizado') {
        const hoje = new Date();
        let dataInicio: Date;
        let dataFim: Date;

        switch (filtrosFinais.periodo) {
          case 'hoje':
            dataInicio = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate());
            dataFim = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate(), 23, 59, 59);
            break;
          case 'semana':
            const inicioSemana = new Date(hoje);
            inicioSemana.setDate(hoje.getDate() - hoje.getDay());
            dataInicio = new Date(inicioSemana.getFullYear(), inicioSemana.getMonth(), inicioSemana.getDate());
            dataFim = new Date(dataInicio);
            dataFim.setDate(dataInicio.getDate() + 6);
            dataFim.setHours(23, 59, 59);
            break;
          case 'mes':
            dataInicio = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
            dataFim = new Date(hoje.getFullYear(), hoje.getMonth() + 1, 0, 23, 59, 59);
            break;
          default:
            dataInicio = hoje;
            dataFim = hoje;
        }

        params.set('data_inicio', dataInicio.toISOString().split('T')[0]);
        params.set('data_fim', dataFim.toISOString().split('T')[0]);
      }

      const response = await fetch(`/api/agendamentos?${params.toString()}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar agendamentos');
      }

      // Transformar dados da API e calcular estatísticas
      const agendamentosRaw = result.data || [];
      const agendamentos = transformarAgendamentosAPI(agendamentosRaw);
      const estatisticas = calcularEstatisticas(agendamentos);

      setEstado(prev => ({
        ...prev,
        agendamentos,
        estatisticas,
        loading: false
      }));

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
    }
  }, [filtros]);

  // Buscar agendamento específico
  const buscarAgendamento = useCallback(async (agendamentoId: number) => {
    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch(`/api/agendamentos/${agendamentoId}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar agendamento');
      }

      setEstado(prev => ({
        ...prev,
        agendamentoSelecionado: result.data,
        loading: false
      }));

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
    }
  }, []);

  // Atualizar status do agendamento
  const atualizarAgendamento = useCallback(async (
    agendamentoId: number, 
    dados: AtualizarAgendamentoData
  ): Promise<boolean> => {
    setEstado(prev => ({ ...prev, atualizando: true, error: null }));

    try {
      const response = await fetch(`/api/agendamentos/${agendamentoId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar agendamento');
      }

      // Atualizar lista local
      setEstado(prev => ({
        ...prev,
        agendamentos: prev.agendamentos.map(agendamento =>
          agendamento.agendamento_id === agendamentoId
            ? { ...agendamento, ...result.data }
            : agendamento
        ),
        agendamentoSelecionado: prev.agendamentoSelecionado?.agendamento_id === agendamentoId
          ? { ...prev.agendamentoSelecionado, ...result.data }
          : prev.agendamentoSelecionado,
        atualizando: false
      }));

      // Recalcular estatísticas
      const agendamentosAtualizados = estado.agendamentos.map(agendamento =>
        agendamento.agendamento_id === agendamentoId
          ? { ...agendamento, ...result.data }
          : agendamento
      );
      const novasEstatisticas = calcularEstatisticas(agendamentosAtualizados);
      
      setEstado(prev => ({
        ...prev,
        estatisticas: novasEstatisticas
      }));

      return true;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        atualizando: false
      }));
      return false;
    }
  }, [estado.agendamentos]);

  // Confirmar agendamento
  const confirmarAgendamento = useCallback(async (agendamentoId: number): Promise<boolean> => {
    return atualizarAgendamento(agendamentoId, { status_agendamento: 'Confirmado' });
  }, [atualizarAgendamento]);

  // Recusar agendamento
  const recusarAgendamento = useCallback(async (agendamentoId: number): Promise<boolean> => {
    return atualizarAgendamento(agendamentoId, { status_agendamento: 'Recusado' });
  }, [atualizarAgendamento]);

  // Cancelar agendamento (método simples - mantido para compatibilidade)
  const cancelarAgendamento = useCallback(async (agendamentoId: number): Promise<boolean> => {
    return atualizarAgendamento(agendamentoId, { status_agendamento: 'Cancelado' });
  }, [atualizarAgendamento]);

  // Cancelar agendamento com política de reembolso
  const cancelarComPolitica = useCallback(async (
    agendamentoId: number,
    dados: CancelarAgendamentoData
  ): Promise<CancelamentoResponse | null> => {
    setEstado(prev => ({ ...prev, atualizando: true, error: null }));

    try {
      const response = await fetch(`/api/agendamentos/${agendamentoId}/cancelar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao cancelar agendamento');
      }

      // Atualizar lista local
      setEstado(prev => ({
        ...prev,
        agendamentos: prev.agendamentos.map(agendamento =>
          agendamento.agendamento_id === agendamentoId
            ? { ...agendamento, status_agendamento: 'Cancelado' }
            : agendamento
        ),
        agendamentoSelecionado: prev.agendamentoSelecionado?.agendamento_id === agendamentoId
          ? { ...prev.agendamentoSelecionado, status_agendamento: 'Cancelado' }
          : prev.agendamentoSelecionado,
        atualizando: false
      }));

      // Recalcular estatísticas
      const agendamentosAtualizados = estado.agendamentos.map(agendamento =>
        agendamento.agendamento_id === agendamentoId
          ? { ...agendamento, status_agendamento: 'Cancelado' as StatusAgendamento }
          : agendamento
      );
      const novasEstatisticas = calcularEstatisticas(agendamentosAtualizados);

      setEstado(prev => ({
        ...prev,
        estatisticas: novasEstatisticas
      }));

      return result as CancelamentoResponse;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        atualizando: false
      }));
      return null;
    }
  }, [estado.agendamentos]);

  // Concluir agendamento
  const concluirAgendamento = useCallback(async (agendamentoId: number): Promise<boolean> => {
    return atualizarAgendamento(agendamentoId, { status_agendamento: 'Concluido' });
  }, [atualizarAgendamento]);

  // Marcar como pago
  const marcarComoPago = useCallback(async (agendamentoId: number): Promise<boolean> => {
    return atualizarAgendamento(agendamentoId, { status_pagamento: 'Pago' });
  }, [atualizarAgendamento]);

  // Aplicar filtros
  const aplicarFiltros = useCallback((novosFiltros: FiltrosAtivos) => {
    setFiltros(prev => ({ ...prev, ...novosFiltros }));
  }, []);

  // Limpar filtros
  const limparFiltros = useCallback(() => {
    setFiltros({
      periodo: 'hoje',
      visualizacao: 'lista'
    });
  }, []);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  // Limpar seleção
  const limparSelecao = useCallback(() => {
    setEstado(prev => ({ ...prev, agendamentoSelecionado: null }));
  }, []);

  // Calcular estatísticas
  const calcularEstatisticas = (agendamentos: AgendamentoCompleto[]): EstatisticasAgendamentos => {
    const total = agendamentos.length;
    const pendentes = agendamentos.filter(a => a.status_agendamento === 'Pendente').length;
    const confirmados = agendamentos.filter(a => a.status_agendamento === 'Confirmado').length;
    const cancelados = agendamentos.filter(a => a.status_agendamento === 'Cancelado').length;
    const concluidos = agendamentos.filter(a => a.status_agendamento === 'Concluido').length;
    
    const valor_total = agendamentos
      .filter(a => a.status_agendamento === 'Concluido' && a.status_pagamento === 'Pago')
      .reduce((sum, a) => sum + a.valor_total, 0);

    const por_status = agendamentos.reduce((acc, a) => {
      acc[a.status_agendamento] = (acc[a.status_agendamento] || 0) + 1;
      return acc;
    }, {} as { [status: string]: number });

    const por_forma_pagamento = agendamentos.reduce((acc, a) => {
      acc[a.forma_pagamento] = (acc[a.forma_pagamento] || 0) + 1;
      return acc;
    }, {} as { [forma: string]: number });

    return {
      total,
      pendentes,
      confirmados,
      cancelados,
      concluidos,
      valor_total,
      por_status,
      por_forma_pagamento
    };
  };

  // Auto-refresh a cada 30 segundos para agendamentos pendentes
  useEffect(() => {
    const interval = setInterval(() => {
      if (estado.agendamentos.some(a => a.status_agendamento === 'Pendente')) {
        buscarAgendamentos();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [estado.agendamentos, buscarAgendamentos]);

  return {
    // Estado
    agendamentos: estado.agendamentos,
    agendamentoSelecionado: estado.agendamentoSelecionado,
    estatisticas: estado.estatisticas,
    loading: estado.loading,
    error: estado.error,
    atualizando: estado.atualizando,
    filtros,

    // Ações
    buscarAgendamentos,
    buscarAgendamento,
    atualizarAgendamento,
    confirmarAgendamento,
    recusarAgendamento,
    cancelarAgendamento,
    cancelarComPolitica,
    concluirAgendamento,
    marcarComoPago,
    aplicarFiltros,
    limparFiltros,
    limparErro,
    limparSelecao
  };
}
