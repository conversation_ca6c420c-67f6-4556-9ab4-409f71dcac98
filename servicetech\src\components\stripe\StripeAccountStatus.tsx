'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface StatusConta {
  status: 'not_connected' | 'pending' | 'active' | 'restricted';
  mensagem: string;
  cor: 'gray' | 'yellow' | 'green' | 'red';
}

interface DadosEmpresa {
  nome: string;
  pagamentos_habilitados: boolean;
  percentual_comissao: number;
}

interface StatusStripeConnect {
  connected: boolean;
  account_id?: string;
  status: StatusConta;
  charges_enabled?: boolean;
  payouts_enabled?: boolean;
  details_submitted?: boolean;
  requirements?: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
  capabilities?: {
    card_payments: string;
    transfers: string;
  };
  empresa: DadosEmpresa;
  pode_receber_pagamentos: boolean;
  requer_configuracao: boolean;
  dashboard_url?: string;
  erro_verificacao?: boolean;
}

interface StripeAccountStatusProps {
  status: StatusStripeConnect;
}

export function StripeAccountStatus({ status }: StripeAccountStatusProps) {
  const getStatusIcon = () => {
    switch (status.status.cor) {
      case 'green':
        return (
          <div className="w-8 h-8 bg-[var(--success)] rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'yellow':
        return (
          <div className="w-8 h-8 bg-[var(--warning)] rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        );
      case 'red':
        return (
          <div className="w-8 h-8 bg-[var(--error)] rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-[var(--text-secondary)] rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getStatusColor = () => {
    switch (status.status.cor) {
      case 'green': return 'var(--success-light)';
      case 'yellow': return 'var(--warning-light)';
      case 'red': return 'var(--error-light)';
      default: return 'var(--surface)';
    }
  };

  const getBorderColor = () => {
    switch (status.status.cor) {
      case 'green': return 'var(--success)';
      case 'yellow': return 'var(--warning)';
      case 'red': return 'var(--error)';
      default: return 'var(--border)';
    }
  };

  const formatarRequirements = (requirements: string[]) => {
    const traducoes: Record<string, string> = {
      'business_profile.url': 'URL do site da empresa',
      'business_profile.mcc': 'Categoria de negócio',
      'business_profile.product_description': 'Descrição dos produtos/serviços',
      'company.tax_id': 'CNPJ da empresa',
      'company.address.line1': 'Endereço da empresa',
      'company.address.city': 'Cidade da empresa',
      'company.address.state': 'Estado da empresa',
      'company.address.postal_code': 'CEP da empresa',
      'company.phone': 'Telefone da empresa',
      'individual.id_number': 'CPF do representante',
      'individual.address.line1': 'Endereço do representante',
      'individual.dob.day': 'Data de nascimento',
      'individual.dob.month': 'Data de nascimento',
      'individual.dob.year': 'Data de nascimento',
      'individual.phone': 'Telefone do representante',
      'individual.email': 'Email do representante',
      'tos_acceptance.date': 'Aceite dos termos de serviço',
      'tos_acceptance.ip': 'Aceite dos termos de serviço'
    };

    return requirements.map(req => traducoes[req] || req);
  };

  return (
    <Card 
      className="border-2"
      style={{ 
        backgroundColor: getStatusColor(),
        borderColor: getBorderColor()
      }}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          {getStatusIcon()}
          <div>
            <div className="text-[var(--text-primary)]">Status da Conta</div>
            <div className="text-sm font-normal text-[var(--text-secondary)]">
              {status.status.mensagem}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Capacidades */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${status.charges_enabled ? 'bg-[var(--success)]' : 'bg-[var(--error)]'}`}></div>
            <span className="text-sm text-[var(--text-secondary)]">
              Receber Pagamentos
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${status.payouts_enabled ? 'bg-[var(--success)]' : 'bg-[var(--error)]'}`}></div>
            <span className="text-sm text-[var(--text-secondary)]">
              Receber Transferências
            </span>
          </div>
        </div>

        {/* Configuração da Plataforma */}
        <div className="flex items-center justify-between p-3 bg-white/50 rounded-lg">
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${status.empresa.pagamentos_habilitados ? 'bg-[var(--success)]' : 'bg-[var(--text-secondary)]'}`}></div>
            <span className="text-sm text-[var(--text-secondary)]">
              Pagamentos Online na Plataforma
            </span>
          </div>
          <span className="text-sm font-medium text-[var(--text-primary)]">
            {status.empresa.pagamentos_habilitados ? 'Habilitado' : 'Desabilitado'}
          </span>
        </div>

        {/* Documentação Pendente */}
        {status.requirements && (
          <>
            {status.requirements.currently_due.length > 0 && (
              <div className="p-3 bg-[var(--warning-light)] border border-[var(--warning)] rounded-lg">
                <h5 className="font-medium text-[var(--text-primary)] mb-2">
                  📋 Documentação Pendente
                </h5>
                <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                  {formatarRequirements(status.requirements.currently_due).map((req, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-[var(--warning)] rounded-full"></div>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {status.requirements.past_due.length > 0 && (
              <div className="p-3 bg-[var(--error-light)] border border-[var(--error)] rounded-lg">
                <h5 className="font-medium text-[var(--text-primary)] mb-2">
                  ⚠️ Documentação em Atraso
                </h5>
                <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                  {formatarRequirements(status.requirements.past_due).map((req, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-[var(--error)] rounded-full"></div>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {status.requirements.pending_verification.length > 0 && (
              <div className="p-3 bg-[var(--info-light)] border border-[var(--info)] rounded-lg">
                <h5 className="font-medium text-[var(--text-primary)] mb-2">
                  🔍 Em Verificação
                </h5>
                <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                  {formatarRequirements(status.requirements.pending_verification).map((req, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-[var(--info)] rounded-full"></div>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </>
        )}

        {/* Ações */}
        {status.requer_configuracao && (
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => window.location.reload()}
              className="flex-1"
            >
              Completar Configuração
            </Button>
          </div>
        )}

        {/* Informações Adicionais */}
        {status.erro_verificacao && (
          <div className="p-3 bg-[var(--warning-light)] border border-[var(--warning)] rounded-lg">
            <p className="text-sm text-[var(--text-secondary)]">
              ⚠️ Não foi possível verificar o status atual no Stripe. 
              Os dados mostrados podem estar desatualizados.
            </p>
          </div>
        )}

        {/* Preview para Clientes */}
        {status.pode_receber_pagamentos && status.empresa.pagamentos_habilitados && (
          <div className="p-3 bg-[var(--success-light)] border border-[var(--success)] rounded-lg">
            <h5 className="font-medium text-[var(--text-primary)] mb-2">
              ✅ Configuração Completa
            </h5>
            <p className="text-sm text-[var(--text-secondary)]">
              Seus clientes já podem pagar agendamentos online com cartão de crédito/débito e Pix.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
