// Tipos para o módulo de agendamentos

// Interface principal do agendamento (baseada no schema do banco)
export interface Agendamento {
  agendamento_id: number;
  cliente_user_id: string;
  empresa_id: number;
  colaborador_user_id: string;
  servico_id: number;
  data_hora_inicio: string;
  data_hora_fim: string;
  observacoes_cliente?: string;
  status_agendamento: StatusAgendamento;
  forma_pagamento: FormaPagamento;
  status_pagamento: StatusPagamento;
  valor_total: number;
  valor_desconto?: number;
  stripe_payment_intent_id?: string;
  codigo_confirmacao: string;
  prazo_confirmacao: string;
  created_at: string;
  updated_at: string;
}

// Status do agendamento
export type StatusAgendamento = 
  | 'Pendente' 
  | 'Confirmado' 
  | 'Recusado' 
  | 'Cancelado' 
  | 'Concluido';

// Forma de pagamento
export type FormaPagamento = 
  | 'Online' 
  | 'Local';

// Status do pagamento
export type StatusPagamento = 
  | 'Pendente' 
  | 'Pago' 
  | 'Reembolsado' 
  | 'Falhou';

// Dados para criação de um novo agendamento
export interface CriarAgendamentoData {
  empresa_id: number;
  colaborador_user_id?: string; // Opcional - se não informado, usa round-robin
  servicos_ids: number[]; // Array de IDs dos serviços
  data_hora_inicio: string;
  observacoes_cliente?: string;
  forma_pagamento: FormaPagamento;
  combo_id?: number; // ID do combo aplicado (opcional)
  valor_desconto?: number; // Valor do desconto aplicado
}

// Dados para atualização de um agendamento
export interface AtualizarAgendamentoData {
  status_agendamento?: StatusAgendamento;
  status_pagamento?: StatusPagamento;
  observacoes_cliente?: string;
  stripe_payment_intent_id?: string;
}

// Dados para criar Payment Intent de agendamento
export interface CriarPaymentIntentAgendamentoData {
  agendamento_id: number;
  valor: number;
  descricao?: string;
}

// Resposta da criação de Payment Intent
export interface PaymentIntentResponse {
  client_secret: string;
  payment_intent_id: string;
}

// Dados para processar reembolso
export interface ProcessarReembolsoData {
  agendamento_id: number;
  motivo?: string;
  valor_reembolso?: number; // Se não informado, reembolsa o valor total
}

// Resposta do reembolso
export interface ReembolsoResponse {
  success: boolean;
  refund_id?: string;
  valor_reembolsado: number;
  message: string;
}

// Agendamento com dados relacionados (para exibição)
export interface AgendamentoCompleto extends Agendamento {
  empresa: {
    nome_empresa: string;
    endereco: string;
    telefone?: string;
  };
  colaborador: {
    name: string;
    email: string;
  };
  servico: {
    nome_servico: string;
    descricao: string;
    duracao_minutos: number;
    preco: number;
    categoria: string;
  };
  cliente: {
    name: string;
    email: string;
    phone?: string;
  };
}

// Resposta da API para operações de agendamentos
export interface AgendamentoApiResponse {
  success: boolean;
  data?: Agendamento | Agendamento[] | AgendamentoCompleto | AgendamentoCompleto[];
  error?: string;
  message?: string;
}

// Filtros para busca de agendamentos
export interface FiltrosAgendamentos {
  status_agendamento?: StatusAgendamento;
  status_pagamento?: StatusPagamento;
  forma_pagamento?: FormaPagamento;
  data_inicio?: string;
  data_fim?: string;
  empresa_id?: number;
  colaborador_user_id?: string;
  cliente_user_id?: string;
}

// Estatísticas de agendamentos
export interface EstatisticasAgendamentos {
  total: number;
  pendentes: number;
  confirmados: number;
  cancelados: number;
  concluidos: number;
  valor_total: number;
  por_status: { [status: string]: number };
  por_forma_pagamento: { [forma: string]: number };
}

// Serviço selecionado no fluxo
export interface ServicoSelecionado {
  servico_id: number;
  nome_servico: string;
  duracao_minutos: number;
  preco: number;
  categoria?: string;
}

// Dados do fluxo de agendamento
export interface FluxoAgendamento {
  empresa_id: number;
  servicos_selecionados: ServicoSelecionado[];
  colaborador_selecionado?: {
    colaborador_user_id: string;
    name: string;
  };
  horario_selecionado?: {
    data_hora_inicio: string;
    data_hora_fim: string;
  };
  observacoes?: string;
  forma_pagamento?: FormaPagamento;
  valor_total: number;
  valor_original: number;
  valor_desconto: number;
  combo_aplicado?: {
    combo_id: number;
    nome_combo: string;
    descricao: string;
    economia: number;
    tipo_desconto: 'valor_fixo' | 'percentual';
    valor_desconto: number;
  };
}

// Validação de agendamento
export interface ValidacaoAgendamento {
  valido: boolean;
  erros: string[];
  avisos?: string[];
}
