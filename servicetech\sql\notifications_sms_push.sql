-- Tabela para preferências de notificação dos usuários
CREATE TABLE IF NOT EXISTS preferencias_notificacao (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  email_enabled BOOLEAN DEFAULT true,
  sms_enabled BOOLEAN DEFAULT true,
  push_enabled BOOLEAN DEFAULT true,
  tipos_habilitados JSONB DEFAULT '["novo_agendamento", "agendamento_confirmado", "agendamento_recusado", "agendamento_cancelado", "lembrete_confirmacao", "lembrete_agendamento", "pagamento_confirmado"]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Tabela para tokens de dispositivos (notificações push)
CREATE TABLE IF NOT EXISTS device_tokens (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  token TEXT NOT NULL,
  platform VARCHAR(20) NOT NULL CHECK (platform IN ('web', 'android', 'ios')),
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, token)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_preferencias_notificacao_user_id ON preferencias_notificacao(user_id);
CREATE INDEX IF NOT EXISTS idx_device_tokens_user_id ON device_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_device_tokens_active ON device_tokens(active) WHERE active = true;

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_preferencias_notificacao_updated_at 
    BEFORE UPDATE ON preferencias_notificacao 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_device_tokens_updated_at 
    BEFORE UPDATE ON device_tokens 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Políticas RLS para preferencias_notificacao
ALTER TABLE preferencias_notificacao ENABLE ROW LEVEL SECURITY;

-- Usuários podem ver e editar apenas suas próprias preferências
CREATE POLICY "Usuários podem ver suas preferências" ON preferencias_notificacao
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem inserir suas preferências" ON preferencias_notificacao
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem atualizar suas preferências" ON preferencias_notificacao
    FOR UPDATE USING (auth.uid() = user_id);

-- Administradores podem ver todas as preferências
CREATE POLICY "Administradores podem ver todas as preferências" ON preferencias_notificacao
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.user_metadata->>'role' = 'Administrador'
        )
    );

-- Políticas RLS para device_tokens
ALTER TABLE device_tokens ENABLE ROW LEVEL SECURITY;

-- Usuários podem gerenciar apenas seus próprios tokens
CREATE POLICY "Usuários podem ver seus tokens" ON device_tokens
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem inserir seus tokens" ON device_tokens
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Usuários podem atualizar seus tokens" ON device_tokens
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Usuários podem deletar seus tokens" ON device_tokens
    FOR DELETE USING (auth.uid() = user_id);

-- Administradores podem ver todos os tokens (para estatísticas)
CREATE POLICY "Administradores podem ver todos os tokens" ON device_tokens
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.user_metadata->>'role' = 'Administrador'
        )
    );

-- Função para criar preferências padrão quando um usuário se cadastra
CREATE OR REPLACE FUNCTION create_default_notification_preferences()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO preferencias_notificacao (user_id)
    VALUES (NEW.id)
    ON CONFLICT (user_id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para criar preferências padrão automaticamente
CREATE TRIGGER on_auth_user_created_notification_preferences
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION create_default_notification_preferences();

-- Comentários para documentação
COMMENT ON TABLE preferencias_notificacao IS 'Preferências de notificação dos usuários para email, SMS e push';
COMMENT ON TABLE device_tokens IS 'Tokens de dispositivos para notificações push';

COMMENT ON COLUMN preferencias_notificacao.tipos_habilitados IS 'Array JSON com tipos de notificação habilitados para o usuário';
COMMENT ON COLUMN device_tokens.platform IS 'Plataforma do dispositivo: web, android ou ios';
COMMENT ON COLUMN device_tokens.active IS 'Se o token está ativo (dispositivo ainda instalado/logado)';
