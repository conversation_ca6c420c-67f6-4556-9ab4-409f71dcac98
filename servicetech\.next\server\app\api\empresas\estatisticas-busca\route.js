/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/empresas/estatisticas-busca/route";
exports.ids = ["app/api/empresas/estatisticas-busca/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&page=%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Festatisticas-busca%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&page=%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Festatisticas-busca%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projetos_1_geremias_servicetech_src_app_api_empresas_estatisticas_busca_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/empresas/estatisticas-busca/route.ts */ \"(rsc)/./src/app/api/empresas/estatisticas-busca/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/empresas/estatisticas-busca/route\",\n        pathname: \"/api/empresas/estatisticas-busca\",\n        filename: \"route\",\n        bundlePath: \"app/api/empresas/estatisticas-busca/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\api\\\\empresas\\\\estatisticas-busca\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projetos_1_geremias_servicetech_src_app_api_empresas_estatisticas_busca_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&page=%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Festatisticas-busca%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/empresas/estatisticas-busca/route.ts":
/*!**********************************************************!*\
  !*** ./src/app/api/empresas/estatisticas-busca/route.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n\n\nasync function GET() {\n    try {\n        // Usar cliente administrativo para bypassa RLS e evitar recursão infinita\n        const supabase = (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createAdminClient)();\n        // Buscar estatísticas gerais das empresas ativas\n        const { data: empresas, error: empresasError } = await supabase.from('empresas').select('empresa_id, cidade, estado').eq('status', 'ativo');\n        if (empresasError) {\n            console.error('Erro ao buscar empresas para estatísticas:', empresasError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao buscar estatísticas'\n            }, {\n                status: 500\n            });\n        }\n        // Buscar categorias e preços dos serviços ativos\n        const { data: servicos, error: servicosError } = await supabase.from('servicos').select(`\n        categoria,\n        preco,\n        empresas!inner (\n          empresa_id,\n          status\n        )\n      `).eq('ativo', true).eq('empresas.status', 'ativo');\n        if (servicosError) {\n            console.error('Erro ao buscar serviços para estatísticas:', servicosError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao buscar estatísticas de serviços'\n            }, {\n                status: 500\n            });\n        }\n        // Processar dados\n        const cidadesSet = new Set();\n        const estadosSet = new Set();\n        const categoriasSet = new Set();\n        const precos = [];\n        // Processar empresas\n        empresas?.forEach((empresa)=>{\n            if (empresa.cidade) cidadesSet.add(empresa.cidade);\n            if (empresa.estado) estadosSet.add(empresa.estado);\n        });\n        // Processar serviços\n        servicos?.forEach((servico)=>{\n            if (servico.categoria) categoriasSet.add(servico.categoria);\n            if (servico.preco) precos.push(servico.preco);\n        });\n        // Calcular faixa de preços\n        const precoMinimo = precos.length > 0 ? Math.min(...precos) : 0;\n        const precoMaximo = precos.length > 0 ? Math.max(...precos) : 0;\n        // Montar resposta\n        const estatisticas = {\n            total_empresas: empresas?.length || 0,\n            cidades_disponiveis: Array.from(cidadesSet).sort(),\n            estados_disponiveis: Array.from(estadosSet).sort(),\n            categorias_disponiveis: Array.from(categoriasSet).sort(),\n            faixa_precos: {\n                minimo: precoMinimo,\n                maximo: precoMaximo\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: estatisticas\n        });\n    } catch (error) {\n        console.error('Erro geral na API de estatísticas de busca:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/empresas/estatisticas-busca/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Cliente administrativo para operações que requerem service role\nfunction createAdminClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // No-op for admin client\n            }\n        },\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc3VwYWJhc2Uvc2VydmVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDWjtBQUUvQixlQUFlRTtJQUNwQixNQUFNQyxjQUFjLE1BQU1GLHFEQUFPQTtJQUVqQyxPQUFPRCxpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLFFBQVFDLEdBQUcsQ0FBQ0UseUJBQXlCLEVBQ3JDO1FBQ0VOLFNBQVM7WUFDUE87Z0JBQ0UsT0FBT0wsWUFBWUssTUFBTTtZQUMzQjtZQUNBQyxRQUFPQyxZQUFZO2dCQUNqQixJQUFJO29CQUNGQSxhQUFhQyxPQUFPLENBQUMsQ0FBQyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFLEdBQzVDWCxZQUFZWSxHQUFHLENBQUNILE1BQU1DLE9BQU9DO2dCQUVqQyxFQUFFLE9BQU07Z0JBQ04sMERBQTBEO2dCQUMxRCx3REFBd0Q7Z0JBQ3hELGlCQUFpQjtnQkFDbkI7WUFDRjtRQUNGO0lBQ0Y7QUFFSjtBQUVBLGtFQUFrRTtBQUMzRCxTQUFTRTtJQUNkLE9BQU9oQixpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLFFBQVFDLEdBQUcsQ0FBQ0UseUJBQXlCLEVBQ3JDO1FBQ0VOLFNBQVM7WUFDUE87Z0JBQ0UsT0FBTyxFQUFFO1lBQ1g7WUFDQUM7WUFDRSx5QkFBeUI7WUFDM0I7UUFDRjtRQUNBUSxNQUFNO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7QUFFSiIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXHNyY1xcdXRpbHNcXHN1cGFiYXNlXFxzZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2VydmVyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3NzcidcclxuaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycydcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVDbGllbnQoKSB7XHJcbiAgY29uc3QgY29va2llU3RvcmUgPSBhd2FpdCBjb29raWVzKClcclxuXHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZISxcclxuICAgIHtcclxuICAgICAgY29va2llczoge1xyXG4gICAgICAgIGdldEFsbCgpIHtcclxuICAgICAgICAgIHJldHVybiBjb29raWVTdG9yZS5nZXRBbGwoKVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgc2V0QWxsKGNvb2tpZXNUb1NldCkge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29va2llc1RvU2V0LmZvckVhY2goKHsgbmFtZSwgdmFsdWUsIG9wdGlvbnMgfSkgPT5cclxuICAgICAgICAgICAgICBjb29raWVTdG9yZS5zZXQobmFtZSwgdmFsdWUsIG9wdGlvbnMpXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgIH0gY2F0Y2gge1xyXG4gICAgICAgICAgICAvLyBUaGUgYHNldEFsbGAgbWV0aG9kIHdhcyBjYWxsZWQgZnJvbSBhIFNlcnZlciBDb21wb25lbnQuXHJcbiAgICAgICAgICAgIC8vIFRoaXMgY2FuIGJlIGlnbm9yZWQgaWYgeW91IGhhdmUgbWlkZGxld2FyZSByZWZyZXNoaW5nXHJcbiAgICAgICAgICAgIC8vIHVzZXIgc2Vzc2lvbnMuXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH1cclxuICApXHJcbn1cclxuXHJcbi8vIENsaWVudGUgYWRtaW5pc3RyYXRpdm8gcGFyYSBvcGVyYcOnw7VlcyBxdWUgcmVxdWVyZW0gc2VydmljZSByb2xlXHJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVBZG1pbkNsaWVudCgpIHtcclxuICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50KFxyXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcclxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxyXG4gICAge1xyXG4gICAgICBjb29raWVzOiB7XHJcbiAgICAgICAgZ2V0QWxsKCkge1xyXG4gICAgICAgICAgcmV0dXJuIFtdXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzZXRBbGwoKSB7XHJcbiAgICAgICAgICAvLyBOby1vcCBmb3IgYWRtaW4gY2xpZW50XHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgICAgYXV0aDoge1xyXG4gICAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGZhbHNlLFxyXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlQ2xpZW50IiwiY29va2llU3RvcmUiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsImdldEFsbCIsInNldEFsbCIsImNvb2tpZXNUb1NldCIsImZvckVhY2giLCJuYW1lIiwidmFsdWUiLCJvcHRpb25zIiwic2V0IiwiY3JlYXRlQWRtaW5DbGllbnQiLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&page=%2Fapi%2Fempresas%2Festatisticas-busca%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Festatisticas-busca%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();