'use client';
import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { createClient } from '@/utils/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

// Carregando o Stripe com a chave publicável
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY as string);

// Componente do formulário de checkout
function CheckoutForm({ plano }: { plano: string }) {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);
    setErrorMessage(null);

    // Confirmar o pagamento com o Stripe.js
    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `${window.location.origin}/pagamento-sucesso`,
      },
    });

    if (error) {
      setErrorMessage(error.message ?? 'Ocorreu um erro ao processar o pagamento.');
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="p-4 border border-gray-200 rounded-lg">
        <PaymentElement />
      </div>

      {errorMessage && (
        <div className="p-4 bg-red-50 text-red-700 rounded-lg">
          {errorMessage}
        </div>
      )}

      <button
        type="submit"
        disabled={!stripe || loading}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
      >
        {loading ? 'Processando...' : `Pagar ${plano === 'essencial' ? 'R$ 99,00' : 'R$ 199,00'}/mês`}
      </button>
    </form>
  );
}

// Componente principal da página de compra
function ComprarPlanoContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading: authLoading } = useAuth();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [plano, setPlano] = useState<string | null>(null);

  useEffect(() => {
    const initializePurchase = async () => {
      if (authLoading) return;

      // Verificar se o usuário está logado
      if (!user) {
        // Salvar o plano selecionado e redirecionar para login
        const planoParam = searchParams.get('plano');
        if (planoParam) {
          localStorage.setItem('plano_redirect', planoParam);
        }
        router.push('/login?redirect=/comprar-plano');
        return;
      }

      // Obter o plano da URL ou do localStorage
      let planoSelecionado = searchParams.get('plano');
      if (!planoSelecionado) {
        planoSelecionado = localStorage.getItem('plano_redirect');
        localStorage.removeItem('plano_redirect');
      }

      if (!planoSelecionado || !['essencial', 'premium'].includes(planoSelecionado)) {
        router.push('/planos');
        return;
      }

      setPlano(planoSelecionado);

      // Verificar se o usuário já tem pagamento confirmado
      const supabase = createClient();
      const { data: { user: authUser } } = await supabase.auth.getUser();

      if (authUser?.user_metadata?.pagamento_confirmado) {
        // Usuário já tem pagamento confirmado, redirecionar para onboarding
        router.push('/onboarding');
        return;
      }

      // Criar intent de pagamento
      try {
        const response = await fetch('/api/create-payment-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            plano: planoSelecionado,
            user_id: user.id,
          }),
        });

        if (!response.ok) {
          throw new Error('Falha ao criar intent de pagamento');
        }

        const data = await response.json();
        setClientSecret(data.clientSecret);
      } catch (err) {
        setError('Ocorreu um erro ao preparar o pagamento. Por favor, tente novamente.');
        console.error('Erro ao criar payment intent:', err);
      } finally {
        setLoading(false);
      }
    };

    initializePurchase();
  }, [user, authLoading, searchParams, router]);

  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#3b82f6',
    },
  };

  const options = {
    clientSecret: clientSecret || undefined,
    appearance,
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Preparando pagamento...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center text-gray-800 mb-2">
            Finalizar Compra
          </h1>
          <p className="text-gray-600 text-center mb-8">
            Complete o pagamento para ativar sua assinatura
          </p>

          {/* Resumo do Plano */}
          {plano && (
            <div className="mb-8 p-6 bg-blue-50 rounded-lg">
              <h2 className="text-xl font-semibold text-blue-800 mb-4">
                Plano {plano === 'essencial' ? 'Essencial' : 'Premium'}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                <div>
                  <strong>Valor mensal:</strong> {plano === 'essencial' ? 'R$ 99,00' : 'R$ 199,00'}
                </div>
                <div>
                  <strong>Serviços:</strong> Até {plano === 'essencial' ? '6' : '12'} serviços
                </div>
                <div>
                  <strong>Colaboradores:</strong> Até {plano === 'essencial' ? '2' : '6'} colaboradores extras
                </div>
                <div>
                  <strong>Relatórios:</strong> {plano === 'essencial' ? 'Básicos' : 'Completos'}
                </div>
              </div>
            </div>
          )}

          {error ? (
            <div className="p-6 bg-red-50 text-red-700 rounded-lg text-center">
              <p className="font-medium mb-4">{error}</p>
              <Link href="/planos" className="text-blue-600 hover:text-blue-800 underline">
                Voltar para planos
              </Link>
            </div>
          ) : clientSecret && plano ? (
            <Elements options={options} stripe={stripePromise}>
              <CheckoutForm plano={plano} />
            </Elements>
          ) : null}

          {/* Informações de Segurança */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-center text-sm text-gray-600">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              Pagamento seguro processado pelo Stripe
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ComprarPlanoPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-600">Preparando pagamento...</p>
        </div>
      </div>
    }>
      <ComprarPlanoContent />
    </Suspense>
  );
}
