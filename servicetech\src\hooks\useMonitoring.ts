/**
 * Hook para monitoramento de logs e métricas
 */

import { useState, useEffect, useCallback } from 'react';
import { logger, LogLevel } from '@/services/LoggingService';

// Re-export LogLevel para uso em outros componentes
export { LogLevel };

export interface LogEntry {
  id: string;
  level: LogLevel;
  message: string;
  timestamp: string;
  userId?: string;
  context?: Record<string, any>;
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
}

export interface LogFilters {
  level?: LogLevel;
  startDate?: string;
  endDate?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface LogStats {
  total: number;
  byLevel: Record<LogLevel, number>;
  byRiskLevel: Record<string, number>;
}

export interface PerformanceMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  requests: {
    total: number;
    errors: number;
    averageResponseTime: number;
  };
  database: {
    connections: number;
    queryTime: number;
  };
}

export interface MonitoringData {
  logs: LogEntry[];
  stats: LogStats;
  metrics: PerformanceMetrics;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export function useMonitoring() {
  const [data, setData] = useState<MonitoringData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Buscar logs
  const fetchLogs = useCallback(async (filters: LogFilters = {}) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (filters.level) params.append('level', filters.level);
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.search) params.append('search', filters.search);
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.offset) params.append('offset', filters.offset.toString());

      const response = await fetch(`/api/monitoring/logs?${params}`);
      
      if (!response.ok) {
        throw new Error('Erro ao buscar logs');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error ?? 'Erro desconhecido');
      }

      setData(prevData => ({
        ...prevData,
        logs: result.data.logs,
        stats: result.data.stats,
        pagination: result.data.pagination,
        metrics: prevData?.metrics || {} as PerformanceMetrics
      }));

    } catch (err: any) {
      setError(err.message);
      logger.error('Erro ao buscar logs', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Buscar métricas
  const fetchMetrics = useCallback(async (type?: string, startDate?: string, endDate?: string) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      
      if (type) params.append('type', type);
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await fetch(`/api/monitoring/metrics?${params}`);
      
      if (!response.ok) {
        throw new Error('Erro ao buscar métricas');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error ?? 'Erro desconhecido');
      }

      setData(prevData => ({
        ...prevData,
        metrics: result.data.systemMetrics,
        logs: prevData?.logs ?? [],
        stats: prevData?.stats ?? {} as LogStats,
        pagination: prevData?.pagination ?? { total: 0, limit: 100, offset: 0, hasMore: false }
      }));

    } catch (err: any) {
      setError(err.message);
      logger.error('Erro ao buscar métricas', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Criar log manual
  const createManualLog = useCallback(async (level: LogLevel, message: string, context?: Record<string, any>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/monitoring/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'manual_log',
          level,
          message,
          context
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao criar log manual');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error ?? 'Erro desconhecido');
      }

      // Recarregar logs após criar
      await fetchLogs();

      return result;

    } catch (err: any) {
      setError(err.message);
      logger.error('Erro ao criar log manual', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchLogs]);

  // Testar sistema de logging
  const testLogging = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/monitoring/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'test_logging'
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao testar logging');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error ?? 'Erro desconhecido');
      }

      // Recarregar logs após teste
      await fetchLogs();

      return result;

    } catch (err: any) {
      setError(err.message);
      logger.error('Erro ao testar logging', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchLogs]);

  // Exportar logs
  const exportLogs = useCallback(async (format: string = 'json', filters: LogFilters = {}) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/monitoring/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'export_logs',
          format,
          filters
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao exportar logs');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error ?? 'Erro desconhecido');
      }

      return result.data;

    } catch (err: any) {
      setError(err.message);
      logger.error('Erro ao exportar logs', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Registrar métrica personalizada
  const recordMetric = useCallback(async (name: string, value: number, unit?: string, tags?: Record<string, string>) => {
    try {
      const response = await fetch('/api/monitoring/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'record_metric',
          name,
          value,
          unit,
          tags
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao registrar métrica');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error ?? 'Erro desconhecido');
      }

      return result;

    } catch (err: any) {
      logger.error('Erro ao registrar métrica', err);
      throw err;
    }
  }, []);

  // Carregar dados iniciais
  useEffect(() => {
    fetchLogs();
    fetchMetrics();
  }, [fetchLogs, fetchMetrics]);

  // Auto-refresh das métricas a cada 30 segundos
  useEffect(() => {
    const interval = setInterval(() => {
      fetchMetrics();
    }, 30000);

    return () => clearInterval(interval);
  }, [fetchMetrics]);

  return {
    data,
    loading,
    error,
    fetchLogs,
    fetchMetrics,
    createManualLog,
    testLogging,
    exportLogs,
    recordMetric,
    refresh: () => {
      fetchLogs();
      fetchMetrics();
    }
  };
}
