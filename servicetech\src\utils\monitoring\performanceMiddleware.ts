/**
 * Middleware para captura automática de métricas de performance
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/services/LoggingService';

interface RequestMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  statusCode?: number;
  endpoint: string;
  method: string;
  userAgent?: string;
  ip?: string;
  userId?: string;
}

// Armazenamento em memória para métricas (em produção, usar Redis)
const activeRequests = new Map<string, RequestMetrics>();
const completedRequests: RequestMetrics[] = [];

/**
 * Gera ID único para requisição
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Extrai IP do cliente
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

/**
 * Middleware para capturar métricas de performance
 */
export async function withPerformanceMonitoring(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
): Promise<NextResponse> {
  const requestId = generateRequestId();
  const startTime = Date.now();
  
  // Criar métrica inicial
  const metrics: RequestMetrics = {
    startTime,
    endpoint: new URL(request.url).pathname,
    method: request.method,
    userAgent: request.headers.get('user-agent') ?? undefined,
    ip: getClientIP(request)
  };
  
  // Armazenar requisição ativa
  activeRequests.set(requestId, metrics);
  
  try {
    // Executar handler
    const response = await handler(request);
    
    // Capturar métricas finais
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    metrics.endTime = endTime;
    metrics.duration = duration;
    metrics.statusCode = response.status;
    
    // Remover da lista de ativas e adicionar às completas
    activeRequests.delete(requestId);
    completedRequests.push(metrics);
    
    // Manter apenas as últimas 1000 requisições
    if (completedRequests.length > 1000) {
      completedRequests.shift();
    }
    
    // Log de performance
    const logLevel = response.status >= 500 ? 'error' :
                    response.status >= 400 ? 'warn' : 'info';

    const logMethod = logger[logLevel as keyof typeof logger] as any;
    if (logLevel === 'error') {
      logMethod(`API Request ${response.status}`, undefined, {
        requestId,
        endpoint: metrics.endpoint,
        method: metrics.method,
        duration,
        statusCode: response.status,
        userAgent: metrics.userAgent,
        ip: metrics.ip
      });
    } else {
      logMethod(`API Request ${response.status}`, {
        requestId,
        endpoint: metrics.endpoint,
        method: metrics.method,
        duration,
        statusCode: response.status,
        userAgent: metrics.userAgent,
        ip: metrics.ip
      });
    }
    
    // Enviar métrica para API de monitoramento se duração for alta
    if (duration > 5000) { // Mais de 5 segundos
      logger.warn('Slow API request detected', {
        requestId,
        endpoint: metrics.endpoint,
        duration,
        statusCode: response.status
      });
      
      // Registrar métrica de performance lenta
      try {
        await fetch('/api/monitoring/metrics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'record_metric',
            name: 'slow_request',
            value: duration,
            unit: 'ms',
            tags: {
              endpoint: metrics.endpoint,
              method: metrics.method,
              statusCode: response.status.toString()
            }
          }),
        }).catch(() => {
          // Ignorar erros de registro de métrica
        });
      } catch (error) {
        // Ignorar erros de registro de métrica
      }
    }
    
    return response;
    
  } catch (error: any) {
    // Capturar erro
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    metrics.endTime = endTime;
    metrics.duration = duration;
    metrics.statusCode = 500;
    
    // Remover da lista de ativas e adicionar às completas
    activeRequests.delete(requestId);
    completedRequests.push(metrics);
    
    // Log de erro
    logger.error('API Request Error', error, {
      requestId,
      endpoint: metrics.endpoint,
      method: metrics.method,
      duration,
      userAgent: metrics.userAgent,
      ip: metrics.ip
    });
    
    // Re-throw o erro
    throw error;
  }
}

/**
 * Obter estatísticas de performance
 */
export function getPerformanceStats() {
  const now = Date.now();
  const last24h = now - (24 * 60 * 60 * 1000);
  const last1h = now - (60 * 60 * 1000);
  
  // Filtrar requisições das últimas 24h
  const recent24h = completedRequests.filter(req => 
    req.endTime && req.endTime > last24h
  );
  
  // Filtrar requisições da última hora
  const recent1h = completedRequests.filter(req => 
    req.endTime && req.endTime > last1h
  );
  
  // Calcular estatísticas
  const calculateStats = (requests: RequestMetrics[]) => {
    if (requests.length === 0) {
      return {
        total: 0,
        errors: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        p95Duration: 0,
        errorRate: 0
      };
    }
    
    const durations = requests.map(r => r.duration || 0).sort((a, b) => a - b);
    const errors = requests.filter(r => r.statusCode && r.statusCode >= 400);
    
    const p95Index = Math.floor(durations.length * 0.95);
    
    return {
      total: requests.length,
      errors: errors.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      minDuration: durations[0] || 0,
      maxDuration: durations[durations.length - 1] || 0,
      p95Duration: durations[p95Index] || 0,
      errorRate: (errors.length / requests.length) * 100
    };
  };
  
  return {
    activeRequests: activeRequests.size,
    last24h: calculateStats(recent24h),
    last1h: calculateStats(recent1h),
    byEndpoint: getStatsByEndpoint(recent24h),
    slowestRequests: getSlowRequests(recent24h, 10)
  };
}

/**
 * Obter estatísticas por endpoint
 */
function getStatsByEndpoint(requests: RequestMetrics[]) {
  const byEndpoint: Record<string, RequestMetrics[]> = {};
  
  requests.forEach(req => {
    if (!byEndpoint[req.endpoint]) {
      byEndpoint[req.endpoint] = [];
    }
    byEndpoint[req.endpoint].push(req);
  });
  
  const stats: Record<string, any> = {};
  
  Object.entries(byEndpoint).forEach(([endpoint, endpointRequests]) => {
    const durations = endpointRequests.map(r => r.duration || 0);
    const errors = endpointRequests.filter(r => r.statusCode && r.statusCode >= 400);
    
    stats[endpoint] = {
      total: endpointRequests.length,
      errors: errors.length,
      averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
      errorRate: (errors.length / endpointRequests.length) * 100
    };
  });
  
  return stats;
}

/**
 * Obter requisições mais lentas
 */
function getSlowRequests(requests: RequestMetrics[], limit: number = 10) {
  return requests
    .filter(req => req.duration)
    .sort((a, b) => (b.duration || 0) - (a.duration || 0))
    .slice(0, limit)
    .map(req => ({
      endpoint: req.endpoint,
      method: req.method,
      duration: req.duration,
      statusCode: req.statusCode,
      timestamp: req.endTime
    }));
}

/**
 * Limpar dados antigos (executar periodicamente)
 */
export function cleanupOldMetrics() {
  const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 dias
  
  // Remover requisições antigas
  const filtered = completedRequests.filter(req => 
    req.endTime && req.endTime > cutoff
  );
  
  completedRequests.length = 0;
  completedRequests.push(...filtered);
  
  logger.info('Performance metrics cleanup completed', {
    removed: completedRequests.length - filtered.length,
    remaining: filtered.length
  });
}

// Executar limpeza a cada 6 horas
if (typeof window === 'undefined') {
  setInterval(cleanupOldMetrics, 6 * 60 * 60 * 1000);
}
