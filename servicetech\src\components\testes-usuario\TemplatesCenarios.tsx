'use client';

import { useState, useEffect } from 'react';
import { useTestesUsuario } from '@/hooks/useTestesUsuario';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

export function TemplatesCenarios() {
  const { buscarTemplatesCenarios, criarCenarioDeTemplate, loading, error } = useTestesUsuario();
  const [templates, setTemplates] = useState<any[]>([]);
  const [templateSelecionado, setTemplateSelecionado] = useState<any>(null);

  useEffect(() => {
    carregarTemplates();
  }, []);

  const carregarTemplates = async () => {
    try {
      const response = await buscarTemplatesCenarios();
      if (response.success) {
        setTemplates(response.data || []);
      }
    } catch (error) {
      console.error('Erro ao carregar templates:', error);
    }
  };

  const handleCriarDeTemplate = async (templateId: number) => {
    try {
      await criarCenarioDeTemplate(templateId);
      // Mostrar sucesso
    } catch (error) {
      console.error('Erro ao criar cenário do template:', error);
    }
  };

  const getDificuldadeColor = (dificuldade: string) => {
    switch (dificuldade) {
      case 'Facil': return 'text-green-600 bg-green-100';
      case 'Medio': return 'text-yellow-600 bg-yellow-100';
      case 'Dificil': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoriaColor = (categoria: string) => {
    switch (categoria) {
      case 'Onboarding': return 'text-blue-600 bg-blue-100';
      case 'Agendamento': return 'text-purple-600 bg-purple-100';
      case 'Gestao': return 'text-indigo-600 bg-indigo-100';
      case 'Pagamento': return 'text-green-600 bg-green-100';
      case 'Navegacao': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Templates mockados para demonstração
  const templatesMock = [
    {
      template_id: 1,
      nome_template: 'Primeiro Agendamento - Cliente',
      descricao: 'Template para testar o primeiro agendamento de um cliente novo na plataforma',
      papel_usuario: 'Cliente',
      categoria: 'Agendamento',
      dificuldade: 'Medio',
      tempo_estimado_minutos: 25,
      uso_count: 15,
      passos_template: [
        { descricao: 'Acessar a página de agendamento', acao_esperada: 'Clicar no botão Agendar Serviço', resultado_esperado: 'Página de agendamento carrega corretamente' },
        { descricao: 'Selecionar estabelecimento', acao_esperada: 'Escolher um estabelecimento da lista', resultado_esperado: 'Estabelecimento é selecionado e serviços aparecem' },
        { descricao: 'Escolher serviço', acao_esperada: 'Selecionar um serviço disponível', resultado_esperado: 'Serviço é selecionado e horários aparecem' },
        { descricao: 'Selecionar data e horário', acao_esperada: 'Escolher data e horário disponível', resultado_esperado: 'Data e horário são selecionados' },
        { descricao: 'Confirmar agendamento', acao_esperada: 'Clicar em confirmar agendamento', resultado_esperado: 'Agendamento é criado com sucesso' }
      ]
    },
    {
      template_id: 2,
      nome_template: 'Onboarding Proprietário',
      descricao: 'Template para testar o processo de onboarding completo de proprietários de estabelecimento',
      papel_usuario: 'Proprietario',
      categoria: 'Onboarding',
      dificuldade: 'Medio',
      tempo_estimado_minutos: 45,
      uso_count: 8,
      passos_template: [
        { descricao: 'Completar cadastro inicial', acao_esperada: 'Preencher dados da empresa', resultado_esperado: 'Dados são salvos corretamente' },
        { descricao: 'Configurar serviços', acao_esperada: 'Adicionar pelo menos um serviço', resultado_esperado: 'Serviço é criado e aparece na lista' },
        { descricao: 'Definir horários de funcionamento', acao_esperada: 'Configurar horários da semana', resultado_esperado: 'Horários são salvos' },
        { descricao: 'Configurar colaboradores', acao_esperada: 'Adicionar pelo menos um colaborador', resultado_esperado: 'Colaborador é adicionado' },
        { descricao: 'Finalizar configuração', acao_esperada: 'Completar o onboarding', resultado_esperado: 'Dashboard principal é exibido' }
      ]
    },
    {
      template_id: 3,
      nome_template: 'Navegação Geral',
      descricao: 'Template para testar a navegação geral e usabilidade básica da plataforma',
      papel_usuario: 'Cliente',
      categoria: 'Navegacao',
      dificuldade: 'Facil',
      tempo_estimado_minutos: 15,
      uso_count: 22,
      passos_template: [
        { descricao: 'Explorar menu principal', acao_esperada: 'Navegar pelos itens do menu', resultado_esperado: 'Todas as páginas carregam corretamente' },
        { descricao: 'Usar busca', acao_esperada: 'Pesquisar por estabelecimentos', resultado_esperado: 'Resultados relevantes são exibidos' },
        { descricao: 'Acessar perfil', acao_esperada: 'Ir para página de perfil', resultado_esperado: 'Dados do usuário são exibidos' },
        { descricao: 'Ver histórico', acao_esperada: 'Acessar histórico de agendamentos', resultado_esperado: 'Lista de agendamentos é exibida' }
      ]
    },
    {
      template_id: 4,
      nome_template: 'Gestão de Agendamentos - Proprietário',
      descricao: 'Template para testar funcionalidades de gestão de agendamentos pelo proprietário',
      papel_usuario: 'Proprietario',
      categoria: 'Gestao',
      dificuldade: 'Medio',
      tempo_estimado_minutos: 30,
      uso_count: 5,
      passos_template: [
        { descricao: 'Acessar dashboard de agendamentos', acao_esperada: 'Navegar para área de gestão', resultado_esperado: 'Lista de agendamentos é exibida' },
        { descricao: 'Filtrar agendamentos', acao_esperada: 'Aplicar filtros por data/status', resultado_esperado: 'Resultados são filtrados corretamente' },
        { descricao: 'Visualizar detalhes', acao_esperada: 'Clicar em um agendamento', resultado_esperado: 'Detalhes completos são exibidos' },
        { descricao: 'Alterar status', acao_esperada: 'Confirmar ou cancelar agendamento', resultado_esperado: 'Status é atualizado' }
      ]
    }
  ];

  if (templateSelecionado) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900">
            Detalhes do Template
          </h2>
          <Button
            onClick={() => setTemplateSelecionado(null)}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700"
          >
            Voltar
          </Button>
        </div>

        {/* Detalhes do Template */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {templateSelecionado.nome_template}
                </h3>
                <div className="flex flex-wrap gap-2 mb-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoriaColor(templateSelecionado.categoria)}`}>
                    {templateSelecionado.categoria}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDificuldadeColor(templateSelecionado.dificuldade)}`}>
                    {templateSelecionado.dificuldade}
                  </span>
                  <span className="px-3 py-1 rounded-full text-sm font-medium text-gray-600 bg-gray-100">
                    {templateSelecionado.papel_usuario}
                  </span>
                  <span className="px-3 py-1 rounded-full text-sm font-medium text-blue-600 bg-blue-100">
                    {templateSelecionado.tempo_estimado_minutos} min
                  </span>
                </div>
              </div>
              <Button
                onClick={() => handleCriarDeTemplate(templateSelecionado.template_id)}
                className="bg-blue-600 hover:bg-blue-700"
                disabled={loading}
              >
                {loading ? 'Criando...' : 'Criar Cenário'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Descrição</h4>
                <p className="text-gray-600">{templateSelecionado.descricao}</p>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-3">
                  Passos do Template ({templateSelecionado.passos_template.length})
                </h4>
                <div className="space-y-3">
                  {templateSelecionado.passos_template.map((passo: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-blue-600 font-medium text-sm">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 mb-1">{passo.descricao}</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="font-medium text-gray-700">Ação esperada:</span>
                              <p className="text-gray-600">{passo.acao_esperada}</p>
                            </div>
                            <div>
                              <span className="font-medium text-gray-700">Resultado esperado:</span>
                              <p className="text-gray-600">{passo.resultado_esperado}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Estatísticas de Uso</h4>
                <p className="text-sm text-gray-600">
                  Este template foi usado <span className="font-medium">{templateSelecionado.uso_count} vezes</span> para criar cenários de teste.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Templates de Cenários
          </h2>
          <p className="text-gray-600 mt-1">
            Use templates predefinidos para criar cenários de teste rapidamente
          </p>
        </div>
      </div>

      {/* Informações sobre Templates */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">💡 Como usar os Templates</h3>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Vantagens dos Templates</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Criação rápida de cenários testados
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Baseados em melhores práticas de UX
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Cobertura de fluxos principais
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Métricas pré-configuradas
                </li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Como Personalizar</h4>
              <ul className="space-y-1 text-sm text-gray-600">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Ajuste passos conforme sua interface
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Modifique critérios de sucesso
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Adapte métricas aos seus objetivos
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Adicione observações específicas
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Templates */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {templatesMock.map((template) => (
          <Card key={template.template_id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {template.nome_template}
                  </h3>
                  <div className="flex flex-wrap gap-2 mb-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoriaColor(template.categoria)}`}>
                      {template.categoria}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDificuldadeColor(template.dificuldade)}`}>
                      {template.dificuldade}
                    </span>
                    <span className="px-2 py-1 rounded-full text-xs font-medium text-gray-600 bg-gray-100">
                      {template.papel_usuario}
                    </span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4 line-clamp-2">
                {template.descricao}
              </p>
              
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Tempo estimado:</span>
                  <p className="text-gray-600">{template.tempo_estimado_minutos} min</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Passos:</span>
                  <p className="text-gray-600">{template.passos_template.length} passos</p>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Usado {template.uso_count} vezes
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  onClick={() => setTemplateSelecionado(template)}
                  className="bg-gray-600 hover:bg-gray-700 text-sm"
                >
                  Ver Detalhes
                </Button>
                <Button
                  onClick={() => handleCriarDeTemplate(template.template_id)}
                  className="bg-blue-600 hover:bg-blue-700 text-sm"
                  disabled={loading}
                >
                  {loading ? 'Criando...' : 'Usar Template'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Erro ao processar template
              </h3>
              <div className="mt-2 text-sm text-red-700">
                {error}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
