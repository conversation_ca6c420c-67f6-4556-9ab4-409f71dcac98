import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;
    const cenarioId = parseInt(resolvedParams.id);
    if (isNaN(cenarioId)) {
      return NextResponse.json(
        { success: false, error: 'ID do cenário inválido' },
        { status: 400 }
      );
    }

    // Buscar cenário
    const { data: cenario, error } = await supabase
      .from('cenarios_teste')
      .select('*')
      .eq('cenario_id', cenarioId)
      .single();

    if (error) {
      console.error('Erro ao buscar cenário:', error);
      return NextResponse.json(
        { success: false, error: 'Cenário não encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: cenario
    });

  } catch (error: any) {
    console.error('Erro na API de cenário:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const { data: userData } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', user.id)
      .single();

    const userRole = userData?.raw_user_meta_data?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas administradores podem editar cenários.' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const cenarioId = parseInt(resolvedParams.id);
    if (isNaN(cenarioId)) {
      return NextResponse.json(
        { success: false, error: 'ID do cenário inválido' },
        { status: 400 }
      );
    }

    const body = await request.json();
    
    // Validar dados se fornecidos
    if (body.papel_usuario) {
      const papeisValidos = ['Administrador', 'Proprietario', 'Colaborador', 'Cliente'];
      if (!papeisValidos.includes(body.papel_usuario)) {
        return NextResponse.json(
          { success: false, error: 'Papel de usuário inválido' },
          { status: 400 }
        );
      }
    }

    if (body.categoria) {
      const categoriasValidas = ['Onboarding', 'Agendamento', 'Gestao', 'Pagamento', 'Navegacao'];
      if (!categoriasValidas.includes(body.categoria)) {
        return NextResponse.json(
          { success: false, error: 'Categoria inválida' },
          { status: 400 }
        );
      }
    }

    if (body.dificuldade) {
      const dificuldadesValidas = ['Facil', 'Medio', 'Dificil'];
      if (!dificuldadesValidas.includes(body.dificuldade)) {
        return NextResponse.json(
          { success: false, error: 'Dificuldade inválida' },
          { status: 400 }
        );
      }
    }

    // Se passos foram fornecidos, numerá-los
    if (body.passos && Array.isArray(body.passos)) {
      body.passos = body.passos.map((passo: any, index: number) => ({
        ...passo,
        passo_numero: index + 1
      }));
    }

    // Atualizar cenário
    const { data: cenarioAtualizado, error } = await supabase
      .from('cenarios_teste')
      .update({
        ...body,
        updated_at: new Date().toISOString()
      })
      .eq('cenario_id', cenarioId)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar cenário:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao atualizar cenário' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: cenarioAtualizado,
      message: 'Cenário atualizado com sucesso'
    });

  } catch (error: any) {
    console.error('Erro na API de atualização de cenário:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Não autorizado' },
        { status: 401 }
      );
    }

    // Verificar se é administrador
    const { data: userData } = await supabase
      .from('auth.users')
      .select('raw_user_meta_data')
      .eq('id', user.id)
      .single();

    const userRole = userData?.raw_user_meta_data?.role;
    if (userRole !== 'Administrador') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas administradores podem excluir cenários.' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const cenarioId = parseInt(resolvedParams.id);
    if (isNaN(cenarioId)) {
      return NextResponse.json(
        { success: false, error: 'ID do cenário inválido' },
        { status: 400 }
      );
    }

    // Verificar se o cenário está sendo usado em alguma sessão
    const { data: sessoes, error: sessaoError } = await supabase
      .from('sessoes_teste')
      .select('sessao_id')
      .contains('cenarios_incluidos', [cenarioId])
      .limit(1);

    if (sessaoError) {
      console.error('Erro ao verificar uso do cenário:', sessaoError);
      return NextResponse.json(
        { success: false, error: 'Erro ao verificar uso do cenário' },
        { status: 500 }
      );
    }

    if (sessoes && sessoes.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Não é possível excluir cenário que está sendo usado em sessões de teste' },
        { status: 400 }
      );
    }

    // Excluir cenário
    const { error } = await supabase
      .from('cenarios_teste')
      .delete()
      .eq('cenario_id', cenarioId);

    if (error) {
      console.error('Erro ao excluir cenário:', error);
      return NextResponse.json(
        { success: false, error: 'Erro ao excluir cenário' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Cenário excluído com sucesso'
    });

  } catch (error: any) {
    console.error('Erro na API de exclusão de cenário:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
