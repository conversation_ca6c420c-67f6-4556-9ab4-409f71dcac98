'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { PLANOS, getPlanoDetalhes } from '@/constants/planos';
import { PlanoTipo } from '@/types/onboarding';
import { salvarPlanoSelecionado } from '@/utils/onboarding';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

export default function SelecaoPlanoPage() {
  const router = useRouter();
  const [planoSelecionado, setPlanoSelecionado] = useState<PlanoTipo | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSelecionarPlano = (plano: PlanoTipo) => {
    setPlanoSelecionado(plano);
  };

  const handleContinuar = () => {
    if (!planoSelecionado) return;

    setLoading(true);
    
    // Salvar plano selecionado no localStorage
    salvarPlanoSelecionado(planoSelecionado);
    
    // Redirecionar para a próxima etapa do onboarding
    router.push('/onboarding/registro-empresa');
  };

  const planoEssencial = getPlanoDetalhes('essencial');
  const planoPremium = getPlanoDetalhes('premium');

  return (
    <div className="min-h-screen bg-[var(--background)] py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-[var(--text-primary)] mb-4">
              Escolha Seu Plano
            </h1>
            <p className="text-xl text-[var(--text-secondary)] max-w-3xl mx-auto">
              Selecione o plano ideal para o seu estabelecimento e comece a transformar a gestão dos seus agendamentos.
            </p>
          </div>

          {/* Planos */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* Plano Essencial */}
            <Card
              className={`cursor-pointer transition-all duration-300 hover:shadow-xl border-t-4 ${
                planoSelecionado === 'essencial'
                  ? 'border-[var(--primary)] ring-2 ring-[var(--primary)] bg-[var(--primary-light)]'
                  : 'border-[var(--primary)] hover:ring-1 hover:ring-[var(--primary)]'
              }`}
              onClick={() => handleSelecionarPlano('essencial')}
            >
              <div className="p-8 flex flex-col justify-between h-full">
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-3xl font-semibold text-[var(--text-primary)]">
                      {planoEssencial.nome}
                    </h2>
                    <span className="bg-[var(--primary)] text-[var(--text-on-primary)] px-3 py-1 rounded-full text-sm font-medium">
                      Mais Popular
                    </span>
                  </div>
                  
                  <p className="text-[var(--text-secondary)] mb-6">
                    {planoEssencial.descricao}
                  </p>
                  
                  <div className="text-4xl font-bold text-[var(--primary)] mb-6">
                    R$ {planoEssencial.preco.toFixed(2).replace('.', ',')}
                    <span className="text-lg font-normal text-[var(--text-secondary)]">/mês</span>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {planoEssencial.recursos.map((recurso, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                        </svg>
                        {recurso}
                      </li>
                    ))}
                  </ul>
                </div>

                {planoSelecionado === 'essencial' && (
                  <div className="mt-4 p-3 bg-[var(--primary)] text-[var(--text-on-primary)] rounded-lg text-center">
                    <span className="font-medium">✓ Plano Selecionado</span>
                  </div>
                )}
              </div>
            </Card>

            {/* Plano Premium */}
            <Card
              className={`cursor-pointer transition-all duration-300 hover:shadow-xl border-t-4 relative ${
                planoSelecionado === 'premium'
                  ? 'border-[var(--accent)] ring-2 ring-[var(--accent)] bg-[var(--accent-light)]'
                  : 'border-[var(--accent)] hover:ring-1 hover:ring-[var(--accent)]'
              }`}
              onClick={() => handleSelecionarPlano('premium')}
            >
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-[var(--accent)] text-[var(--text-on-accent)] px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                  Recomendado
                </span>
              </div>
              
              <div className="p-8 flex flex-col justify-between h-full">
                <div>
                  <h2 className="text-3xl font-semibold text-[var(--text-primary)] mb-4">
                    {planoPremium.nome}
                  </h2>
                  
                  <p className="text-[var(--text-secondary)] mb-6">
                    {planoPremium.descricao}
                  </p>
                  
                  <div className="text-4xl font-bold text-[var(--accent)] mb-6">
                    R$ {planoPremium.preco.toFixed(2).replace('.', ',')}
                    <span className="text-lg font-normal text-[var(--text-secondary)]">/mês</span>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {planoPremium.recursos.map((recurso, index) => (
                      <li key={index} className="flex items-center">
                        <svg className="w-5 h-5 text-[var(--success)] mr-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                        </svg>
                        {recurso}
                      </li>
                    ))}
                  </ul>
                </div>

                {planoSelecionado === 'premium' && (
                  <div className="mt-4 p-3 bg-[var(--accent)] text-[var(--text-on-accent)] rounded-lg text-center">
                    <span className="font-medium">✓ Plano Selecionado</span>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Comparativo Rápido */}
          <div className="bg-[var(--surface)] rounded-lg shadow-lg p-8 mb-12">
            <h3 className="text-2xl font-semibold text-[var(--text-primary)] mb-6 text-center">
              Comparativo Rápido
            </h3>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-[var(--border-color)]">
                <thead className="bg-[var(--background)]">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wider">
                      Funcionalidade
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wider">
                      Essencial
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-[var(--text-secondary)] uppercase tracking-wider">
                      Premium
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-[var(--surface)] divide-y divide-[var(--border-color)]">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">
                      Serviços Cadastrados
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      Até 6
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      Até 12
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">
                      Colaboradores Extras
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      Até 2
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      Até 6
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">
                      Relatórios
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      Básicos
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      Avançados
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">
                      Marketing
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      <span className="text-[var(--error)]">✗</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      <span className="text-[var(--success)]">✓</span>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-[var(--text-primary)]">
                      Assinatura de Serviços
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      <span className="text-[var(--error)]">✗</span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center text-[var(--text-secondary)]">
                      <span className="text-[var(--success)]">✓</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Ações */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <Link href="/planos" className="text-[var(--text-secondary)] hover:text-[var(--text-primary)] underline">
              ← Ver detalhes completos dos planos
            </Link>

            <div className="flex gap-4">
              <Button
                onClick={handleContinuar}
                disabled={!planoSelecionado || loading}
                className={`px-8 py-3 text-lg font-semibold transition-colors duration-300 ${
                  planoSelecionado === 'premium'
                    ? 'bg-[var(--accent)] text-[var(--text-on-accent)] hover:bg-[var(--accent-hover)]'
                    : 'bg-[var(--primary)] text-[var(--text-on-primary)] hover:bg-[var(--primary-hover)]'
                }`}
              >
                {loading ? 'Processando...' : 'Continuar com o Plano Selecionado'}
              </Button>
            </div>
          </div>

          {/* Informação adicional */}
          {planoSelecionado && (
            <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start">
                <svg className="w-6 h-6 text-blue-600 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                </svg>
                <div>
                  <h4 className="text-lg font-medium text-blue-800 mb-2">
                    Plano {planoSelecionado === 'essencial' ? 'Essencial' : 'Premium'} Selecionado
                  </h4>
                  <p className="text-blue-700">
                    Você poderá alterar seu plano a qualquer momento após a configuração inicial. 
                    O pagamento será processado na próxima etapa do cadastro.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
