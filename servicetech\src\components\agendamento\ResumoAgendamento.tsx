'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FluxoAgendamento } from '@/types/agendamentos';

interface ResumoAgendamentoProps {
  fluxoAgendamento: FluxoAgendamento;
  nomeEmpresa: string;
  enderecoEmpresa: string;
  onDefinirObservacoes: (observacoes: string) => void;
  onDefinirFormaPagamento: (forma: 'Online' | 'Local') => void;
  onFinalizar: () => void;
  onVoltar: () => void;
  loading?: boolean;
  aceitaPagamentoOnline?: boolean;
}

export function ResumoAgendamento({
  fluxoAgendamento,
  nomeEmpresa,
  enderecoEmpresa,
  onDefinirObservacoes,
  onDefinirFormaPagamento,
  onFinalizar,
  onVoltar,
  loading = false,
  aceitaPagamentoOnline = false
}: ResumoAgendamentoProps) {
  const [observacoes, setObservacoes] = useState(fluxoAgendamento.observacoes || '');

  const formatarPreco = (preco: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(preco);
  };

  const formatarDuracao = (minutos: number) => {
    if (minutos < 60) {
      return `${minutos}min`;
    }
    const horas = Math.floor(minutos / 60);
    const minutosRestantes = minutos % 60;
    return minutosRestantes > 0 ? `${horas}h ${minutosRestantes}min` : `${horas}h`;
  };

  const formatarDataHora = (dataHoraStr: string) => {
    const data = new Date(dataHoraStr);
    const hoje = new Date();
    const amanha = new Date();
    amanha.setDate(hoje.getDate() + 1);

    let dataFormatada = '';
    if (data.toDateString() === hoje.toDateString()) {
      dataFormatada = 'Hoje';
    } else if (data.toDateString() === amanha.toDateString()) {
      dataFormatada = 'Amanhã';
    } else {
      dataFormatada = data.toLocaleDateString('pt-BR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
      });
    }

    const horario = data.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return `${dataFormatada}, ${horario}`;
  };

  const handleObservacoesChange = (value: string) => {
    setObservacoes(value);
    onDefinirObservacoes(value);
  };

  // Se não aceita pagamento online, forçar seleção de "Local"
  useEffect(() => {
    if (!aceitaPagamentoOnline && !fluxoAgendamento.forma_pagamento) {
      onDefinirFormaPagamento('Local');
    }
  }, [aceitaPagamentoOnline, fluxoAgendamento.forma_pagamento, onDefinirFormaPagamento]);

  const podeConfirmar = fluxoAgendamento.servicos_selecionados?.length > 0 &&
                       fluxoAgendamento.horario_selecionado &&
                       fluxoAgendamento.forma_pagamento;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">
          Confirmar Agendamento
        </h3>
        <p className="text-[var(--text-secondary)] text-sm">
          Revise os detalhes do seu agendamento antes de confirmar
        </p>
      </div>

      {/* Resumo do estabelecimento */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-[var(--primary)] rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-[var(--text-primary)]">{nomeEmpresa}</h4>
              <p className="text-sm text-[var(--text-secondary)]">{enderecoEmpresa}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resumo dos serviços */}
      {fluxoAgendamento.servicos_selecionados && fluxoAgendamento.servicos_selecionados.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-[var(--secondary)] rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h4 className="font-medium text-[var(--text-primary)]">
                  Serviços Selecionados ({fluxoAgendamento.servicos_selecionados.length})
                </h4>
              </div>

              <div className="space-y-3">
                {fluxoAgendamento.servicos_selecionados.map((servico) => (
                  <div key={servico.servico_id} className="flex items-center justify-between p-3 bg-[var(--surface)] rounded-lg">
                    <div className="flex-1">
                      <h5 className="font-medium text-[var(--text-primary)]">
                        {servico.nome_servico}
                      </h5>
                      <div className="flex items-center gap-4 text-sm text-[var(--text-secondary)] mt-1">
                        <span>⏱️ {formatarDuracao(servico.duracao_minutos)}</span>
                        {servico.categoria && <span>• {servico.categoria}</span>}
                      </div>
                    </div>
                    <div className="text-right">
                      <span className="font-semibold text-[var(--primary)]">
                        {formatarPreco(servico.preco)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Resumo de valores */}
              <div className="border-t pt-3 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-[var(--text-secondary)]">Valor dos serviços:</span>
                  <span className="text-[var(--text-primary)]">
                    {formatarPreco(fluxoAgendamento.valor_original || 0)}
                  </span>
                </div>

                {fluxoAgendamento.combo_aplicado && (
                  <>
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Desconto ({fluxoAgendamento.combo_aplicado.nome_combo}):</span>
                      <span>-{formatarPreco(fluxoAgendamento.valor_desconto || 0)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-green-700 bg-green-50 p-2 rounded">
                      <span className="font-medium">Economia total:</span>
                      <span className="font-medium">
                        {formatarPreco(fluxoAgendamento.combo_aplicado.economia)}
                      </span>
                    </div>
                  </>
                )}

                <div className="flex justify-between text-base font-medium border-t pt-2">
                  <span className="text-[var(--text-primary)]">Total a pagar:</span>
                  <span className="text-[var(--primary)] text-lg">
                    {formatarPreco(fluxoAgendamento.valor_total || 0)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resumo do profissional */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 bg-[var(--info)] rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-[var(--text-primary)]">
                {fluxoAgendamento.colaborador_selecionado 
                  ? fluxoAgendamento.colaborador_selecionado.name
                  : 'Qualquer profissional disponível'
                }
              </h4>
              <p className="text-sm text-[var(--text-secondary)]">
                {fluxoAgendamento.colaborador_selecionado 
                  ? 'Agendamento específico com este profissional'
                  : 'O melhor profissional será selecionado automaticamente'
                }
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resumo do horário */}
      {fluxoAgendamento.horario_selecionado && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-[var(--success)] rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-[var(--text-primary)]">
                  {formatarDataHora(fluxoAgendamento.horario_selecionado.data_hora_inicio)}
                </h4>
                <p className="text-sm text-[var(--text-secondary)]">
                  Data e horário do agendamento
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Observações */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-[var(--text-primary)] mb-3">
            Observações (Opcional)
          </h4>
          <textarea
            value={observacoes}
            onChange={(e) => handleObservacoesChange(e.target.value)}
            placeholder="Adicione alguma observação especial para o profissional..."
            className="w-full p-3 border border-[var(--border)] rounded-lg resize-none focus:ring-2 focus:ring-[var(--primary)] focus:border-transparent"
            rows={3}
            maxLength={500}
          />
          <p className="text-xs text-[var(--text-secondary)] mt-2">
            {observacoes.length}/500 caracteres
          </p>
        </CardContent>
      </Card>

      {/* Forma de pagamento */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-[var(--text-primary)] mb-4">
            Forma de Pagamento
          </h4>
          <div className="space-y-3">
            <label className="flex items-center gap-3 p-3 border border-[var(--border)] rounded-lg cursor-pointer hover:bg-[var(--surface-hover)] transition-colors">
              <input
                type="radio"
                name="forma_pagamento"
                value="Local"
                checked={fluxoAgendamento.forma_pagamento === 'Local'}
                onChange={() => onDefinirFormaPagamento('Local')}
                className="text-[var(--primary)]"
              />
              <div className="flex-1">
                <div className="font-medium text-[var(--text-primary)]">Pagar no Local</div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Efetue o pagamento diretamente no estabelecimento
                </div>
              </div>
              <div className="text-[var(--success)] font-medium">
                {aceitaPagamentoOnline ? 'Disponível' : 'Única opção'}
              </div>
            </label>

            {aceitaPagamentoOnline ? (
              <label className="flex items-center gap-3 p-3 border border-[var(--border)] rounded-lg cursor-pointer hover:bg-[var(--surface-hover)] transition-colors">
                <input
                  type="radio"
                  name="forma_pagamento"
                  value="Online"
                  checked={fluxoAgendamento.forma_pagamento === 'Online'}
                  onChange={() => onDefinirFormaPagamento('Online')}
                  className="text-[var(--primary)]"
                />
                <div className="flex-1">
                  <div className="font-medium text-[var(--text-primary)]">Pagar Online</div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Pague agora com cartão ou Pix
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-[var(--success)] rounded-full"></div>
                  <div className="text-sm text-[var(--success)] font-medium">Disponível</div>
                </div>
              </label>
            ) : (
              <div className="p-3 border border-[var(--border)] rounded-lg bg-[var(--surface)] opacity-60">
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    disabled
                    className="text-[var(--text-secondary)]"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-[var(--text-secondary)]">Pagar Online</div>
                    <div className="text-sm text-[var(--text-secondary)]">
                      Este estabelecimento não aceita pagamentos online
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[var(--text-secondary)] rounded-full"></div>
                    <div className="text-sm text-[var(--text-secondary)] font-medium">Indisponível</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Total */}
      <Card className="bg-[var(--primary-light)] border-[var(--primary)]">
        <CardContent className="p-4">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium text-[var(--text-primary)]">Total a Pagar</h4>
              <p className="text-sm text-[var(--text-secondary)]">
                {fluxoAgendamento.forma_pagamento === 'Local' ? 'No estabelecimento' : 'Online'}
              </p>
            </div>
            <div className="text-2xl font-bold text-[var(--primary)]">
              {formatarPreco(fluxoAgendamento.valor_total || 0)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botões de ação */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          variant="outline"
          onClick={onVoltar}
          className="flex-1"
          disabled={loading}
        >
          Voltar
        </Button>
        <Button
          onClick={onFinalizar}
          className="flex-1"
          disabled={!podeConfirmar || loading}
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Confirmando...
            </div>
          ) : (
            'Confirmar Agendamento'
          )}
        </Button>
      </div>

      {/* Aviso importante */}
      <Card className="bg-[var(--warning-light)] border-[var(--warning)]">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-[var(--warning)] rounded-full flex items-center justify-center mt-0.5">
              <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-[var(--text-primary)] mb-1">
                Importante
              </h4>
              <p className="text-sm text-[var(--text-secondary)]">
                Seu agendamento ficará pendente de confirmação por até 24 horas. 
                Você receberá uma notificação quando for confirmado pelo estabelecimento.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
