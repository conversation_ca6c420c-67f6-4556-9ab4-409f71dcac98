'use client';

import { useAuth } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { TestNotifications } from '@/components/notifications/TestNotifications';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';

export default function AdminNotificationsPage() {
  const { user } = useAuth();

  return (
    <ProtectedRoute requiredRole={['Administrador']}>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-[var(--text-primary)]">
              📧 Sistema de Notificações
            </h1>
            <p className="mt-2 text-[var(--text-secondary)]">
              Painel administrativo para gerenciar e testar notificações por email
            </p>
          </div>

          {/* Informações do sistema */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-xl font-semibold text-[var(--text-primary)]">
                ℹ️ Informações do Sistema
              </h2>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="text-blue-800 font-medium">Provedor de Email</div>
                  <div className="text-blue-600 text-sm">Resend</div>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="text-green-800 font-medium">Status</div>
                  <div className="text-green-600 text-sm">
                    {process.env.NEXT_PUBLIC_RESEND_API_KEY ? '✅ Configurado' : '❌ Não configurado'}
                  </div>
                </div>
                
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="text-purple-800 font-medium">Tipos Suportados</div>
                  <div className="text-purple-600 text-sm">6 tipos de notificação</div>
                </div>
                
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <div className="text-orange-800 font-medium">Canais</div>
                  <div className="text-orange-600 text-sm">Email (SMS em breve)</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tipos de notificação */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-xl font-semibold text-[var(--text-primary)]">
                📋 Tipos de Notificação Implementados
              </h2>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="border rounded-lg p-4">
                  <div className="font-medium text-[var(--text-primary)] mb-2">
                    🆕 Novo Agendamento
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Enviado para o cliente quando um agendamento é criado
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="font-medium text-[var(--text-primary)] mb-2">
                    ✅ Agendamento Confirmado
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Enviado para o cliente quando o agendamento é confirmado
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="font-medium text-[var(--text-primary)] mb-2">
                    ❌ Agendamento Recusado
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Enviado para o cliente quando o agendamento é recusado
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="font-medium text-[var(--text-primary)] mb-2">
                    🚫 Agendamento Cancelado
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Enviado para o cliente quando o agendamento é cancelado
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="font-medium text-[var(--text-primary)] mb-2">
                    ⏰ Lembrete de Confirmação
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Enviado para proprietários sobre agendamentos pendentes
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <div className="font-medium text-[var(--text-primary)] mb-2">
                    🔔 Lembrete de Agendamento
                  </div>
                  <div className="text-sm text-[var(--text-secondary)]">
                    Enviado para clientes sobre agendamentos do dia seguinte
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Configuração necessária */}
          <Card className="mb-8">
            <CardHeader>
              <h2 className="text-xl font-semibold text-[var(--text-primary)]">
                ⚙️ Configuração Necessária
              </h2>
            </CardHeader>
            <CardContent>
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
                <h3 className="font-medium text-yellow-800 mb-2">
                  📝 Variáveis de Ambiente Necessárias:
                </h3>
                <div className="text-sm text-yellow-700 space-y-1 font-mono">
                  <div>RESEND_API_KEY=re_xxxxxxxxxxxxxxx</div>
                  <div>RESEND_FROM_EMAIL=<EMAIL></div>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h3 className="font-medium text-blue-800 mb-2">
                  🔗 Como Configurar o Resend:
                </h3>
                <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                  <li>Acesse <a href="https://resend.com" target="_blank" rel="noopener noreferrer" className="underline">resend.com</a> e crie uma conta</li>
                  <li>Adicione e verifique seu domínio</li>
                  <li>Gere uma API Key</li>
                  <li>Configure as variáveis de ambiente no arquivo .env.local</li>
                  <li>Reinicie o servidor de desenvolvimento</li>
                </ol>
              </div>
            </CardContent>
          </Card>

          {/* Componente de teste */}
          <TestNotifications 
            agendamentoId={1}
            clienteId={user?.id}
            proprietarioId={user?.id}
          />

          {/* Automação */}
          <Card className="mt-8">
            <CardHeader>
              <h2 className="text-xl font-semibold text-[var(--text-primary)]">
                🤖 Automação de Notificações
              </h2>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h3 className="font-medium text-green-800 mb-2">
                    ✅ Notificações Automáticas Implementadas:
                  </h3>
                  <ul className="text-sm text-green-700 space-y-1 list-disc list-inside">
                    <li>Novo agendamento → Email para cliente e proprietário</li>
                    <li>Confirmação de agendamento → Email para cliente</li>
                    <li>Recusa de agendamento → Email para cliente</li>
                    <li>Cancelamento de agendamento → Email para cliente</li>
                  </ul>
                </div>
                
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <h3 className="font-medium text-orange-800 mb-2">
                    🔄 Automação Futura (Cron Jobs):
                  </h3>
                  <ul className="text-sm text-orange-700 space-y-1 list-disc list-inside">
                    <li>Lembretes de confirmação (2h antes do prazo)</li>
                    <li>Lembretes de agendamento (1 dia antes)</li>
                    <li>Reenvio de notificações falhadas</li>
                    <li>Cancelamento automático de agendamentos expirados</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  );
}
