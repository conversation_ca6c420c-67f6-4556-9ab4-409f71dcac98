/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/empresas/buscar/route";
exports.ids = ["app/api/empresas/buscar/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Fbuscar%2Froute&page=%2Fapi%2Fempresas%2Fbuscar%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Fbuscar%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Fbuscar%2Froute&page=%2Fapi%2Fempresas%2Fbuscar%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Fbuscar%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Projetos_1_geremias_servicetech_src_app_api_empresas_buscar_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/empresas/buscar/route.ts */ \"(rsc)/./src/app/api/empresas/buscar/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/empresas/buscar/route\",\n        pathname: \"/api/empresas/buscar\",\n        filename: \"route\",\n        bundlePath: \"app/api/empresas/buscar/route\"\n    },\n    resolvedPagePath: \"D:\\\\Projetos\\\\1\\\\geremias\\\\servicetech\\\\src\\\\app\\\\api\\\\empresas\\\\buscar\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Projetos_1_geremias_servicetech_src_app_api_empresas_buscar_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Fbuscar%2Froute&page=%2Fapi%2Fempresas%2Fbuscar%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Fbuscar%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/empresas/buscar/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/empresas/buscar/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/supabase/server */ \"(rsc)/./src/utils/supabase/server.ts\");\n/* harmony import */ var _types_busca__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types/busca */ \"(rsc)/./src/types/busca.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        // Usar cliente administrativo para bypassa RLS e evitar recursão infinita\n        const supabase = (0,_utils_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createAdminClient)();\n        const { searchParams } = new URL(request.url);\n        // Extrair parâmetros de busca\n        const filtros = {\n            termo: searchParams.get('termo') || undefined,\n            cidade: searchParams.get('cidade') || undefined,\n            estado: searchParams.get('estado') || undefined,\n            bairro: searchParams.get('bairro') || undefined,\n            categorias_servicos: searchParams.get('categorias_servicos')?.split(',').filter(Boolean) || undefined,\n            preco_minimo: searchParams.get('preco_minimo') ? Number(searchParams.get('preco_minimo')) : undefined,\n            preco_maximo: searchParams.get('preco_maximo') ? Number(searchParams.get('preco_maximo')) : undefined,\n            ordenacao: searchParams.get('ordenacao') || 'relevancia',\n            pagina: Number(searchParams.get('pagina')) || 1,\n            limite: Math.min(Number(searchParams.get('limite')) || _types_busca__WEBPACK_IMPORTED_MODULE_2__.LIMITE_PADRAO, _types_busca__WEBPACK_IMPORTED_MODULE_2__.LIMITE_MAXIMO)\n        };\n        // Calcular offset para paginação\n        const offset = (filtros.pagina - 1) * filtros.limite;\n        // Construir query base com agregação de serviços\n        let query = supabase.from('empresas').select(`\n        empresa_id,\n        nome_empresa,\n        logo_url,\n        endereco,\n        numero,\n        bairro,\n        cidade,\n        estado,\n        descricao,\n        slug,\n        segmento,\n        servicos!inner (\n          servico_id,\n          nome_servico,\n          categoria,\n          preco\n        )\n      `).eq('status', 'ativo').eq('servicos.ativo', true);\n        // Aplicar filtros de texto\n        if (filtros.termo) {\n            query = query.or(`nome_empresa.ilike.%${filtros.termo}%,descricao.ilike.%${filtros.termo}%`);\n        }\n        // Aplicar filtros de localização\n        if (filtros.cidade) {\n            query = query.ilike('cidade', `%${filtros.cidade}%`);\n        }\n        if (filtros.estado) {\n            query = query.ilike('estado', `%${filtros.estado}%`);\n        }\n        if (filtros.bairro) {\n            query = query.ilike('bairro', `%${filtros.bairro}%`);\n        }\n        // Aplicar filtros de categoria de serviços\n        if (filtros.categorias_servicos && filtros.categorias_servicos.length > 0) {\n            query = query.in('servicos.categoria', filtros.categorias_servicos);\n        }\n        // Aplicar filtros de preço\n        if (filtros.preco_minimo !== undefined) {\n            query = query.gte('servicos.preco', filtros.preco_minimo);\n        }\n        if (filtros.preco_maximo !== undefined) {\n            query = query.lte('servicos.preco', filtros.preco_maximo);\n        }\n        // Executar query\n        const { data: empresasRaw, error } = await query;\n        if (error) {\n            console.error('Erro ao buscar empresas:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Erro ao buscar empresas'\n            }, {\n                status: 500\n            });\n        }\n        // Processar dados e agrupar por empresa\n        const empresasMap = new Map();\n        empresasRaw?.forEach((item)=>{\n            const empresaId = item.empresa_id;\n            if (!empresasMap.has(empresaId)) {\n                empresasMap.set(empresaId, {\n                    empresa_id: item.empresa_id,\n                    nome_empresa: item.nome_empresa,\n                    logo_url: item.logo_url,\n                    endereco: item.endereco,\n                    numero: item.numero,\n                    bairro: item.bairro,\n                    cidade: item.cidade,\n                    estado: item.estado,\n                    descricao: item.descricao,\n                    slug: item.slug,\n                    segmento: item.segmento,\n                    total_servicos: 0,\n                    preco_minimo: Infinity,\n                    preco_maximo: 0,\n                    categorias_servicos: [],\n                    endereco_completo: `${item.endereco}, ${item.numero} - ${item.bairro}, ${item.cidade}/${item.estado}`\n                });\n            }\n            const empresa = empresasMap.get(empresaId);\n            // Processar array de serviços\n            if (item.servicos && Array.isArray(item.servicos)) {\n                item.servicos.forEach((servico)=>{\n                    empresa.total_servicos++;\n                    // Verificar se o preço é válido antes de agregar\n                    if (servico.preco !== null && servico.preco !== undefined) {\n                        empresa.preco_minimo = Math.min(empresa.preco_minimo, servico.preco);\n                        empresa.preco_maximo = Math.max(empresa.preco_maximo, servico.preco);\n                    }\n                    // Verificar se a categoria é válida antes de agregar\n                    if (servico.categoria && !empresa.categorias_servicos.includes(servico.categoria)) {\n                        empresa.categorias_servicos.push(servico.categoria);\n                    }\n                });\n            }\n        });\n        // Converter para array e corrigir preços infinitos\n        let empresas = Array.from(empresasMap.values()).map((empresa)=>({\n                ...empresa,\n                preco_minimo: empresa.preco_minimo === Infinity ? 0 : empresa.preco_minimo\n            }));\n        // Aplicar ordenação\n        empresas = aplicarOrdenacao(empresas, filtros.ordenacao);\n        // Calcular total antes da paginação\n        const total = empresas.length;\n        // Aplicar paginação\n        empresas = empresas.slice(offset, offset + filtros.limite);\n        // Calcular metadados de paginação\n        const totalPaginas = Math.ceil(total / filtros.limite);\n        const temProximaPagina = filtros.pagina < totalPaginas;\n        const temPaginaAnterior = filtros.pagina > 1;\n        const resultado = {\n            empresas,\n            total,\n            pagina: filtros.pagina,\n            limite: filtros.limite,\n            total_paginas: totalPaginas,\n            tem_proxima_pagina: temProximaPagina,\n            tem_pagina_anterior: temPaginaAnterior\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: resultado\n        });\n    } catch (error) {\n        console.error('Erro geral na API de busca de empresas:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Erro interno do servidor'\n        }, {\n            status: 500\n        });\n    }\n}\n// Função auxiliar para aplicar ordenação\nfunction aplicarOrdenacao(empresas, ordenacao) {\n    switch(ordenacao){\n        case 'preco_menor':\n            return empresas.sort((a, b)=>a.preco_minimo - b.preco_minimo);\n        case 'preco_maior':\n            return empresas.sort((a, b)=>b.preco_maximo - a.preco_maximo);\n        case 'nome_az':\n            return empresas.sort((a, b)=>a.nome_empresa.localeCompare(b.nome_empresa));\n        case 'nome_za':\n            return empresas.sort((a, b)=>b.nome_empresa.localeCompare(a.nome_empresa));\n        case 'mais_servicos':\n            return empresas.sort((a, b)=>b.total_servicos - a.total_servicos);\n        case 'avaliacao':\n            // Para futuro - por enquanto ordena por nome\n            return empresas.sort((a, b)=>a.nome_empresa.localeCompare(b.nome_empresa));\n        case 'relevancia':\n        default:\n            // Ordenação por relevância: mais serviços + nome\n            return empresas.sort((a, b)=>{\n                if (b.total_servicos !== a.total_servicos) {\n                    return b.total_servicos - a.total_servicos;\n                }\n                return a.nome_empresa.localeCompare(b.nome_empresa);\n            });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/empresas/buscar/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/busca.ts":
/*!****************************!*\
  !*** ./src/types/busca.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ESTADOS_BRASIL: () => (/* binding */ ESTADOS_BRASIL),\n/* harmony export */   FAIXAS_PRECO: () => (/* binding */ FAIXAS_PRECO),\n/* harmony export */   LIMITE_MAXIMO: () => (/* binding */ LIMITE_MAXIMO),\n/* harmony export */   LIMITE_PADRAO: () => (/* binding */ LIMITE_PADRAO),\n/* harmony export */   OPCOES_ORDENACAO: () => (/* binding */ OPCOES_ORDENACAO)\n/* harmony export */ });\n// Tipos para o módulo de busca de empresas\n// Interface para filtros de busca\n// Constantes para filtros\nconst OPCOES_ORDENACAO = [\n    {\n        value: 'relevancia',\n        label: 'Mais Relevantes'\n    },\n    {\n        value: 'preco_menor',\n        label: 'Menor Preço'\n    },\n    {\n        value: 'preco_maior',\n        label: 'Maior Preço'\n    },\n    {\n        value: 'nome_az',\n        label: 'Nome A-Z'\n    },\n    {\n        value: 'nome_za',\n        label: 'Nome Z-A'\n    },\n    {\n        value: 'mais_servicos',\n        label: 'Mais Serviços'\n    },\n    {\n        value: 'avaliacao',\n        label: 'Melhor Avaliação'\n    }\n];\nconst LIMITE_PADRAO = 12;\nconst LIMITE_MAXIMO = 50;\n// Estados brasileiros para filtro\nconst ESTADOS_BRASIL = [\n    'AC',\n    'AL',\n    'AP',\n    'AM',\n    'BA',\n    'CE',\n    'DF',\n    'ES',\n    'GO',\n    'MA',\n    'MT',\n    'MS',\n    'MG',\n    'PA',\n    'PB',\n    'PR',\n    'PE',\n    'PI',\n    'RJ',\n    'RN',\n    'RS',\n    'RO',\n    'RR',\n    'SC',\n    'SP',\n    'SE',\n    'TO'\n];\n// Faixas de preço predefinidas para filtros rápidos\nconst FAIXAS_PRECO = [\n    {\n        label: 'Até R$ 50',\n        min: 0,\n        max: 50\n    },\n    {\n        label: 'R$ 50 - R$ 100',\n        min: 50,\n        max: 100\n    },\n    {\n        label: 'R$ 100 - R$ 200',\n        min: 100,\n        max: 200\n    },\n    {\n        label: 'R$ 200 - R$ 500',\n        min: 200,\n        max: 500\n    },\n    {\n        label: 'Acima de R$ 500',\n        min: 500,\n        max: null\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/busca.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/supabase/server.ts":
/*!**************************************!*\
  !*** ./src/utils/supabase/server.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAdminClient: () => (/* binding */ createAdminClient),\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n// Cliente administrativo para operações que requerem service role\nfunction createAdminClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://tlbpsdgoklkekoxzmzlo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        cookies: {\n            getAll () {\n                return [];\n            },\n            setAll () {\n            // No-op for admin client\n            }\n        },\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc3VwYWJhc2Uvc2VydmVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBa0Q7QUFDWjtBQUUvQixlQUFlRTtJQUNwQixNQUFNQyxjQUFjLE1BQU1GLHFEQUFPQTtJQUVqQyxPQUFPRCxpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLFFBQVFDLEdBQUcsQ0FBQ0UseUJBQXlCLEVBQ3JDO1FBQ0VOLFNBQVM7WUFDUE87Z0JBQ0UsT0FBT0wsWUFBWUssTUFBTTtZQUMzQjtZQUNBQyxRQUFPQyxZQUFZO2dCQUNqQixJQUFJO29CQUNGQSxhQUFhQyxPQUFPLENBQUMsQ0FBQyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFLEdBQzVDWCxZQUFZWSxHQUFHLENBQUNILE1BQU1DLE9BQU9DO2dCQUVqQyxFQUFFLE9BQU07Z0JBQ04sMERBQTBEO2dCQUMxRCx3REFBd0Q7Z0JBQ3hELGlCQUFpQjtnQkFDbkI7WUFDRjtRQUNGO0lBQ0Y7QUFFSjtBQUVBLGtFQUFrRTtBQUMzRCxTQUFTRTtJQUNkLE9BQU9oQixpRUFBa0JBLENBQ3ZCSSwwQ0FBb0MsRUFDcENBLFFBQVFDLEdBQUcsQ0FBQ0UseUJBQXlCLEVBQ3JDO1FBQ0VOLFNBQVM7WUFDUE87Z0JBQ0UsT0FBTyxFQUFFO1lBQ1g7WUFDQUM7WUFDRSx5QkFBeUI7WUFDM0I7UUFDRjtRQUNBUSxNQUFNO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7QUFFSiIsInNvdXJjZXMiOlsiRDpcXFByb2pldG9zXFwxXFxnZXJlbWlhc1xcc2VydmljZXRlY2hcXHNyY1xcdXRpbHNcXHN1cGFiYXNlXFxzZXJ2ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2VydmVyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3NzcidcclxuaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycydcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVDbGllbnQoKSB7XHJcbiAgY29uc3QgY29va2llU3RvcmUgPSBhd2FpdCBjb29raWVzKClcclxuXHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZISxcclxuICAgIHtcclxuICAgICAgY29va2llczoge1xyXG4gICAgICAgIGdldEFsbCgpIHtcclxuICAgICAgICAgIHJldHVybiBjb29raWVTdG9yZS5nZXRBbGwoKVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgc2V0QWxsKGNvb2tpZXNUb1NldCkge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29va2llc1RvU2V0LmZvckVhY2goKHsgbmFtZSwgdmFsdWUsIG9wdGlvbnMgfSkgPT5cclxuICAgICAgICAgICAgICBjb29raWVTdG9yZS5zZXQobmFtZSwgdmFsdWUsIG9wdGlvbnMpXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgIH0gY2F0Y2gge1xyXG4gICAgICAgICAgICAvLyBUaGUgYHNldEFsbGAgbWV0aG9kIHdhcyBjYWxsZWQgZnJvbSBhIFNlcnZlciBDb21wb25lbnQuXHJcbiAgICAgICAgICAgIC8vIFRoaXMgY2FuIGJlIGlnbm9yZWQgaWYgeW91IGhhdmUgbWlkZGxld2FyZSByZWZyZXNoaW5nXHJcbiAgICAgICAgICAgIC8vIHVzZXIgc2Vzc2lvbnMuXHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH1cclxuICApXHJcbn1cclxuXHJcbi8vIENsaWVudGUgYWRtaW5pc3RyYXRpdm8gcGFyYSBvcGVyYcOnw7VlcyBxdWUgcmVxdWVyZW0gc2VydmljZSByb2xlXHJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVBZG1pbkNsaWVudCgpIHtcclxuICByZXR1cm4gY3JlYXRlU2VydmVyQ2xpZW50KFxyXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcclxuICAgIHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkhLFxyXG4gICAge1xyXG4gICAgICBjb29raWVzOiB7XHJcbiAgICAgICAgZ2V0QWxsKCkge1xyXG4gICAgICAgICAgcmV0dXJuIFtdXHJcbiAgICAgICAgfSxcclxuICAgICAgICBzZXRBbGwoKSB7XHJcbiAgICAgICAgICAvLyBOby1vcCBmb3IgYWRtaW4gY2xpZW50XHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgICAgYXV0aDoge1xyXG4gICAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGZhbHNlLFxyXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlQ2xpZW50IiwiY29va2llU3RvcmUiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsImdldEFsbCIsInNldEFsbCIsImNvb2tpZXNUb1NldCIsImZvckVhY2giLCJuYW1lIiwidmFsdWUiLCJvcHRpb25zIiwic2V0IiwiY3JlYXRlQWRtaW5DbGllbnQiLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@supabase","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fempresas%2Fbuscar%2Froute&page=%2Fapi%2Fempresas%2Fbuscar%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fempresas%2Fbuscar%2Froute.ts&appDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjetos%5C1%5Cgeremias%5Cservicetech&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();