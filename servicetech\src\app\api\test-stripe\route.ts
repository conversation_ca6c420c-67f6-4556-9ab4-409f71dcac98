import { NextResponse } from 'next/server';
import Strip<PERSON> from 'stripe';

export async function GET() {
  console.log('🔍 Testando conexão com Stripe...');
  
  try {
    // Verificar se a chave existe
    if (!process.env.STRIPE_SECRET_KEY) {
      return NextResponse.json({
        status: 'error',
        message: 'STRIPE_SECRET_KEY não configurada'
      }, { status: 500 });
    }

    // Inicializar Stripe
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2025-05-28.basil',
    });

    // Fazer uma chamada simples para testar a conexão
    const account = await stripe.accounts.retrieve();
    
    console.log('✅ Conexão com Stripe bem-sucedida');
    
    return NextResponse.json({
      status: 'success',
      message: 'Conexão com Stripe estabelecida com sucesso!',
      account: {
        id: account.id,
        country: account.country,
        default_currency: account.default_currency,
        email: account.email
      },
      timestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ Erro ao conectar com Stripe:', error.message);
    
    return NextResponse.json({
      status: 'error',
      message: `Erro ao conectar com Stripe: ${error.message}`,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
