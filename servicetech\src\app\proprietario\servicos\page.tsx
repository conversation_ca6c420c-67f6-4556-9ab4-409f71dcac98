'use client';

import React, { useState, useEffect } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { useServicos } from '@/hooks/useServicos';
import Link from 'next/link';

export default function ServicosPage() {
  return (
    <ProtectedRoute requiredRole="Proprietario">
      <GerenciamentoServicos />
    </ProtectedRoute>
  );
}

function GerenciamentoServicos() {
  const { user } = useAuth();
  const {
    servicos,
    loading,
    error,
    estatisticas,
    buscarServicos,
    criarServico,
    atualizarServico,
    excluirServico,
    alternarStatusServico,
    limparError
  } = useServicos();

  const [mostrarFormulario, setMostrarFormulario] = useState(false);
  const [mensagem, setMensagem] = useState<{ tipo: 'success' | 'error'; texto: string } | null>(null);

  // Carregar serviços ao montar o componente
  useEffect(() => {
    buscarServicos();
  }, [buscarServicos]);

  // Limpar mensagens após 5 segundos
  useEffect(() => {
    if (mensagem) {
      const timer = setTimeout(() => setMensagem(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [mensagem]);

  // Limpar erro quando houver mudança
  useEffect(() => {
    if (error) {
      setMensagem({ tipo: 'error', texto: error });
      limparError();
    }
  }, [error, limparError]);

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-[var(--border-color)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-[var(--text-primary)]">
                Gerenciar Serviços
              </h1>
              <p className="text-[var(--text-secondary)]">
                Configure os serviços oferecidos pelo seu estabelecimento
              </p>
            </div>
            <div className="flex space-x-3">
              <Link href="/proprietario/dashboard">
                <Button variant="outline">
                  Voltar ao Dashboard
                </Button>
              </Link>
              {!mostrarFormulario && (
                <Button onClick={() => setMostrarFormulario(true)}>
                  Novo Serviço
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Mensagem de Feedback */}
        {mensagem && (
          <div className={`mb-6 p-4 rounded-md ${
            mensagem.tipo === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {mensagem.tipo === 'success' ? (
                  <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{mensagem.texto}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setMensagem(null)}
                  className="inline-flex text-gray-400 hover:text-gray-600"
                >
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Estatísticas */}
        {!mostrarFormulario && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Total de Serviços</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.total}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Serviços Ativos</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.ativos}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Serviços Inativos</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">{estatisticas.inativos}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-[var(--text-secondary)]">Categorias</p>
                    <p className="text-2xl font-bold text-[var(--text-primary)]">
                      {Object.keys(estatisticas.categorias).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Lista de Serviços Simplificada */}
        {!mostrarFormulario && (
          <Card>
            <CardHeader>
              <CardTitle>Serviços Cadastrados</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">
                  <p className="text-[var(--text-secondary)]">Carregando serviços...</p>
                </div>
              ) : servicos.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-[var(--text-secondary)]">
                    Nenhum serviço encontrado. Crie seu primeiro serviço!
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {servicos.map((servico) => (
                    <div key={servico.servico_id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-semibold text-[var(--text-primary)]">
                            {servico.nome_servico}
                          </h3>
                          <p className="text-[var(--text-secondary)] text-sm">
                            {servico.categoria} • {servico.duracao_minutos}min • R$ {servico.preco.toFixed(2)}
                          </p>
                          {servico.descricao && (
                            <p className="text-[var(--text-secondary)] text-sm mt-1">
                              {servico.descricao}
                            </p>
                          )}
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          servico.ativo 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {servico.ativo ? 'Ativo' : 'Inativo'}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Formulário Simplificado */}
        {mostrarFormulario && (
          <Card>
            <CardHeader>
              <CardTitle>Novo Serviço</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-[var(--text-secondary)] mb-4">
                  Formulário completo será implementado na próxima iteração
                </p>
                <Button onClick={() => setMostrarFormulario(false)} variant="outline">
                  Voltar à Lista
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
