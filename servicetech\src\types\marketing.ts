// Tipos para o módulo de marketing (Premium)

// ===== CUPONS =====

// Interface principal do cupom
export interface Cupom {
  cupom_id: number;
  empresa_id: number;
  codigo_cupom: string;
  nome_cupom: string;
  descricao: string;
  tipo_desconto: 'valor_fixo' | 'percentual';
  valor_desconto: number;
  valor_minimo_pedido?: number;
  limite_usos_total?: number;
  limite_usos_por_cliente: number;
  usos_realizados: number;
  data_inicio: string;
  data_fim: string;
  ativo: boolean;
  aplicavel_servicos?: number[];
  created_at: string;
  updated_at: string;
}

// Dados para criar/editar cupom
export interface CriarCupomData {
  codigo_cupom: string;
  nome_cupom: string;
  descricao: string;
  tipo_desconto: 'valor_fixo' | 'percentual';
  valor_desconto: number;
  valor_minimo_pedido?: number;
  limite_usos_total?: number;
  limite_usos_por_cliente?: number;
  data_inicio: string;
  data_fim: string;
  ativo?: boolean;
  aplicavel_servicos?: number[];
}

// Cupom com informações calculadas
export interface CupomCompleto extends Cupom {
  valido: boolean;
  motivo_invalido?: string;
  pode_usar: boolean;
  usos_restantes?: number;
  servicos_aplicaveis?: {
    servico_id: number;
    nome_servico: string;
  }[];
}

// Resultado da aplicação de cupom
export interface ResultadoAplicacaoCupom {
  cupom_aplicado: boolean;
  cupom?: Cupom;
  valor_original: number;
  valor_desconto: number;
  valor_final: number;
  economia: number;
  erro?: string;
}

// ===== CAMPANHAS =====

// Critérios de segmentação de clientes
export interface SegmentacaoCampanha {
  clientes_ativos?: boolean;
  clientes_inativos_dias?: number;
  min_agendamentos?: number;
  max_agendamentos?: number;
  servicos_utilizados?: number[];
  valor_gasto_min?: number;
  valor_gasto_max?: number;
  periodo_analise_dias?: number;
}

// Interface principal da campanha
export interface CampanhaMarketing {
  campanha_id: number;
  empresa_id: number;
  nome_campanha: string;
  descricao: string;
  tipo_campanha: 'email' | 'sms' | 'ambos';
  assunto_email?: string;
  conteudo_email?: string;
  conteudo_sms?: string;
  segmentacao: SegmentacaoCampanha;
  cupom_id?: number;
  status: 'rascunho' | 'agendada' | 'enviando' | 'enviada' | 'cancelada';
  data_agendamento?: string;
  data_envio?: string;
  total_destinatarios: number;
  total_enviados: number;
  total_erros: number;
  created_at: string;
  updated_at: string;
}

// Dados para criar/editar campanha
export interface CriarCampanhaData {
  nome_campanha: string;
  descricao: string;
  tipo_campanha: 'email' | 'sms' | 'ambos';
  assunto_email?: string;
  conteudo_email?: string;
  conteudo_sms?: string;
  segmentacao: SegmentacaoCampanha;
  cupom_id?: number;
  data_agendamento?: string;
}

// Destinatário da campanha
export interface DestinatarioCampanha {
  destinatario_id: number;
  campanha_id: number;
  cliente_user_id: string;
  email: string;
  telefone?: string;
  status_email: 'pendente' | 'enviado' | 'erro' | 'nao_aplicavel';
  status_sms: 'pendente' | 'enviado' | 'erro' | 'nao_aplicavel';
  erro_email?: string;
  erro_sms?: string;
  data_envio_email?: string;
  data_envio_sms?: string;
  data_abertura_email?: string;
  data_clique_email?: string;
  cupom_utilizado: boolean;
  created_at: string;
}

// Campanha com dados relacionados
export interface CampanhaCompleta extends CampanhaMarketing {
  cupom?: Cupom;
  destinatarios?: DestinatarioCampanha[];
  estatisticas?: EstatisticasCampanha;
}

// Estatísticas da campanha
export interface EstatisticasCampanha {
  taxa_entrega_email?: number;
  taxa_entrega_sms?: number;
  taxa_abertura_email?: number;
  taxa_clique_email?: number;
  taxa_utilizacao_cupom?: number;
  receita_gerada?: number;
  agendamentos_gerados?: number;
}

// ===== CLIENTES SEGMENTADOS =====

// Cliente elegível para campanha
export interface ClienteSegmentado {
  user_id: string;
  email: string;
  telefone?: string;
  nome?: string;
  total_agendamentos: number;
  valor_total_gasto: number;
  ultimo_agendamento?: string;
  dias_inativo?: number;
  servicos_utilizados: number[];
  elegivel_email: boolean;
  elegivel_sms: boolean;
}

// Resultado da segmentação
export interface ResultadoSegmentacao {
  total_clientes: number;
  clientes_elegiveis: ClienteSegmentado[];
  criterios_aplicados: SegmentacaoCampanha;
  estatisticas: {
    clientes_ativos: number;
    clientes_inativos: number;
    ticket_medio: number;
    servicos_mais_utilizados: {
      servico_id: number;
      nome_servico: string;
      total_usos: number;
    }[];
  };
}

// ===== FORMULÁRIOS =====

// Estado do formulário de cupom
export interface FormularioCupomState {
  dados: CriarCupomData;
  erros: Record<string, string>;
  carregando: boolean;
  preview?: CupomCompleto;
}

// Estado do formulário de campanha
export interface FormularioCampanhaState {
  dados: CriarCampanhaData;
  erros: Record<string, string>;
  carregando: boolean;
  preview_segmentacao?: ResultadoSegmentacao;
  cupons_disponiveis?: Cupom[];
}

// ===== FILTROS E BUSCA =====

// Filtros para cupons
export interface FiltrosCupons {
  busca?: string;
  status?: 'todos' | 'ativos' | 'inativos' | 'expirados';
  tipo_desconto?: 'todos' | 'valor_fixo' | 'percentual';
  data_inicio?: string;
  data_fim?: string;
}

// Filtros para campanhas
export interface FiltrosCampanhas {
  busca?: string;
  status?: 'todos' | 'rascunho' | 'agendada' | 'enviando' | 'enviada' | 'cancelada';
  tipo_campanha?: 'todos' | 'email' | 'sms' | 'ambos';
  data_inicio?: string;
  data_fim?: string;
}

// ===== RESPOSTAS DA API =====

// Resposta padrão da API
export interface RespostaAPI<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Resposta da API de cupons
export interface RespostaCupons extends RespostaAPI {
  data?: {
    cupons: Cupom[];
    total: number;
    pagina: number;
    total_paginas: number;
  };
}

// Resposta da API de campanhas
export interface RespostaCampanhas extends RespostaAPI {
  data?: {
    campanhas: CampanhaMarketing[];
    total: number;
    pagina: number;
    total_paginas: number;
  };
}

// Resposta da validação de cupom
export interface RespostaValidacaoCupom extends RespostaAPI {
  data?: ResultadoAplicacaoCupom;
}

// Resposta da segmentação
export interface RespostaSegmentacao extends RespostaAPI {
  data?: ResultadoSegmentacao;
}

// ===== CONSTANTES =====

// Status de cupom
export const STATUS_CUPOM = {
  ATIVO: 'ativo',
  INATIVO: 'inativo',
  EXPIRADO: 'expirado',
  ESGOTADO: 'esgotado'
} as const;

// Status de campanha
export const STATUS_CAMPANHA = {
  RASCUNHO: 'rascunho',
  AGENDADA: 'agendada',
  ENVIANDO: 'enviando',
  ENVIADA: 'enviada',
  CANCELADA: 'cancelada'
} as const;

// Tipos de campanha
export const TIPOS_CAMPANHA = {
  EMAIL: 'email',
  SMS: 'sms',
  AMBOS: 'ambos'
} as const;

// Tipos de desconto
export const TIPOS_DESCONTO = {
  VALOR_FIXO: 'valor_fixo',
  PERCENTUAL: 'percentual'
} as const;
